# SaaS Ideas - TypeScript Types

## User Types
```typescript
// src/types/user.ts

export interface User {
  id: string;
  email: string;
  name: string | null;
  avatarUrl: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
}
```

## Project Types
```typescript
// src/types/project.ts

export interface Project {
  id: string;
  userId: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  analysisData: AnalysisData | null;
  overallScore: number | null;
}

export interface ProjectWithFeatures extends Project {
  features: Feature[];
}

export interface ProjectWithUserFlow extends Project {
  userFlow: UserFlow | null;
}

export interface ProjectWithAnalysis extends Project {
  analysis: AnalysisData;
}

export interface ProjectStats {
  totalProjects: number;
  completedProjects: number;
  inProgressProjects: number;
  averageScore: number;
}
```

## Analysis Types
```typescript
// src/types/analysis.ts

export interface AnalysisData {
  pillars: PillarScore[];
  overallScore: number;
  improvements: Improvement[];
  coreFeatures: CoreFeature[];
  techStack: TechStack;
  pricingModel: PricingModel;
}

export interface PillarScore {
  name: PillarName;
  score: number; // 0-10
  description: string;
  feedback: string;
}

export type PillarName = 
  | 'uniqueness' 
  | 'stickiness' 
  | 'growthPotential' 
  | 'pricingModel' 
  | 'upsellPotential' 
  | 'customerPurchasingPower';

export interface Improvement {
  id: string;
  pillar: PillarName;
  suggestion: string;
  impact: 'low' | 'medium' | 'high';
}

export interface CoreFeature {
  id: string;
  name: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  complexity: 'low' | 'medium' | 'high';
  estimatedTime: string; // e.g. "2 days"
}

export interface TechStack {
  frontend: string[];
  backend: string[];
  database: string[];
  hosting: string[];
  other: string[];
}

export interface PricingModel {
  type: 'free' | 'freemium' | 'subscription' | 'one-time' | 'usage-based' | 'hybrid';
  tiers: PricingTier[];
  recommendation: string;
}

export interface PricingTier {
  name: string;
  price: number;
  billingCycle: 'monthly' | 'yearly' | 'one-time';
  features: string[];
}
```

## Feature Types
```typescript
// src/types/feature.ts

export interface Feature {
  id: string;
  projectId: string;
  name: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'backlog' | 'todo' | 'in_progress' | 'review' | 'done';
  createdAt: string;
  updatedAt: string;
  assignee?: string | null;
  dueDate?: string | null;
  tags?: string[];
}

export interface FeatureStats {
  total: number;
  backlog: number;
  todo: number;
  inProgress: number;
  review: number;
  done: number;
  completionPercentage: number;
}
```

## User Flow Types
```typescript
// src/types/userFlow.ts

export interface UserFlow {
  id: string;
  projectId: string;
  flowData: FlowData;
  createdAt: string;
  updatedAt: string;
  version: number;
}

export interface FlowData {
  nodes: FlowNode[];
  edges: FlowEdge[];
}

export interface FlowNode {
  id: string;
  type: 'page' | 'action' | 'decision' | 'api';
  position: {
    x: number;
    y: number;
  };
  data: {
    label: string;
    description?: string;
    features?: string[];
  };
}

export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  type?: 'default' | 'success' | 'error' | 'conditional';
}
```

## Memory Bank / Cursor AI Types
```typescript
// src/types/cursor.ts

export interface CursorIntegration {
  projectId: string;
  connected: boolean;
  lastSynced: string | null;
}

export interface CursorTask {
  id: string;
  featureId: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
  result?: string;
}
```

## API Response Types
```typescript
// src/types/api.ts

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface AnalysisRequest {
  projectId: string;
  name: string;
  description: string;
}

export interface FeatureRequest {
  name: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'backlog' | 'todo' | 'in_progress' | 'review' | 'done';
  assignee?: string;
  dueDate?: string;
  tags?: string[];
}

export interface UserFlowRequest {
  flowData: FlowData;
  version?: number;
}
``` 