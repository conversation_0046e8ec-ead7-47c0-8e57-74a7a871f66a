# SaaS Ideas - AI Analysis Implementation

## Overview

The AI analysis component is a core feature of the SaaS Ideas application. It analyzes SaaS project ideas based on six core pillars and provides valuable insights, recommendations, and visualizations to help users refine their ideas and build successful applications.

## Core Pillars

1. **Uniqueness**: How different is the idea from existing solutions?
2. **Stickiness**: Will users keep coming back to the product?
3. **Growth Potential**: Can the product scale and grow its user base?
4. **Pricing Model**: Is the pricing strategy viable and competitive?
5. **Upsell Potential**: Are there opportunities for additional revenue streams?
6. **Customer Purchasing Power**: Can the target audience afford the product?

## Implementation

### AI Service Integration

```typescript
// src/lib/ai/analysis.ts

import { AnalysisData, CoreFeature, Improvement, PillarScore, PricingModel, TechStack } from '@/types/analysis';
import { Project } from '@/types/project';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function analyzeProject(project: Pick<Project, 'name' | 'description'>): Promise<AnalysisData> {
  try {
    const prompt = generateAnalysisPrompt(project);
    
    const response = await openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [
        {
          role: 'system',
          content: `You are an expert SaaS business analyst and technical architect. 
          Analyze the provided SaaS idea based on the six core pillars: uniqueness, stickiness, growth potential, 
          pricing model, upsell potential, and customer purchasing power. 
          Provide scores, feedback, improvement suggestions, core features, tech stack recommendations, and pricing model suggestions.
          Respond with a structured JSON object following the specified format.`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      response_format: { type: 'json_object' }
    });

    const analysisData = JSON.parse(response.choices[0].message.content || '{}') as AnalysisData;
    
    // Process and validate the analysis data
    return processAnalysisData(analysisData);
  } catch (error) {
    console.error('Error analyzing project:', error);
    throw new Error('Failed to analyze project');
  }
}

function generateAnalysisPrompt(project: Pick<Project, 'name' | 'description'>): string {
  return `
    Please analyze the following SaaS idea:
    
    Name: ${project.name}
    Description: ${project.description}
    
    Provide a comprehensive analysis based on the six core pillars:
    1. Uniqueness: How different is this idea from existing solutions?
    2. Stickiness: Will users keep coming back to the product?
    3. Growth Potential: Can the product scale and grow its user base?
    4. Pricing Model: Is the pricing strategy viable and competitive?
    5. Upsell Potential: Are there opportunities for additional revenue streams?
    6. Customer Purchasing Power: Can the target audience afford the product?
    
    For each pillar, provide:
    - A score from 0 to 10
    - A brief description
    - Specific feedback
    
    Also provide:
    - At least 3 improvement suggestions for each pillar with low scores (below 7)
    - 5-10 core features the app should have, with priority and complexity ratings
    - A recommended tech stack (frontend, backend, database, hosting, other)
    - A suggested pricing model with tiers
    
    Return the analysis in the following JSON format:
    {
      "pillars": [
        {
          "name": "uniqueness",
          "score": 0-10,
          "description": "Brief description",
          "feedback": "Detailed feedback"
        },
        ...
      ],
      "overallScore": 0-10,
      "improvements": [
        {
          "id": "unique-id",
          "pillar": "pillarName",
          "suggestion": "Improvement suggestion",
          "impact": "low|medium|high"
        },
        ...
      ],
      "coreFeatures": [
        {
          "id": "unique-id",
          "name": "Feature name",
          "description": "Feature description",
          "priority": "low|medium|high",
          "complexity": "low|medium|high",
          "estimatedTime": "time estimate"
        },
        ...
      ],
      "techStack": {
        "frontend": ["tech1", "tech2", ...],
        "backend": ["tech1", "tech2", ...],
        "database": ["tech1", "tech2", ...],
        "hosting": ["tech1", "tech2", ...],
        "other": ["tech1", "tech2", ...]
      },
      "pricingModel": {
        "type": "free|freemium|subscription|one-time|usage-based|hybrid",
        "tiers": [
          {
            "name": "Tier name",
            "price": price,
            "billingCycle": "monthly|yearly|one-time",
            "features": ["feature1", "feature2", ...]
          },
          ...
        ],
        "recommendation": "Pricing recommendation"
      }
    }
  `;
}

function processAnalysisData(data: any): AnalysisData {
  // Calculate overall score as average of pillar scores if not provided
  if (!data.overallScore) {
    const totalScore = data.pillars.reduce((sum: number, pillar: PillarScore) => sum + pillar.score, 0);
    data.overallScore = Math.round((totalScore / data.pillars.length) * 10) / 10;
  }
  
  // Ensure all required fields are present
  return {
    pillars: data.pillars || [],
    overallScore: data.overallScore || 0,
    improvements: data.improvements || [],
    coreFeatures: data.coreFeatures || [],
    techStack: data.techStack || {
      frontend: [],
      backend: [],
      database: [],
      hosting: [],
      other: []
    },
    pricingModel: data.pricingModel || {
      type: 'subscription',
      tiers: [],
      recommendation: 'No pricing recommendation available'
    }
  };
}
```

### API Route Implementation

```typescript
// src/app/api/analysis/route.ts

import { analyzeProject } from '@/lib/ai/analysis';
import { supabase } from '@/lib/supabase/client';
import { AnalysisRequest } from '@/types/api';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { projectId, name, description }: AnalysisRequest = await request.json();
    
    // Validate input
    if (!projectId || !name || !description) {
      return NextResponse.json(
        { error: 'Project ID, name, and description are required' },
        { status: 400 }
      );
    }
    
    // Analyze the project
    const analysisData = await analyzeProject({ name, description });
    
    // Save analysis results to database
    const { data, error } = await supabase
      .from('projects')
      .update({
        analysis_data: analysisData,
        overall_score: analysisData.overallScore,
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId);
    
    if (error) {
      console.error('Error saving analysis:', error);
      return NextResponse.json(
        { error: 'Failed to save analysis results' },
        { status: 500 }
      );
    }
    
    // Generate initial features based on core features
    for (const feature of analysisData.coreFeatures) {
      await supabase.from('features').insert({
        project_id: projectId,
        name: feature.name,
        description: feature.description,
        priority: feature.priority,
        status: 'backlog'
      });
    }
    
    return NextResponse.json({ data: analysisData });
  } catch (error) {
    console.error('Analysis error:', error);
    return NextResponse.json(
      { error: 'Failed to analyze project' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const projectId = url.searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }
    
    const { data, error } = await supabase
      .from('projects')
      .select('analysis_data, overall_score')
      .eq('id', projectId)
      .single();
    
    if (error) {
      console.error('Error fetching analysis:', error);
      return NextResponse.json(
        { error: 'Failed to fetch analysis results' },
        { status: 500 }
      );
    }
    
    if (!data || !data.analysis_data) {
      return NextResponse.json(
        { error: 'No analysis data found for this project' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ 
      data: {
        analysisData: data.analysis_data,
        overallScore: data.overall_score
      } 
    });
  } catch (error) {
    console.error('Error fetching analysis:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analysis results' },
      { status: 500 }
    );
  }
}
```

### React Component for Analysis Visualization

```typescript
// src/components/analysis/AnalysisCard.tsx

'use client';

import { AnalysisData, PillarName, PillarScore } from '@/types/analysis';
import { useEffect, useState } from 'react';
import RadarChart from './RadarChart';
import PillarScoreCard from './PillarScore';
import TechStackDisplay from './TechStackDisplay';
import PricingModelDisplay from './PricingModelDisplay';
import ImprovementsList from './ImprovementsList';
import CoreFeaturesList from './CoreFeaturesList';

interface AnalysisCardProps {
  projectId: string;
  initialData?: AnalysisData;
}

export default function AnalysisCard({ projectId, initialData }: AnalysisCardProps) {
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(initialData || null);
  const [isLoading, setIsLoading] = useState(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'improvements' | 'features' | 'techStack' | 'pricing'>('overview');

  useEffect(() => {
    if (!initialData) {
      fetchAnalysisData();
    }
  }, [projectId, initialData]);

  async function fetchAnalysisData() {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/analysis?projectId=${projectId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch analysis data');
      }
      
      const result = await response.json();
      setAnalysisData(result.data.analysisData);
    } catch (err) {
      setError('Failed to load analysis data');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }

  if (isLoading) {
    return <div className="p-6 bg-white rounded-lg shadow-md">Loading analysis...</div>;
  }

  if (error || !analysisData) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-md">
        <h3 className="text-lg font-semibold text-red-600">Error: {error || 'No analysis data available'}</h3>
        <button 
          onClick={fetchAnalysisData}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Header with overall score */}
      <div className="p-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold">SaaS Idea Analysis</h2>
            <p className="text-sm opacity-80">Based on the six core pillars</p>
          </div>
          <div className="text-center">
            <div className="text-5xl font-bold">{analysisData.overallScore.toFixed(1)}</div>
            <div className="text-sm">Overall Score</div>
          </div>
        </div>
      </div>
      
      {/* Tab navigation */}
      <div className="border-b">
        <nav className="flex">
          {['overview', 'improvements', 'features', 'techStack', 'pricing'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as any)}
              className={`px-4 py-3 font-medium text-sm ${
                activeTab === tab
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </nav>
      </div>
      
      {/* Tab content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Pillar Scores</h3>
                <div className="space-y-4">
                  {analysisData.pillars.map((pillar) => (
                    <PillarScoreCard key={pillar.name} pillar={pillar} />
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">Score Visualization</h3>
                <div className="h-64">
                  <RadarChart pillars={analysisData.pillars} />
                </div>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'improvements' && (
          <div>
            <h3 className="text-lg font-semibold mb-4">Improvement Suggestions</h3>
            <ImprovementsList improvements={analysisData.improvements} />
          </div>
        )}
        
        {activeTab === 'features' && (
          <div>
            <h3 className="text-lg font-semibold mb-4">Core Features</h3>
            <CoreFeaturesList features={analysisData.coreFeatures} />
          </div>
        )}
        
        {activeTab === 'techStack' && (
          <div>
            <h3 className="text-lg font-semibold mb-4">Recommended Tech Stack</h3>
            <TechStackDisplay techStack={analysisData.techStack} />
          </div>
        )}
        
        {activeTab === 'pricing' && (
          <div>
            <h3 className="text-lg font-semibold mb-4">Suggested Pricing Model</h3>
            <PricingModelDisplay pricingModel={analysisData.pricingModel} />
          </div>
        )}
      </div>
    </div>
  );
}
```

## Radar Chart for Visualization

```typescript
// src/components/analysis/RadarChart.tsx

'use client';

import { PillarScore } from '@/types/analysis';
import { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

interface RadarChartProps {
  pillars: PillarScore[];
}

export default function RadarChart({ pillars }: RadarChartProps) {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;
    
    // Destroy previous chart instance if it exists
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }
    
    // Format data for the radar chart
    const labels = pillars.map(pillar => formatPillarName(pillar.name));
    const scores = pillars.map(pillar => pillar.score);
    
    // Create new chart
    const ctx = chartRef.current.getContext('2d');
    if (ctx) {
      chartInstance.current = new Chart(ctx, {
        type: 'radar',
        data: {
          labels,
          datasets: [
            {
              label: 'Pillar Scores',
              data: scores,
              backgroundColor: 'rgba(99, 102, 241, 0.2)',
              borderColor: 'rgba(99, 102, 241, 1)',
              pointBackgroundColor: 'rgba(99, 102, 241, 1)',
              pointBorderColor: '#fff',
              pointHoverBackgroundColor: '#fff',
              pointHoverBorderColor: 'rgba(99, 102, 241, 1)'
            }
          ]
        },
        options: {
          scales: {
            r: {
              angleLines: {
                display: true
              },
              suggestedMin: 0,
              suggestedMax: 10
            }
          }
        }
      });
    }
    
    // Cleanup function
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [pillars]);

  // Helper function to format pillar names for display
  function formatPillarName(name: string): string {
    return name
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase());
  }

  return <canvas ref={chartRef} />;
}
```

## Integration with Cursor AI

```typescript
// src/lib/ai/cursor.ts

import { CursorIntegration, CursorTask } from '@/types/cursor';
import { Feature } from '@/types/feature';
import { supabase } from '@/lib/supabase/client';

export async function connectToCursor(projectId: string): Promise<CursorIntegration> {
  try {
    // Check if integration already exists
    const { data: existingIntegration, error: fetchError } = await supabase
      .from('cursor_integrations')
      .select('*')
      .eq('project_id', projectId)
      .single();
    
    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows found"
      throw fetchError;
    }
    
    if (existingIntegration) {
      // Update existing integration
      const { data, error } = await supabase
        .from('cursor_integrations')
        .update({
          connected: true,
          last_synced: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', existingIntegration.id)
        .select()
        .single();
      
      if (error) throw error;
      return data as CursorIntegration;
    } else {
      // Create new integration
      const { data, error } = await supabase
        .from('cursor_integrations')
        .insert({
          project_id: projectId,
          connected: true,
          last_synced: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) throw error;
      return data as CursorIntegration;
    }
  } catch (error) {
    console.error('Error connecting to Cursor:', error);
    throw new Error('Failed to connect to Cursor AI');
  }
}

export async function createCursorTask(featureId: string, description: string): Promise<CursorTask> {
  try {
    const { data, error } = await supabase
      .from('cursor_tasks')
      .insert({
        feature_id: featureId,
        description,
        status: 'pending'
      })
      .select()
      .single();
    
    if (error) throw error;
    return data as CursorTask;
  } catch (error) {
    console.error('Error creating Cursor task:', error);
    throw new Error('Failed to create Cursor task');
  }
}

export async function updateTaskStatus(taskId: string, status: 'pending' | 'in_progress' | 'completed' | 'failed', result?: string): Promise<CursorTask> {
  try {
    const { data, error } = await supabase
      .from('cursor_tasks')
      .update({
        status,
        result,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId)
      .select()
      .single();
    
    if (error) throw error;
    
    // If task is completed, update the feature status as well
    if (status === 'completed') {
      const { data: taskData } = await supabase
        .from('cursor_tasks')
        .select('feature_id')
        .eq('id', taskId)
        .single();
      
      if (taskData?.feature_id) {
        await supabase
          .from('features')
          .update({
            status: 'done',
            updated_at: new Date().toISOString()
          })
          .eq('id', taskData.feature_id);
      }
    }
    
    return data as CursorTask;
  } catch (error) {
    console.error('Error updating task status:', error);
    throw new Error('Failed to update task status');
  }
}

export async function getProjectContext(projectId: string): Promise<string> {
  try {
    // Fetch project details
    const { data: project } = await supabase
      .from('projects')
      .select('name, description, analysis_data')
      .eq('id', projectId)
      .single();
    
    // Fetch features
    const { data: features } = await supabase
      .from('features')
      .select('*')
      .eq('project_id', projectId);
    
    // Fetch user flow
    const { data: userFlow } = await supabase
      .from('user_flows')
      .select('flow_data')
      .eq('project_id', projectId)
      .single();
    
    // Compile context
    const context = {
      project: {
        name: project?.name,
        description: project?.description,
        analysis: project?.analysis_data
      },
      features: features || [],
      userFlow: userFlow?.flow_data || null
    };
    
    return JSON.stringify(context);
  } catch (error) {
    console.error('Error getting project context:', error);
    throw new Error('Failed to get project context');
  }
}
```

## User Interface for Analysis

The analysis results are presented in a user-friendly interface with the following components:

1. **Overall Score Card**: Displays the overall feasibility score of the SaaS idea.
2. **Pillar Score Cards**: Individual cards for each pillar showing the score, description, and feedback.
3. **Radar Chart**: Visual representation of scores across all six pillars.
4. **Improvement Suggestions**: List of suggestions to improve the idea, grouped by pillar.
5. **Core Features**: List of recommended features with priority and complexity indicators.
6. **Tech Stack Recommendations**: Visual display of recommended technologies for frontend, backend, database, etc.
7. **Pricing Model**: Visualization of the suggested pricing tiers and strategy.

## Analysis Process Flow

1. User enters project name and description
2. System sends data to AI service for analysis
3. AI analyzes the idea based on the six core pillars
4. Analysis results are stored in the database
5. Core features are automatically added to the project's feature list
6. User reviews analysis and can make adjustments
7. User can generate user flow diagrams and Kanban tickets based on the analysis

## Performance Considerations

- Analysis requests are cached to avoid unnecessary API calls
- Large language model responses are validated and processed before storage
- Radar chart rendering is optimized for performance
- Analysis data is structured for efficient database storage and retrieval 