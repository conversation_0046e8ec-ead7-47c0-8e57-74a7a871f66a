-- Migration to add "created_by" fields to track who added each user
-- Run this in your Supabase SQL Editor

-- Add the new columns to the users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS created_by_email TEXT,
ADD COLUMN IF NOT EXISTS created_by_name TEXT;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_users_created_by ON public.users(created_by);

-- Add comment to document the new fields
COMMENT ON COLUMN public.users.created_by IS 'ID of the admin who created this user account';
COMMENT ON COLUMN public.users.created_by_email IS 'Email of the admin who created this user account';
COMMENT ON COLUMN public.users.created_by_name IS 'Name of the admin who created this user account';

-- Success message
SELECT 'Created by fields added successfully!' as message;
