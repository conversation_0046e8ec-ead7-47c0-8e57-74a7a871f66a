const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  try {
    console.log('Applying user flows table migration...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '..', 'lib', 'services', 'create-user-flows-table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error('Error applying migration:', error);
      process.exit(1);
    }
    
    console.log('Migration applied successfully!');
    console.log('user_flows table created with the following structure:');
    console.log('- id (UUID, primary key)');
    console.log('- project_name (TEXT, unique)');
    console.log('- flow_data (JSONB)');
    console.log('- created_at (TIMESTAMP)');
    console.log('- updated_at (TIMESTAMP)');
    
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Alternative method using direct SQL execution
async function applyMigrationDirect() {
  try {
    console.log('Applying user flows table migration...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '..', 'lib', 'services', 'create-user-flows-table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Split SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log('Executing:', statement.trim().substring(0, 50) + '...');
        const { error } = await supabase.from('_').select('*').limit(0); // This won't work, we need a different approach
      }
    }
    
    console.log('Migration completed!');
    
  } catch (error) {
    console.error('Error:', error);
    console.log('\nPlease run the following SQL manually in your Supabase SQL editor:');
    console.log('\n' + '='.repeat(50));
    
    const sqlPath = path.join(__dirname, '..', 'lib', 'services', 'create-user-flows-table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    console.log(sql);
    console.log('='.repeat(50));
  }
}

// Run the migration
applyMigrationDirect();