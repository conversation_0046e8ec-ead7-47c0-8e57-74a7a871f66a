-- Add columns for storing generated tasks and explanations to the projects table
-- Run this SQL in your Supabase SQL Editor

-- Add the new columns
ALTER TABLE public.projects 
ADD COLUMN IF NOT EXISTS generated_tasks JSONB,
ADD COLUMN IF NOT EXISTS tasks_explanation TEXT,
ADD COLUMN IF NOT EXISTS tasks_generated_at TIMESTAMPTZ;

-- Create index for faster queries on tasks_generated_at
CREATE INDEX IF NOT EXISTS projects_tasks_generated_at_idx ON public.projects (tasks_generated_at);

-- Add comments to document the new columns
COMMENT ON COLUMN public.projects.generated_tasks IS 'AI-generated tasks for the project stored as JSON';
COMMENT ON COLUMN public.projects.tasks_explanation IS 'AI-generated explanation about the tasks';
COMMENT ON COLUMN public.projects.tasks_generated_at IS 'Timestamp when tasks were generated';

-- Verify the columns were added (optional - for testing)
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'projects' 
-- AND column_name IN ('generated_tasks', 'tasks_explanation', 'tasks_generated_at');
