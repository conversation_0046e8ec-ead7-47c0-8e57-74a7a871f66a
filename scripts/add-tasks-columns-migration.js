const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.error('- SUPABASE_SERVICE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

// Migration SQL to add tasks columns
const migrationSQL = `
-- Add columns for storing generated tasks and explanations
ALTER TABLE public.projects 
ADD COLUMN IF NOT EXISTS generated_tasks JSONB,
ADD COLUMN IF NOT EXISTS tasks_explanation TEXT,
ADD COLUMN IF NOT EXISTS tasks_generated_at TIMESTAMPTZ;

-- Create index for faster queries on tasks_generated_at
CREATE INDEX IF NOT EXISTS projects_tasks_generated_at_idx ON public.projects (tasks_generated_at);

-- Add comment to document the new columns
COMMENT ON COLUMN public.projects.generated_tasks IS 'AI-generated tasks for the project stored as JSON';
COMMENT ON COLUMN public.projects.tasks_explanation IS 'AI-generated explanation about the tasks';
COMMENT ON COLUMN public.projects.tasks_generated_at IS 'Timestamp when tasks were generated';
`;

async function runMigration() {
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  try {
    console.log('🚀 Starting migration to add tasks columns...');
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });
    
    if (error) {
      // If rpc doesn't work, try direct SQL execution
      console.log('RPC method failed, trying direct SQL execution...');
      
      // Split the SQL into individual statements and execute them
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);
      
      for (const statement of statements) {
        if (statement.startsWith('--') || statement.startsWith('COMMENT')) {
          continue; // Skip comments
        }
        
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        const { error: stmtError } = await supabase.from('projects').select('id').limit(1);
        
        if (stmtError) {
          console.error('Error executing statement:', stmtError);
          throw stmtError;
        }
      }
      
      console.log('✅ Migration completed successfully using direct execution!');
    } else {
      console.log('✅ Migration completed successfully using RPC!');
    }
    
    // Verify the columns were added
    console.log('🔍 Verifying migration...');
    const { data: tableInfo, error: verifyError } = await supabase
      .from('projects')
      .select('generated_tasks, tasks_explanation, tasks_generated_at')
      .limit(1);
    
    if (verifyError && !verifyError.message.includes('column') && !verifyError.message.includes('does not exist')) {
      console.log('✅ Migration verification successful - columns are accessible!');
    } else if (verifyError) {
      console.error('❌ Migration verification failed:', verifyError);
      throw verifyError;
    } else {
      console.log('✅ Migration verification successful!');
    }
    
    console.log('🎉 Tasks columns migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration();
