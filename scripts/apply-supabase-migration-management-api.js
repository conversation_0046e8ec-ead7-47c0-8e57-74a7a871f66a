// Script to apply Supabase migrations using the Supabase Management API
require('dotenv').config();
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const projectId = supabaseUrl ? supabaseUrl.split('.')[0].replace('https://', '') : '';

if (!supabaseUrl || !supabaseKey || !projectId) {
  console.error('Missing Supabase credentials in environment variables');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

// Read the migration SQL
const migrationPath = path.join(__dirname, '..', 'lib', 'services', 'supabase-migration.sql');
const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

async function applyMigration() {
  try {
    console.log('Applying Supabase migration...');
    
    // Get the database connection string
    const connectionResponse = await fetch(`https://api.supabase.com/v1/projects/${projectId}/connection-string`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${supabaseKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!connectionResponse.ok) {
      const error = await connectionResponse.text();
      throw new Error(`Failed to get connection string: ${error}`);
    }
    
    const connectionData = await connectionResponse.json();
    const connectionString = connectionData.connection_string;
    
    if (!connectionString) {
      throw new Error('No connection string returned');
    }
    
    // Execute the SQL using the Supabase Management API
    const response = await fetch(`https://api.supabase.com/v1/projects/${projectId}/sql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: migrationSQL
      })
    });
    
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to apply migration: ${error}`);
    }
    
    console.log('Migration applied successfully!');
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

applyMigration(); 