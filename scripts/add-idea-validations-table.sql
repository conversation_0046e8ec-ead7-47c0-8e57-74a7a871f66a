-- Create idea_validations table for storing AI idea validation results
-- Run this SQL in your Supabase SQL Editor

-- Create the idea_validations table
CREATE TABLE IF NOT EXISTS public.idea_validations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  idea_title TEXT NOT NULL,
  idea_description TEXT NOT NULL,
  target_market TEXT NOT NULL,
  business_model TEXT NOT NULL,
  technical_complexity TEXT DEFAULT 'medium',
  budget INTEGER,
  timeline TEXT,
  -- Store the input hash for caching
  input_hash TEXT NOT NULL UNIQUE,
  -- Store the validation result
  validation_result JSONB NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- <PERSON>reate indexes for faster queries
CREATE INDEX IF NOT EXISTS idea_validations_user_id_idx ON public.idea_validations (user_id);
CREATE INDEX IF NOT EXISTS idea_validations_input_hash_idx ON public.idea_validations (input_hash);
CREATE INDEX IF NOT EXISTS idea_validations_created_at_idx ON public.idea_validations (created_at);
CREATE INDEX IF NOT EXISTS idea_validations_idea_title_idx ON public.idea_validations (idea_title);

-- Add RLS policies
ALTER TABLE public.idea_validations ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own validations
CREATE POLICY "Users can view own idea validations" ON public.idea_validations
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own validations
CREATE POLICY "Users can insert own idea validations" ON public.idea_validations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own validations
CREATE POLICY "Users can update own idea validations" ON public.idea_validations
  FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own validations
CREATE POLICY "Users can delete own idea validations" ON public.idea_validations
  FOR DELETE USING (auth.uid() = user_id);
