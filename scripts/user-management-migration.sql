-- User Management Migration
-- This script adds user management features to the existing database

-- First, let's add the new columns to the existing users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'pending', 'inactive')),
ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS company TEXT,
ADD COLUMN IF NOT EXISTS job_title TEXT,
ADD COLUMN IF NOT EXISTS bio TEXT,
ADD COLUMN IF NOT EXISTS location TEXT,
ADD COLUMN IF NOT EXISTS website TEXT,
ADD COLUMN IF NOT EXISTS social_links JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Create user_activity_logs table
CREATE TABLE IF NOT EXISTS public.user_activity_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  resource_type TEXT,
  resource_id UUID,
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_sessions table
CREATE TABLE IF NOT EXISTS public.user_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  session_token TEXT UNIQUE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON public.users(status);
CREATE INDEX IF NOT EXISTS idx_users_role ON public.users(role);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON public.users(last_login);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON public.user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_action ON public.user_activity_logs(action);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_created_at ON public.user_activity_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON public.user_sessions(expires_at);

-- Create or replace function to update user activity logs
CREATE OR REPLACE FUNCTION public.update_user_activity_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.created_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create or replace function to update user session activity
CREATE OR REPLACE FUNCTION public.update_user_session_activity()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_activity = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for activity logs
DROP TRIGGER IF EXISTS update_user_activity_logs_timestamp ON public.user_activity_logs;
CREATE TRIGGER update_user_activity_logs_timestamp
BEFORE INSERT ON public.user_activity_logs
FOR EACH ROW EXECUTE PROCEDURE public.update_user_activity_timestamp();

-- Create triggers for session activity
DROP TRIGGER IF EXISTS update_user_session_activity_trigger ON public.user_sessions;
CREATE TRIGGER update_user_session_activity_trigger
BEFORE UPDATE ON public.user_sessions
FOR EACH ROW EXECUTE PROCEDURE public.update_user_session_activity();

-- Enable Row Level Security for new tables
ALTER TABLE public.user_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies for user_activity_logs
DROP POLICY IF EXISTS user_activity_logs_select ON public.user_activity_logs;
CREATE POLICY user_activity_logs_select ON public.user_activity_logs
  FOR SELECT USING (
    auth.uid() = user_id OR 
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'moderator')
    )
  );

DROP POLICY IF EXISTS user_activity_logs_insert ON public.user_activity_logs;
CREATE POLICY user_activity_logs_insert ON public.user_activity_logs
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR 
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'moderator')
    )
  );

-- Create policies for user_sessions
DROP POLICY IF EXISTS user_sessions_select ON public.user_sessions;
CREATE POLICY user_sessions_select ON public.user_sessions
  FOR SELECT USING (
    auth.uid() = user_id OR 
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

DROP POLICY IF EXISTS user_sessions_insert ON public.user_sessions;
CREATE POLICY user_sessions_insert ON public.user_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS user_sessions_update ON public.user_sessions;
CREATE POLICY user_sessions_update ON public.user_sessions
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS user_sessions_delete ON public.user_sessions;
CREATE POLICY user_sessions_delete ON public.user_sessions
  FOR DELETE USING (auth.uid() = user_id);

-- Update existing users to have default values
UPDATE public.users 
SET 
  status = COALESCE(status, 'active'),
  role = COALESCE(role, 'user'),
  email_verified = COALESCE(email_verified, FALSE),
  social_links = COALESCE(social_links, '{}'),
  preferences = COALESCE(preferences, '{}'),
  metadata = COALESCE(metadata, '{}')
WHERE status IS NULL OR role IS NULL OR email_verified IS NULL 
   OR social_links IS NULL OR preferences IS NULL OR metadata IS NULL;

-- Create a view for user statistics
CREATE OR REPLACE VIEW public.user_management_stats AS
SELECT 
  COUNT(*) AS total_users,
  COUNT(CASE WHEN status = 'active' THEN 1 END) AS active_users,
  COUNT(CASE WHEN status = 'suspended' THEN 1 END) AS suspended_users,
  COUNT(CASE WHEN status = 'pending' THEN 1 END) AS pending_users,
  COUNT(CASE WHEN status = 'inactive' THEN 1 END) AS inactive_users,
  COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) AS new_users_today,
  COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) AS new_users_this_week,
  COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) AS new_users_this_month,
  COUNT(CASE WHEN role = 'admin' THEN 1 END) AS admin_users,
  COUNT(CASE WHEN role = 'moderator' THEN 1 END) AS moderator_users,
  COUNT(CASE WHEN role = 'user' THEN 1 END) AS regular_users
FROM public.users;

-- Grant necessary permissions
GRANT SELECT ON public.user_management_stats TO authenticated;
GRANT SELECT, INSERT ON public.user_activity_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_sessions TO authenticated;

-- Create function to log user activity (can be called from application)
CREATE OR REPLACE FUNCTION public.log_user_activity(
  p_user_id UUID,
  p_action TEXT,
  p_resource_type TEXT DEFAULT NULL,
  p_resource_id UUID DEFAULT NULL,
  p_details JSONB DEFAULT '{}',
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO public.user_activity_logs (
    user_id,
    action,
    resource_type,
    resource_id,
    details,
    ip_address,
    user_agent
  ) VALUES (
    p_user_id,
    p_action,
    p_resource_type,
    p_resource_id,
    p_details,
    p_ip_address,
    p_user_agent
  ) RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.log_user_activity TO authenticated;

-- Create function to clean up expired sessions
CREATE OR REPLACE FUNCTION public.cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.user_sessions 
  WHERE expires_at < NOW();
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the cleanup function
GRANT EXECUTE ON FUNCTION public.cleanup_expired_sessions TO authenticated;

-- Insert initial activity log for existing users (optional)
INSERT INTO public.user_activity_logs (user_id, action, details)
SELECT 
  id,
  'account_created',
  jsonb_build_object('migration', true, 'created_at', created_at)
FROM public.users
WHERE NOT EXISTS (
  SELECT 1 FROM public.user_activity_logs 
  WHERE user_activity_logs.user_id = users.id 
  AND action = 'account_created'
);

-- Create a function to get user statistics (for admin dashboard)
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS TABLE (
  total_users BIGINT,
  active_users BIGINT,
  suspended_users BIGINT,
  pending_users BIGINT,
  inactive_users BIGINT,
  new_users_today BIGINT,
  new_users_this_week BIGINT,
  new_users_this_month BIGINT,
  admin_users BIGINT,
  moderator_users BIGINT,
  regular_users BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM public.user_management_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_user_statistics TO authenticated;

-- Add comment to document the migration
COMMENT ON TABLE public.user_activity_logs IS 'Stores user activity logs for audit and analytics purposes';
COMMENT ON TABLE public.user_sessions IS 'Tracks user sessions for security and analytics';
COMMENT ON VIEW public.user_management_stats IS 'Provides aggregated user statistics for admin dashboard';

-- Migration completed successfully
SELECT 'User management migration completed successfully!' AS status;
