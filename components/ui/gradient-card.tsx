import * as React from "react"
import { cn } from "@/lib/utils"

const GradientCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    variant?: "default" | "premium" | "feature" | "statistic" | "blue" | "purple" | "emerald" | "slate" | "glass"
  }
>(({ className, variant = "default", ...props }, ref) => {
  const variants = {
    default: "bg-gradient-to-br from-slate-900/80 via-slate-800/50 to-slate-900/80 backdrop-blur-xl border border-slate-700/60 hover:border-slate-600/80",
    premium: "bg-gradient-to-br from-blue-500/10 via-purple-500/5 to-transparent backdrop-blur-xl border border-blue-500/30 hover:border-blue-400/50",
    feature: "bg-gradient-to-br from-slate-900/80 via-slate-800/50 to-slate-900/80 backdrop-blur-xl border border-slate-700/60 hover:border-slate-600/80",
    statistic: "bg-gradient-to-br from-emerald-500/10 via-blue-500/5 to-transparent backdrop-blur-xl border border-emerald-500/30 hover:border-emerald-400/50",
    blue: "bg-gradient-to-br from-blue-500/10 via-blue-600/5 to-transparent backdrop-blur-xl border border-blue-500/30 hover:border-blue-400/50",
    purple: "bg-gradient-to-br from-purple-500/10 via-purple-600/5 to-transparent backdrop-blur-xl border border-purple-500/30 hover:border-purple-400/50",
    emerald: "bg-gradient-to-br from-emerald-500/10 via-emerald-600/5 to-transparent backdrop-blur-xl border border-emerald-500/30 hover:border-emerald-400/50",
    slate: "bg-gradient-to-br from-slate-800/80 via-slate-700/50 to-slate-800/80 backdrop-blur-xl border border-slate-600/60 hover:border-slate-500/80",
    glass: "bg-gradient-to-br from-white/5 via-white/2 to-transparent backdrop-blur-xl border border-white/20 hover:border-white/30",
  }

  return (
    <div
      ref={ref}
      className={cn(
        "rounded-xl shadow-2xl transition-all duration-300 hover:shadow-3xl hover:scale-[1.02]",
        variants[variant],
        className,
      )}
      {...props}
    />
  )
})
GradientCard.displayName = "GradientCard"

const GradientCardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("flex flex-col space-y-1.5 p-6", className)} {...props} />
  ),
)
GradientCardHeader.displayName = "GradientCardHeader"

const GradientCardTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h3 ref={ref} className={cn("font-semibold leading-none tracking-tight text-white", className)} {...props} />
  ),
)
GradientCardTitle.displayName = "GradientCardTitle"

const GradientCardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => <p ref={ref} className={cn("text-sm text-slate-300", className)} {...props} />,
)
GradientCardDescription.displayName = "GradientCardDescription"

const GradientCardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />,
)
GradientCardContent.displayName = "GradientCardContent"

const GradientCardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("flex items-center p-6 pt-0", className)} {...props} />
  ),
)
GradientCardFooter.displayName = "GradientCardFooter"

export {
  GradientCard,
  GradientCardHeader,
  GradientCardFooter,
  GradientCardTitle,
  GradientCardDescription,
  GradientCardContent,
}
