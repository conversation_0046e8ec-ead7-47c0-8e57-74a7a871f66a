"use client"

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface StaggeredTextProps {
  text: string;
  className?: string;
  delay?: number;
  duration?: number;
  staggerDelay?: number;
  animationType?: 'fadeUp' | 'fadeIn' | 'slideLeft' | 'scale';
}

export default function StaggeredText({
  text,
  className = '',
  delay = 0,
  duration = 0.6,
  staggerDelay = 0.05,
  animationType = 'fadeUp'
}: StaggeredTextProps) {
  const words = text.split(' ');

  const getAnimationVariants = () => {
    switch (animationType) {
      case 'fadeUp':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0 }
        };
      case 'fadeIn':
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1 }
        };
      case 'slideLeft':
        return {
          hidden: { opacity: 0, x: 30 },
          visible: { opacity: 1, x: 0 }
        };
      case 'scale':
        return {
          hidden: { opacity: 0, scale: 0.8 },
          visible: { opacity: 1, scale: 1 }
        };
      default:
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0 }
        };
    }
  };

  const variants = getAnimationVariants();

  return (
    <motion.div
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      transition={{ staggerChildren: staggerDelay, delayChildren: delay }}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          variants={variants}
          transition={{
            duration,
            ease: [0.25, 0.1, 0.25, 1],
            type: "tween"
          }}
          className="inline-block mr-2"
        >
          {word}
        </motion.span>
      ))}
    </motion.div>
  );
}

// Character-level staggered animation
interface StaggeredCharsProps {
  text: string;
  className?: string;
  delay?: number;
  duration?: number;
  staggerDelay?: number;
}

export function StaggeredChars({
  text,
  className = '',
  delay = 0,
  duration = 0.4,
  staggerDelay = 0.02
}: StaggeredCharsProps) {
  const chars = text.split('');

  return (
    <motion.div
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      transition={{ staggerChildren: staggerDelay, delayChildren: delay }}
    >
      {chars.map((char, index) => (
        <motion.span
          key={index}
          variants={{
            hidden: { opacity: 0, y: 20, rotateX: -90 },
            visible: { opacity: 1, y: 0, rotateX: 0 }
          }}
          transition={{
            duration,
            ease: [0.25, 0.1, 0.25, 1],
            type: "tween"
          }}
          className="inline-block"
          style={{ transformOrigin: 'center bottom' }}
        >
          {char === ' ' ? '\u00A0' : char}
        </motion.span>
      ))}
    </motion.div>
  );
}
