'use client';

import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { useRef, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  speed?: number;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right';
  scale?: boolean;
  rotate?: boolean;
}

export default function ParallaxSection({ 
  children, 
  speed = 0.5, 
  className = '',
  direction = 'up',
  scale = false,
  rotate = false
}: Props) {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const getTransformValues = () => {
    const baseSpeed = speed * 100;
    switch (direction) {
      case 'up':
        return useTransform(scrollYProgress, [0, 1], [baseSpeed, 0]);
      case 'down':
        return useTransform(scrollYProgress, [0, 1], [-baseSpeed, 0]);
      case 'left':
        return useTransform(scrollYProgress, [0, 1], [-baseSpeed, 0]);
      case 'right':
        return useTransform(scrollYProgress, [0, 1], [baseSpeed, 0]);
      default:
        return useTransform(scrollYProgress, [0, 1], [baseSpeed, 0]);
    }
  };

  const transformValue = getTransformValues();
  const springConfig = { stiffness: 100, damping: 30, restDelta: 0.001 };
  
  const y = direction === 'up' || direction === 'down' 
    ? useSpring(transformValue, springConfig)
    : 0;
  
  const x = direction === 'left' || direction === 'right'
    ? useSpring(transformValue, springConfig)
    : 0;

  const scaleTransform = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    scale ? [0.8, 1, 0.8] : [1, 1, 1]
  );

  const rotation = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    rotate ? [-5, 0, 5] : [0, 0, 0]
  );

  const opacity = useTransform(
    scrollYProgress,
    [0, 0.2, 0.8, 1],
    [0.3, 1, 1, 0.3]
  );

  return (
    <motion.div
      ref={ref}
      style={{ 
        x,
        y,
        scale: scaleTransform,
        rotate: rotation,
        opacity
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
} 