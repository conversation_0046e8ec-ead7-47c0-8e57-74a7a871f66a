import React from 'react';

export default function TechyNetworkBG({ className = '' }: { className?: string }) {
  // 12x7 grid of points
  const cols = 12;
  const rows = 7;
  const width = 1200;
  const height = 500;
  const nodeRadius = 3;
  const nodeColor = '#7c3aed';
  const lineColor1 = '#06b6d4';
  const lineColor2 = '#6366f1';

  // Generate points
  const points = [];
  for (let y = 0; y < rows; y++) {
    for (let x = 0; x < cols; x++) {
      points.push({
        x: (width / (cols - 1)) * x + (Math.random() - 0.5) * 8,
        y: (height / (rows - 1)) * y + (Math.random() - 0.5) * 8,
      });
    }
  }

  // Generate lines between neighbors
  const lines = [];
  for (let y = 0; y < rows; y++) {
    for (let x = 0; x < cols; x++) {
      const idx = y * cols + x;
      // Right neighbor
      if (x < cols - 1) lines.push([idx, idx + 1]);
      // Down neighbor
      if (y < rows - 1) lines.push([idx, idx + cols]);
    }
  }

  return (
    <svg
      className={className}
      width="100%"
      height="100%"
      viewBox={`0 0 ${width} ${height}`}
      style={{ opacity: 0.22, filter: 'blur(0.5px)' }}
    >
      <defs>
        <linearGradient id="networkLine" x1="0" y1="0" x2="1" y2="1">
          <stop offset="0%" stopColor={lineColor1} />
          <stop offset="100%" stopColor={lineColor2} />
        </linearGradient>
        <radialGradient id="nodeGlow" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stopColor="#fff" stopOpacity="0.9" />
          <stop offset="100%" stopColor={nodeColor} stopOpacity="0.2" />
        </radialGradient>
      </defs>
      {/* Animate lines with CSS keyframes */}
      <g className="animate-pulse-slow">
        {lines.map(([a, b], i) => (
          <line
            key={i}
            x1={points[a].x}
            y1={points[a].y}
            x2={points[b].x}
            y2={points[b].y}
            stroke="url(#networkLine)"
            strokeWidth="2"
            opacity="0.7"
          />
        ))}
      </g>
      {/* Animate nodes with CSS keyframes */}
      <g className="animate-pulse-slow">
        {points.map((pt, i) => (
          <circle
            key={i}
            cx={pt.x}
            cy={pt.y}
            r={nodeRadius}
            fill="url(#nodeGlow)"
            stroke={nodeColor}
            strokeWidth="1.2"
            opacity="0.9"
          />
        ))}
      </g>
      <style>{`
        .animate-pulse-slow {
          animation: pulse-slow 3.5s ease-in-out infinite alternate;
        }
        @keyframes pulse-slow {
          0% { opacity: 0.18; }
          100% { opacity: 0.32; }
        }
      `}</style>
    </svg>
  );
} 