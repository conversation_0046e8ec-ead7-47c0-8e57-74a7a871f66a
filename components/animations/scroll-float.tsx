"use client"

import type React from "react"
import { useRef, useEffect } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"

// Make sure to register ScrollTrigger with GSAP
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger)
}

interface ScrollFloatProps {
  children: React.ReactNode
  direction?: "up" | "down" | "left" | "right"
  distance?: number
  delay?: number
  duration?: number
  ease?: string
  className?: string
  once?: boolean
  threshold?: number
}

const ScrollFloat: React.FC<ScrollFloatProps> = ({
  children,
  direction = "up",
  distance = 50,
  delay = 0,
  duration = 0.6,
  ease = "power2.out",
  className = "",
  once = true,
  threshold = 0.05,
}) => {
  const elementRef = useRef<HTMLDivElement>(null)
  const triggerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const element = elementRef.current
    const trigger = triggerRef.current

    if (!element || !trigger) return

    // Set initial state based on direction
    const initialProps: gsap.TweenVars = {
      autoAlpha: 0,
    }

    switch (direction) {
      case "up":
        initialProps.y = distance
        break
      case "down":
        initialProps.y = -distance
        break
      case "left":
        initialProps.x = distance
        break
      case "right":
        initialProps.x = -distance
        break
    }

    gsap.set(element, initialProps)

    // Create animation
    const animation = gsap.to(element, {
      autoAlpha: 1,
      x: 0,
      y: 0,
      duration,
      delay,
      ease,
      paused: true,
    })

    // Create scroll trigger
    const scrollTrigger = ScrollTrigger.create({
      trigger: trigger,
      start: `top bottom-=${threshold * 100}%`,
      onEnter: () => animation.play(),
      onLeaveBack: () => !once && animation.reverse(),
      markers: false,
    })

    return () => {
      animation.kill()
      scrollTrigger.kill()
    }
  }, [direction, distance, delay, duration, ease, once, threshold])

  return (
    <div ref={triggerRef} className="scroll-float-trigger">
      <div ref={elementRef} className={`scroll-float ${className}`}>
        {children}
      </div>
    </div>
  )
}

export default ScrollFloat
