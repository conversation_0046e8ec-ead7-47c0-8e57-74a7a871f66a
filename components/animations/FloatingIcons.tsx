'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, Code } from 'lucide-react';
import { useEffect, useState } from 'react';

// Use safe area for icon positions (never <10% or >90%)
const icons = [
  {
    Icon: Rocket,
    initialPosition: { left: '12%', top: '16%' },
    color: 'rgba(96, 165, 250, 0.7)',
    anim: 'float1'
  },
  {
    Icon: BarChart,
    initialPosition: { left: '78%', top: '22%' },
    color: 'rgba(167, 139, 250, 0.7)',
    anim: 'float2'
  },
  {
    Icon: Code,
    initialPosition: { left: '55%', top: '75%' },
    color: 'rgba(139, 92, 246, 0.7)',
    anim: 'float3'
  }
];

function usePrefersReducedMotionOrMobile() {
  const [shouldReduce, setShouldReduce] = useState(false);
  useEffect(() => {
    const mq = window.matchMedia('(prefers-reduced-motion: reduce)');
    const check = () => setShouldReduce(mq.matches || window.innerWidth < 640);
    check();
    mq.addEventListener('change', check);
    window.addEventListener('resize', check);
    return () => {
      mq.removeEventListener('change', check);
      window.removeEventListener('resize', check);
    };
  }, []);
  return shouldReduce;
}

export default function FloatingIcons({ className = '' }: { className?: string }) {
  const shouldReduce = usePrefersReducedMotionOrMobile();
  if (shouldReduce) return null;

  return (
    <div className={`absolute inset-0 pointer-events-none overflow-visible ${className}`} style={{zIndex: 1}}>
      <style>{`
        @keyframes float1 {
          0% { transform: translateY(0) scale(1); }
          50% { transform: translateY(-18px) scale(1.05); }
          100% { transform: translateY(0) scale(1); }
        }
        @keyframes float2 {
          0% { transform: translateY(0) scale(1); }
          50% { transform: translateY(14px) scale(0.97); }
          100% { transform: translateY(0) scale(1); }
        }
        @keyframes float3 {
          0% { transform: translateY(0) scale(1); }
          50% { transform: translateY(-10px) scale(1.03); }
          100% { transform: translateY(0) scale(1); }
        }
      `}</style>
      {icons.map(({ Icon, initialPosition, color, anim }, index) => (
        <div
          key={index}
          className="absolute"
          style={{
            ...initialPosition,
            animation: `${anim} 3.5s ease-in-out ${index * 0.7}s infinite`,
            willChange: 'transform, opacity',
            filter: `drop-shadow(0 0 16px ${color})`,
            zIndex: 1,
          }}
        >
          <div 
            className="p-2 rounded-xl backdrop-blur-sm"
            style={{
              background: `linear-gradient(135deg, ${color}, transparent)`
            }}
          >
            <Icon 
              size={22} 
              className="text-white/90"
            />
          </div>
        </div>
      ))}
    </div>
  );
} 