"use client"

import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef, ReactNode } from 'react';

interface SmoothTransitionProps {
  children: ReactNode;
  className?: string;
  offset?: [string, string];
}

export default function SmoothTransition({
  children,
  className = '',
  offset = ["start end", "end start"]
}: SmoothTransitionProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.95, 1, 1, 0.95]);

  return (
    <motion.div
      ref={ref}
      style={{ y, opacity, scale }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Section divider with animated gradient
export function SectionDivider({ 
  gradient = "from-blue-500/20 via-purple-500/20 to-emerald-500/20",
  className = ""
}: {
  gradient?: string;
  className?: string;
}) {
  return (
    <motion.div
      className={`relative h-px w-full ${className}`}
      initial={{ scaleX: 0 }}
      whileInView={{ scaleX: 1 }}
      transition={{ duration: 1, ease: "easeInOut" }}
    >
      <motion.div
        className={`absolute inset-0 bg-gradient-to-r ${gradient}`}
        animate={{
          opacity: [0.3, 0.8, 0.3],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </motion.div>
  );
}

// Floating action button with magnetic effect
export function FloatingActionButton({
  onClick,
  icon,
  label,
  position = "bottom-right"
}: {
  onClick: () => void;
  icon: ReactNode;
  label: string;
  position?: "bottom-right" | "bottom-left";
}) {
  const positionClasses = {
    "bottom-right": "bottom-8 right-8",
    "bottom-left": "bottom-8 left-8"
  };

  return (
    <motion.button
      onClick={onClick}
      className={`fixed ${positionClasses[position]} z-50 group`}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      initial={{ opacity: 0, scale: 0 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay: 2, type: "spring", stiffness: 300, damping: 20 }}
    >
      <div className="relative">
        {/* Main button */}
        <div className="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
          {icon}
        </div>
        
        {/* Tooltip */}
        <motion.div
          className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-slate-900 text-white px-3 py-1 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          initial={{ y: 10, opacity: 0 }}
          whileHover={{ y: 0, opacity: 1 }}
        >
          {label}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-900" />
        </motion.div>

        {/* Ripple effect */}
        <motion.div
          className="absolute inset-0 rounded-full bg-blue-500/30"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 0, 0.5],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>
    </motion.button>
  );
}
