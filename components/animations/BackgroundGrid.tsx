'use client';

import { useEffect, useRef, useState } from 'react';
import { motion, useSpring, useScroll, useTransform } from 'framer-motion';

interface Point {
  x: number;
  y: number;
  value: number;
  color: string;
}

function usePrefersReducedMotionOrMobile() {
  const [shouldReduce, setShouldReduce] = useState(false);
  useEffect(() => {
    const mq = window.matchMedia('(prefers-reduced-motion: reduce)');
    const check = () => setShouldReduce(mq.matches || window.innerWidth < 640);
    check();
    mq.addEventListener('change', check);
    window.addEventListener('resize', check);
    return () => {
      mq.removeEventListener('change', check);
      window.removeEventListener('resize', check);
    };
  }, []);
  return shouldReduce;
}

export default function BackgroundGrid() {
  const shouldReduce = usePrefersReducedMotionOrMobile();
  if (shouldReduce) return null;

  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const pointsRef = useRef<Point[]>([]);
  const { scrollYProgress } = useScroll();
  const [isVisible, setIsVisible] = useState(true);

  // Reduce number of points for performance
  const gridSize = 60; // was 40
  const maxLines = 2; // max lines per point

  // Throttle animation
  const frameRate = 30; // 30 FPS
  let lastFrame = 0;

  const gridOpacity = useTransform(scrollYProgress, [0, 0.5], [0.12, 0.04]);
  const gridScale = useSpring(useTransform(scrollYProgress, [0, 1], [1, 1.05]));

  useEffect(() => {
    if (!canvasRef.current || !containerRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const { width, height } = containerRef.current!.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;
      canvas.width = width * dpr;
      canvas.height = height * dpr;
      canvas.style.width = `${width}px`;
      canvas.style.height = `${height}px`;
      ctx.setTransform(1, 0, 0, 1, 0, 0); // reset transform
      ctx.scale(dpr, dpr);
    };

    // Initialize grid points
    const initPoints = () => {
      const points: Point[] = [];
      const numColumns = Math.ceil(canvas.width / gridSize);
      const numRows = Math.ceil(canvas.height / gridSize);

      for (let i = 0; i < numColumns; i++) {
        for (let j = 0; j < numRows; j++) {
          const value = Math.random();
          const color = value > 0.7 ? 'rgba(96, 165, 250, 0.3)' : // blue
                       value > 0.4 ? 'rgba(167, 139, 250, 0.3)' : // purple
                       'rgba(139, 92, 246, 0.3)'; // violet

          points.push({
            x: i * gridSize,
            y: j * gridSize,
            value,
            color
          });
        }
      }
      pointsRef.current = points;
    };

    const drawGrid = () => {
      if (!ctx) return;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      pointsRef.current.forEach((point, idx) => {
        const size = 2 + point.value * 2.5;
        ctx.beginPath();
        ctx.arc(point.x, point.y, size, 0, Math.PI * 2);
        ctx.fillStyle = point.color;
        ctx.fill();

        // Draw up to maxLines connecting lines to nearby points
        let linesDrawn = 0;
        for (let j = 0; j < pointsRef.current.length && linesDrawn < maxLines; j++) {
          if (j === idx) continue;
          const otherPoint = pointsRef.current[j];
          const dx = otherPoint.x - point.x;
          const dy = otherPoint.y - point.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          if (distance < 60) {
            ctx.beginPath();
            ctx.moveTo(point.x, point.y);
            ctx.lineTo(otherPoint.x, otherPoint.y);
            ctx.strokeStyle = `rgba(147, 197, 253, ${0.08 * (1 - distance / 60)})`;
            ctx.lineWidth = 0.4;
            ctx.stroke();
            linesDrawn++;
          }
        }
      });
    };

    // Intersection Observer to animate only when visible
    let observer: IntersectionObserver | null = null;
    if (containerRef.current) {
      observer = new window.IntersectionObserver(
        ([entry]) => setIsVisible(entry.isIntersecting),
        { threshold: 0.01 }
      );
      observer.observe(containerRef.current);
    }

    const animate = (now: number) => {
      if (!isVisible) return requestAnimationFrame(animate);
      if (now - lastFrame < 1000 / frameRate) {
        requestAnimationFrame(animate);
        return;
      }
      lastFrame = now;
      pointsRef.current = pointsRef.current.map(point => ({
        ...point,
        value: Math.max(0, Math.min(1, point.value + (Math.random() - 0.5) * 0.01)),
      }));
      drawGrid();
      requestAnimationFrame(animate);
    };

    resizeCanvas();
    initPoints();
    requestAnimationFrame(animate);

    window.addEventListener('resize', resizeCanvas);
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (observer && containerRef.current) observer.disconnect();
    };
  }, [isVisible]);

  return (
    <motion.div 
      ref={containerRef}
      className="fixed inset-0 pointer-events-none"
      style={{ opacity: gridOpacity, scale: gridScale, willChange: 'opacity, transform' }}
    >
      <canvas
        ref={canvasRef}
        className="absolute inset-0"
        style={{ 
          mixBlendMode: 'screen',
          filter: 'blur(1px)',
          willChange: 'opacity, transform'
        }}
      />
    </motion.div>
  );
} 