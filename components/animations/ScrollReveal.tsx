'use client';

import { motion, useInView, useScroll, useTransform } from 'framer-motion';
import { useRef, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  width?: "fit-content" | "100%";
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  scale?: boolean;
  rotate?: boolean;
  className?: string;
}

export default function ScrollReveal({ 
  children, 
  width = "fit-content",
  delay = 0,
  direction = 'up',
  scale = false,
  rotate = false,
  className = ''
}: Props) {
  const ref = useRef(null);
  const isInView = useInView(ref, { 
    once: true, 
    margin: "-10% 0px" 
  });

  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const opacity = useTransform(
    scrollYProgress,
    [0, 0.2, 0.8, 1],
    [0, 1, 1, 0]
  );

  const getDirectionAnimation = () => {
    const distance = 100;
    switch (direction) {
      case 'up':
        return { y: [distance, 0] };
      case 'down':
        return { y: [-distance, 0] };
      case 'left':
        return { x: [-distance, 0] };
      case 'right':
        return { x: [distance, 0] };
      default:
        return { y: [distance, 0] };
    }
  };

  return (
    <div ref={ref} style={{ width }} className={className}>
      <motion.div
        style={{
          opacity: opacity,
        }}
        variants={{
          hidden: { 
            opacity: 0,
            scale: scale ? 0.9 : 1,
            rotate: rotate ? -3 : 0,
            filter: 'blur(10px)',
            ...getDirectionAnimation() 
          },
          visible: { 
            opacity: 1,
            scale: 1,
            rotate: 0,
            filter: 'blur(0px)',
            x: 0,
            y: 0
          }
        }}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        transition={{
          duration: 1,
          delay: delay,
          ease: [0.25, 0.1, 0.25, 1],
          opacity: { duration: 0.5 },
          scale: { duration: 0.8 },
          rotate: { duration: 0.6 },
          filter: { duration: 0.8 }
        }}
      >
        {children}
      </motion.div>
    </div>
  );
} 