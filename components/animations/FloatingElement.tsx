'use client';

import { motion, useAnimation, useInView } from 'framer-motion';
import { ReactNode, useEffect, useRef } from 'react';

interface Props {
  children: ReactNode;
  duration?: number;
  delay?: number;
  className?: string;
  intensity?: 'light' | 'medium' | 'heavy';
  hover?: boolean;
}

export default function FloatingElement({ 
  children, 
  duration = 4, 
  delay = 0,
  className = '',
  intensity = 'medium',
  hover = true
}: Props) {
  const controls = useAnimation();
  const ref = useRef(null);
  const isInView = useInView(ref);

  const getIntensityValues = () => {
    switch (intensity) {
      case 'light':
        return { y: 10, rotate: 1 };
      case 'heavy':
        return { y: 30, rotate: 3 };
      default:
        return { y: 20, rotate: 2 };
    }
  };

  const { y, rotate } = getIntensityValues();

  useEffect(() => {
    if (isInView) {
      controls.start("animate");
    }
  }, [isInView, controls]);

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="initial"
      animate={controls}
      whileHover={hover ? "hover" : undefined}
      variants={{
        initial: {
          y: 0,
          rotate: 0,
          scale: 1,
        },
        animate: {
          y: [-y, 0, y],
          rotate: [-rotate, 0, rotate],
          transition: {
            duration: duration,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
            delay: delay,
          }
        },
        hover: {
          scale: 1.05,
          transition: {
            duration: 0.3,
            ease: "easeOut"
          }
        }
      }}
    >
      {children}
    </motion.div>
  );
} 