"use client"

import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef, ReactNode } from 'react';

interface MorphingGradientProps {
  children: ReactNode;
  className?: string;
  gradients?: string[];
  speed?: number;
}

export default function MorphingGradient({
  children,
  className = '',
  gradients = [
    'from-blue-500/10 via-purple-500/10 to-emerald-500/10',
    'from-purple-500/10 via-emerald-500/10 to-blue-500/10',
    'from-emerald-500/10 via-blue-500/10 to-purple-500/10'
  ],
  speed = 1
}: MorphingGradientProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  // Transform scroll progress to gradient index
  const gradientIndex = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    [0, 1, 2]
  );

  // Create smooth transitions between gradients
  const backgroundGradient = useTransform(
    gradientIndex,
    [0, 1, 2],
    gradients
  );

  return (
    <motion.div
      ref={ref}
      className={`relative ${className}`}
    >
      {/* Morphing background */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br transition-all duration-1000 ease-out"
        style={{
          background: `linear-gradient(135deg, ${backgroundGradient})`
        }}
      />
      
      {/* Animated overlay patterns */}
      <motion.div
        className="absolute inset-0 opacity-30"
        animate={{
          background: [
            'radial-gradient(circle at 20% 50%, rgba(59,130,246,0.1) 0%, transparent 50%)',
            'radial-gradient(circle at 80% 50%, rgba(168,85,247,0.1) 0%, transparent 50%)',
            'radial-gradient(circle at 50% 80%, rgba(16,185,129,0.1) 0%, transparent 50%)',
            'radial-gradient(circle at 20% 50%, rgba(59,130,246,0.1) 0%, transparent 50%)'
          ]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
}

// Simpler version for backgrounds
export function AnimatedGradientBG({ 
  className = '',
  intensity = 'medium' 
}: { 
  className?: string;
  intensity?: 'light' | 'medium' | 'strong';
}) {
  const getIntensityValues = () => {
    switch (intensity) {
      case 'light':
        return {
          opacity: 0.05,
          colors: ['rgba(59,130,246,0.05)', 'rgba(168,85,247,0.05)', 'rgba(16,185,129,0.05)']
        };
      case 'strong':
        return {
          opacity: 0.2,
          colors: ['rgba(59,130,246,0.2)', 'rgba(168,85,247,0.2)', 'rgba(16,185,129,0.2)']
        };
      default:
        return {
          opacity: 0.1,
          colors: ['rgba(59,130,246,0.1)', 'rgba(168,85,247,0.1)', 'rgba(16,185,129,0.1)']
        };
    }
  };

  const { colors } = getIntensityValues();

  return (
    <motion.div
      className={`absolute inset-0 ${className}`}
      animate={{
        background: [
          `radial-gradient(circle at 20% 20%, ${colors[0]} 0%, transparent 50%)`,
          `radial-gradient(circle at 80% 80%, ${colors[1]} 0%, transparent 50%)`,
          `radial-gradient(circle at 40% 60%, ${colors[2]} 0%, transparent 50%)`,
          `radial-gradient(circle at 20% 20%, ${colors[0]} 0%, transparent 50%)`
        ]
      }}
      transition={{
        duration: 10,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  );
}
