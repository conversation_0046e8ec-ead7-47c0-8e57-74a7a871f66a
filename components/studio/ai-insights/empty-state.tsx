"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, PlusCircle, BarChart3, <PERSON><PERSON>dingUp, Target } from "lucide-react"
import Image from "next/image"
import PremiumContainer from "@/components/studio/layout/premium-container"

interface EmptyStateProps {
  onCreateNew: () => void
}

export default function EmptyState({ onCreateNew }: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <PremiumContainer variant="floating" glow className="max-w-2xl text-center">
        <div className="space-y-8">
          {/* Premium Icon Display */}
          <div className="relative">
            <div className="w-24 h-24 mx-auto rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center glow-blue">
              <BarChart3 className="w-12 h-12 text-white" />
            </div>
            {/* Floating icons */}
            <div className="absolute -top-2 -right-2 w-8 h-8 rounded-lg bg-gradient-to-br from-emerald-500 to-blue-500 flex items-center justify-center animate-pulse">
              <TrendingUp className="w-4 h-4 text-white" />
            </div>
            <div className="absolute -bottom-2 -left-2 w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center animate-pulse delay-300">
              <Target className="w-4 h-4 text-white" />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-3xl font-bold text-white">
              Ready to Transform Your Ideas?
            </h3>
            <p className="text-slate-300 max-w-lg mx-auto text-lg leading-relaxed">
              Get started by creating your first SaaS idea analysis. Our AI will evaluate your concept and provide detailed insights to help you build the next big thing.
            </p>
          </div>

          {/* Feature highlights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            {[
              { icon: BarChart3, title: "Market Analysis", desc: "Deep market insights" },
              { icon: TrendingUp, title: "Growth Potential", desc: "Scalability assessment" },
              { icon: Target, title: "Success Metrics", desc: "KPI recommendations" }
            ].map((feature, index) => (
              <div key={index} className="flex flex-col items-center p-4 rounded-xl bg-slate-800/30 backdrop-blur-sm">
                <feature.icon className="w-6 h-6 text-blue-400 mb-2" />
                <div className="font-medium text-white">{feature.title}</div>
                <div className="text-slate-400 text-xs">{feature.desc}</div>
              </div>
            ))}
          </div>

          <Button
            onClick={onCreateNew}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-medium shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl border-0 hover:scale-105 glow-blue"
          >
            <Sparkles className="w-5 h-5 mr-3" />
            Create Your First Analysis
            <PlusCircle className="w-5 h-5 ml-3" />
          </Button>
        </div>
      </PremiumContainer>
    </div>
  )
}