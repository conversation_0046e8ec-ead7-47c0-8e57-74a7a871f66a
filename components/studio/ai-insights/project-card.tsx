"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Calendar, GitBranch, Trash2, MoreVertical } from "lucide-react"
import { GradientCard } from "@/components/ui/gradient-card"
import { useRouter } from "next/navigation"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { deleteProject } from "@/lib/services/supabase-service"
import { useState } from "react"

interface Project {
  id: string
  name: string
  description: string
  score: number
  progress: number
  createdAt: string
  category: string
  status: string
}

interface ProjectCardProps {
  project: Project
  onDelete?: (projectId: string) => void
}

export default function ProjectCard({ project, onDelete }: ProjectCardProps) {
  const router = useRouter()
  const [isDeleting, setIsDeleting] = useState(false)

  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500"
    if (score >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "active":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30"
      case "planning":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      default:
        return "bg-slate-500/20 text-slate-400 border-slate-500/30"
    }
  }

  const handleProjectClick = () => {
    // Navigate directly to User Flow as the main entry point
    router.push(`/studio/ai-insights/${project.id}/user-flow`)
  }

  const handleDelete = async () => {
    if (!onDelete) return

    setIsDeleting(true)
    try {
      await deleteProject(project.id)
      onDelete(project.id)
    } catch (error) {
      console.error('Error deleting project:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div className="group relative">
      {/* Premium glow effect on hover */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-0 group-hover:opacity-30 transition duration-500"></div>

      <GradientCard
        variant="glass"
        className="relative p-6 h-full flex flex-col border border-slate-700/30 hover:border-blue-500/50 transition-all duration-500 hover:scale-105 hover:shadow-2xl bg-slate-900/40 backdrop-blur-xl"
      >
        <div className="flex-grow">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1 pr-3">
              <h3 className="text-lg font-bold text-white mb-2 line-clamp-1 group-hover:text-blue-300 transition-colors">
                {project.name}
              </h3>
              <p className="text-sm text-slate-300 line-clamp-2 mb-3 leading-relaxed">
                {project.description}
              </p>
            </div>
            <div className="flex items-center gap-2 ml-2 flex-shrink-0">
              <div
                className={`w-8 h-8 rounded-xl ${getScoreColor(
                  project.score,
                )} flex items-center justify-center text-white text-sm font-bold shadow-lg glow-blue`}
              >
                {project.score}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-all">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-slate-800/90 backdrop-blur-xl border-slate-700/50">
                  <DropdownMenuItem
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="text-red-400 hover:text-red-300 hover:bg-red-500/20 transition-colors"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {isDeleting ? 'Deleting...' : 'Delete'}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-slate-800/30 rounded-xl p-3 backdrop-blur-sm">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-slate-300 font-medium">Progress</span>
                <span className="text-sm text-blue-400 font-bold">{project.progress}%</span>
              </div>
              <Progress
                value={project.progress}
                className="h-2 bg-slate-700/50"
                indicatorClassName="bg-gradient-to-r from-blue-500 to-purple-500"
              />
            </div>

            <div className="flex items-center gap-2 text-sm text-slate-300">
              <Calendar className="w-4 h-4 text-blue-400" />
              <span>{new Date(project.createdAt).toLocaleDateString()}</span>
            </div>

            <div className="flex flex-wrap items-center gap-2">
              <Badge className={`${getStatusColor(project.status)} text-sm px-3 py-1 rounded-lg font-medium`}>
                {project.status}
              </Badge>
              <Badge variant="outline" className="text-slate-300 border-slate-600/50 text-sm px-3 py-1 rounded-lg bg-slate-800/30">
                {project.category}
              </Badge>
            </div>
          </div>
        </div>

        <div className="mt-6 pt-4 border-t border-slate-700/50">
          <Button
            onClick={handleProjectClick}
            size="lg"
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-300 text-sm font-medium shadow-lg hover:shadow-xl hover:scale-105 border-0 rounded-xl py-3"
          >
            <GitBranch className="w-4 h-4 mr-2" />
            <span>View Project</span>
          </Button>
        </div>
      </GradientCard>
    </div>
  )
}
