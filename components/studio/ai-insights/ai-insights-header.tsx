"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, TrendingUp, BarChart3 } from "lucide-react"
import { useAIAnalysis } from "@/components/studio/ai-analysis/ai-analysis-provider"
import PremiumContainer from "@/components/studio/layout/premium-container"

export default function AIInsightsHeader() {
  const { openModal } = useAIAnalysis()

  return (
    <PremiumContainer variant="glass" glow className="border-blue-500/20">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center glow-blue">
            <BarChart3 className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white tracking-tight">
              AI Insights
            </h1>
            <p className="text-slate-300 mt-1 flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-blue-400" />
              AI-powered analysis and management for your SaaS ideas
            </p>
          </div>
        </div>
        <Button
          onClick={openModal}
          size="lg"
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl border-0 hover:scale-105 glow-blue"
        >
          <Sparkles className="w-5 h-5 mr-2" />
          New Analysis
        </Button>
      </div>
    </PremiumContainer>
  )
}
