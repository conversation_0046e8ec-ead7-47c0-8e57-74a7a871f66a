"use client"

import ProjectCard from "./project-card"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"

interface ProjectsGridProps {
  projects: AIAnalysisResult[]
  filter: string
  onProjectDelete?: (projectId: string) => void
}

export default function ProjectsGrid({ projects, filter, onProjectDelete }: ProjectsGridProps) {
  // Filter projects based on the selected filter
  const filteredProjects = projects.filter(project => {
    if (filter === 'all') {
      return true;
    }
    
    if (filter === 'recent') {
      // Show projects created in the last 7 days
      // Since AIAnalysisResult doesn't have createdAt, we'll use current date as fallback
      const projectDate = new Date(); // All projects will be considered recent for now
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return projectDate >= weekAgo;
    }
    
    if (filter === 'high-potential') {
      // Show projects with high market feasibility score (>= 7)
      return (project.marketFeasibility?.overallScore || 0) >= 7;
    }
    
    return true;
  });

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 mb-8">
      {filteredProjects.map((project, index) => (
        <ProjectCard
          key={project.id || `project-${index}`}
          project={{
            id: project.id || `temp-${index}`,
            name: project.projectName,
            description: project.projectDescription,
            score: Math.round((project.marketFeasibility?.overallScore || 0) * 10),
            progress: 25, // Default progress
            createdAt: new Date().toISOString(), // Default date
            category: "SaaS",
            status: "planning"
          }}
          onDelete={onProjectDelete}
        />
      ))}
    </div>
  )
}
