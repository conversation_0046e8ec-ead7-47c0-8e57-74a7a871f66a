"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Filter, Clock, Star, Layers } from "lucide-react"
import PremiumContainer from "@/components/studio/layout/premium-container"

const filters = [
  { id: "all", label: "All Projects", icon: Layers, count: null },
  { id: "recent", label: "Recent", icon: Clock, count: null },
  { id: "high-potential", label: "High Potential", icon: Star, count: null },
]

interface ProjectFiltersProps {
  onFilterChange: (filter: string) => void
  activeFilter: string
}

export default function ProjectFilters({ onFilterChange, activeFilter }: ProjectFiltersProps) {
  return (
    <PremiumContainer variant="elevated" className="p-4">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2 text-slate-300">
          <Filter className="w-4 h-4" />
          <span className="text-sm font-medium">Filter Projects</span>
        </div>
        <div className="flex flex-wrap gap-2">
          {filters.map((filter) => {
            const Icon = filter.icon
            return (
              <Button
                key={filter.id}
                variant={activeFilter === filter.id ? "default" : "outline"}
                size="sm"
                onClick={() => onFilterChange(filter.id)}
                className={
                  activeFilter === filter.id
                    ? "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 px-4 py-2 text-sm font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                    : "bg-slate-800/30 hover:bg-slate-700/50 text-slate-300 border-slate-700/30 hover:border-slate-600/50 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 backdrop-blur-sm hover:scale-105"
                }
              >
                <Icon className="w-4 h-4 mr-2" />
                {filter.label}
              </Button>
            )
          })}
        </div>
      </div>
    </PremiumContainer>
  )
}

