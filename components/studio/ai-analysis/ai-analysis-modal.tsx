"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { X, Check, Sparkles, BarChart3, Users, DollarSign, TrendingUp, Target, Edit } from "lucide-react"
import { GradientCard, GradientCardContent, GradientCardHeader, GradientCardTitle } from "@/components/ui/gradient-card"
import CountUp from "@/components/animations/count-up"
import SplitText from "@/components/animations/split-text"
import { cn } from "@/lib/utils"
import { analyzeProject, type AIAnalysisResult } from "./ai-analysis-service"

interface AIAnalysisModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  isNewUser?: boolean
}

interface AnalysisStep {
  id: string
  label: string
  completed: boolean
}

const analysisSteps: AnalysisStep[] = [
  { id: "description", label: "Analyzing project description", completed: false },
  { id: "name", label: "Generating project name", completed: false },
  { id: "feasibility", label: "Evaluating market feasibility", completed: false },
  { id: "features", label: "Identifying core features", completed: false },
  { id: "tech", label: "Determining technical requirements", completed: false },
  { id: "roadmap", label: "Creating development roadmap", completed: false },
  { id: "improvements", label: "Generating improvement suggestions", completed: false },
]

export default function AIAnalysisModal({ open, onOpenChange, isNewUser = false }: AIAnalysisModalProps) {
  const [currentStep, setCurrentStep] = useState<"input" | "processing" | "results">("input")
  const [projectName, setProjectName] = useState("")
  const [projectDescription, setProjectDescription] = useState("")
  const [progress, setProgress] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<string[]>([])
  const [showResults, setShowResults] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<AIAnalysisResult | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [saveError, setSaveError] = useState<string | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    projectName: '',
    projectDescription: ''
  })

  // Reset state when modal is opened
  useEffect(() => {
    if (open) {
      resetModalState()
    }
  }, [open])

  const resetModalState = () => {
    setCurrentStep("input")
    setProjectName("")
    setProjectDescription("")
    setProgress(0)
    setCompletedSteps([])
    setShowResults(false)
    setAnalysisResult(null)
    setIsAnalyzing(false)
    setIsSaving(false)
    setSaveError(null)
    setIsEditing(false)
    setEditForm({
      projectName: '',
      projectDescription: ''
    })
  }

  const overallScore = analysisResult?.marketFeasibility?.overallScore || 0

  const handleAnalyze = async () => {
    if (projectName.trim() && projectDescription.trim().length >= 50) {
      setCurrentStep("processing")
      setIsAnalyzing(true)

      try {
        // Start the progress animation at a slower pace for AI processing
        simulateProgress()

        // Perform the actual analysis with AI
        console.log("Starting AI analysis...")
        const result = await analyzeProject({
          name: projectName,
          description: projectDescription,
        })
        
        console.log("AI analysis completed, setting results")
        setAnalysisResult(result)

        // Ensure progress reaches 100% before showing results
        setTimeout(() => {
          setProgress(100)
          setCompletedSteps(analysisSteps.map((step) => step?.id).filter(Boolean))

          setTimeout(() => {
            setCurrentStep("results")
            setShowResults(true)
            setIsAnalyzing(false)
          }, 500)
        }, 1000)
      } catch (error) {
        console.error("Analysis failed:", error)
        setIsAnalyzing(false)
        setSaveError("Failed to analyze project. Please try again.")
        setCurrentStep("input")
      }
    }
  }

  const simulateProgress = () => {
    // Safety check to ensure analysisSteps is available
    if (!analysisSteps || analysisSteps.length === 0) {
      console.error("analysisSteps is not available")
      return
    }

    let currentProgress = 0
    let stepIndex = 0

    const interval = setInterval(() => {
      // Gemini API typically responds in 3-10 seconds
      // Increment progress at a pace that matches this timeframe
      const increment = Math.random() * 3 + 1 // 1 to 4 percent per step
      currentProgress += increment

      // Cap at 90% until real analysis completes
      setProgress(Math.min(currentProgress, 90))

      if (stepIndex < analysisSteps.length && currentProgress > (stepIndex + 1) * (90 / analysisSteps.length)) {
        // Fixed: Properly update completed steps with error handling
        setCompletedSteps((prev) => {
          const newSteps = [...prev]
          const currentStep = analysisSteps[stepIndex]
          if (currentStep && currentStep.id && !newSteps.includes(currentStep.id)) {
            newSteps.push(currentStep.id)
          }
          return newSteps
        })
        stepIndex++
      }

      if (currentProgress >= 90) {
        clearInterval(interval)
      }
    }, 350) // Update every 350ms
  }

  const handleStartOver = () => {
    resetModalState()
  }

  const handleSaveIdea = async () => {
    if (analysisResult && !isSaving) {
      try {
        setIsSaving(true)
        setSaveError(null)

        // Import the saveProjectAnalysisClient function
        const { saveProjectAnalysisClient } = await import('./ai-analysis-client');

        // Save to database
        const projectId = await saveProjectAnalysisClient(analysisResult);

        if (projectId) {
          // Close the modal
          onOpenChange(false);

          // You could add a success notification here
          console.log("Project saved successfully with ID:", projectId);
        } else {
          setSaveError("Failed to save project - no ID returned");
        }
      } catch (error) {
        console.error("Error saving project:", error);
        setSaveError("Failed to save project. Please try again.");
      } finally {
        setIsSaving(false)
      }
    }
  }

  const handleEditDetails = () => {
    if (analysisResult) {
      setEditForm({
        projectName: analysisResult.projectName || '',
        projectDescription: analysisResult.projectDescription || ''
      })
      setIsEditing(true)
    }
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditForm({
      projectName: '',
      projectDescription: ''
    })
  }

  const handleSaveEdit = () => {
    if (analysisResult && editForm.projectName.trim() && editForm.projectDescription.trim()) {
      // Update the analysis result with new values
      setAnalysisResult({
        ...analysisResult,
        projectName: editForm.projectName.trim(),
        projectDescription: editForm.projectDescription.trim()
      })
      setIsEditing(false)
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!isAnalyzing || !newOpen) {
          onOpenChange(newOpen)
        }
      }}
    >
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto glass-card text-white border-0 shadow-3xl">
        <DialogHeader className="relative">
          <DialogTitle className="text-2xl font-bold text-center">
            {currentStep === "input" && "Create New SaaS Idea"}
            {currentStep === "processing" && "Generating your SaaS blueprint"}
            {currentStep === "results" && "Review your AI Blueprint"}
          </DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-0 top-0 text-slate-400 hover:text-white"
            onClick={() => !isAnalyzing && onOpenChange(false)}
            disabled={isAnalyzing}
          >
            <X className="w-4 h-4" />
          </Button>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="flex justify-center mb-6">
          <div className="flex items-center space-x-4">
            {["input", "processing", "results"].map((step, index) => (
              <div key={step} className="flex items-center">
                <div
                  className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300",
                    currentStep === step
                      ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white glow-blue"
                      : index < ["input", "processing", "results"].indexOf(currentStep)
                        ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white glow-emerald"
                        : "bg-slate-700/50 text-slate-400 border border-slate-600",
                  )}
                >
                  {index < ["input", "processing", "results"].indexOf(currentStep) ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    index + 1
                  )}
                </div>
                {index < 2 && (
                  <div
                    className={cn(
                      "w-12 h-0.5 mx-2 transition-all",
                      index < ["input", "processing", "results"].indexOf(currentStep) ? "bg-green-500" : "bg-slate-700",
                    )}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step 1: Input Form */}
        {currentStep === "input" && (
          <div className="space-y-6">
            <div className="text-center text-slate-300 mb-8">
              Enter your project idea details to get started with AI-powered analysis
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">Project Name *</label>
                <Input
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  placeholder="Enter your project name"
                  className="glass border-slate-600/50 text-white placeholder:text-slate-400 focus:border-blue-500/50 focus:glow-blue transition-all duration-300"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">Project Description *</label>
                <Textarea
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  placeholder="Describe your SaaS idea in detail. Include target audience, core functionality, and what problem it solves."
                  className="glass border-slate-600/50 text-white placeholder:text-slate-400 min-h-[120px] focus:border-blue-500/50 focus:glow-blue transition-all duration-300"
                />
                <div className="flex justify-between items-center mt-2">
                  <span className="text-xs text-slate-400">Minimum 50 characters required</span>
                  <span
                    className={cn("text-xs", projectDescription.length >= 50 ? "text-green-400" : "text-slate-400")}
                  >
                    {projectDescription.length}/50
                  </span>
                </div>
              </div>
            </div>

            <div className="flex justify-center pt-6">
              <Button
                onClick={handleAnalyze}
                disabled={!projectName.trim() || projectDescription.trim().length < 50}
                className="btn-premium text-white px-8 py-3 text-lg font-medium border-0"
              >
                <Sparkles className="w-5 h-5 mr-2" />
                Analyze My Idea
              </Button>
            </div>
          </div>
        )}

        {/* Step 2: Processing */}
        {currentStep === "processing" && (
          <div className="space-y-6">
            <div className="text-center text-slate-300 mb-8">
              Our AI is analyzing your idea and generating insights...
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-300">Progress</span>
                <span className="text-sm text-blue-400 font-medium">
                  <CountUp to={progress} duration={0.5} />%
                </span>
              </div>
              <Progress
                value={progress}
                className="h-2"
                indicatorClassName="bg-gradient-to-r from-blue-500 to-purple-600"
              />
            </div>

            {/* Analysis Steps */}
            <div className="space-y-3">
              {analysisSteps && analysisSteps.map((step) => (
                step && step.id ? (
                  <div key={step.id} className="flex items-center space-x-3">
                    <div
                      className={cn(
                        "w-5 h-5 rounded-full flex items-center justify-center transition-all duration-300",
                        completedSteps.includes(step.id) ? "bg-green-500 text-white" : "border-2 border-slate-600",
                      )}
                    >
                      {completedSteps.includes(step.id) && <Check className="w-3 h-3" />}
                    </div>
                    <span
                      className={cn(
                        "text-sm transition-all duration-300",
                        completedSteps.includes(step.id) ? "text-white" : "text-slate-400",
                      )}
                    >
                      {step.label}
                    </span>
                  </div>
                ) : null
              ))}
            </div>

            {/* Skeleton UI Preview */}
            <div className="mt-8 space-y-4 opacity-50">
              <div className="h-4 bg-slate-700 rounded animate-pulse" />
              <div className="grid grid-cols-2 gap-4">
                <div className="h-20 bg-slate-700 rounded animate-pulse" />
                <div className="h-20 bg-slate-700 rounded animate-pulse" />
              </div>
              <div className="h-32 bg-slate-700 rounded animate-pulse" />
            </div>
          </div>
        )}

        {/* Step 3: Results */}
        {currentStep === "results" && showResults && analysisResult && (
          <div className="space-y-6">
            {!analysisResult.marketFeasibility || !analysisResult.coreFeatures ? (
              <div className="text-center text-slate-300">Loading analysis details...</div>
            ) : (
            <>
            <div className="text-center text-slate-300 mb-8">Here's the analysis of your SaaS idea</div>

            {/* Project Overview */}
            {isEditing ? (
              <GradientCard variant="feature">
                <GradientCardHeader>
                  <GradientCardTitle>Edit Project Details</GradientCardTitle>
                </GradientCardHeader>
                <GradientCardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">Project Name *</label>
                    <Input
                      value={editForm.projectName}
                      onChange={(e) => setEditForm(prev => ({ ...prev, projectName: e.target.value }))}
                      placeholder="Enter your project name"
                      className="glass border-slate-600/50 text-white placeholder:text-slate-400 focus:border-blue-500/50 focus:glow-blue transition-all duration-300"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">Project Description *</label>
                    <Textarea
                      value={editForm.projectDescription}
                      onChange={(e) => setEditForm(prev => ({ ...prev, projectDescription: e.target.value }))}
                      placeholder="Describe your SaaS idea in detail"
                      className="glass border-slate-600/50 text-white placeholder:text-slate-400 min-h-[120px] focus:border-blue-500/50 focus:glow-blue transition-all duration-300"
                    />
                  </div>
                  <div className="flex justify-center space-x-3 pt-4">
                    <Button
                      variant="outline"
                      onClick={handleCancelEdit}
                      className="border-slate-600 text-slate-300 hover:bg-slate-800"
                    >
                      <X className="w-4 h-4 mr-2" />
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSaveEdit}
                      disabled={!editForm.projectName.trim() || !editForm.projectDescription.trim()}
                      className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white disabled:opacity-50"
                    >
                      <Check className="w-4 h-4 mr-2" />
                      Save Changes
                    </Button>
                  </div>
                </GradientCardContent>
              </GradientCard>
            ) : (
              <div className="text-center space-y-2">
                <SplitText text={analysisResult.projectName} className="text-2xl font-bold text-white" delay={50} />
                <p className="text-slate-300">{analysisResult.projectDescription?.substring(0, 100) || ""}...</p>
              </div>
            )}

            {/* Market Feasibility Analysis */}
            <GradientCard variant="feature">
              <GradientCardHeader>
                <GradientCardTitle className="flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5" />
                  <span>Market Feasibility Analysis</span>
                </GradientCardTitle>
              </GradientCardHeader>
              <GradientCardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {analysisResult.marketFeasibility?.pillars?.map((pillar, index) => (
                    <div key={pillar.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {index === 0 && <Sparkles className="w-4 h-4" />}
                          {index === 1 && <Target className="w-4 h-4" />}
                          {index === 2 && <TrendingUp className="w-4 h-4" />}
                          {index === 3 && <DollarSign className="w-4 h-4" />}
                          {index === 4 && <BarChart3 className="w-4 h-4" />}
                          {index === 5 && <Users className="w-4 h-4" />}
                          <span className="text-sm font-medium text-white">{pillar.name}</span>
                        </div>
                        <span className="text-sm text-blue-400">{pillar.score}/10</span>
                      </div>
                      <Progress value={pillar.score * 10} className="h-2" />
                      <p className="text-xs text-slate-400">{pillar.description}</p>
                    </div>
                  ))}
                </div>
                <div className="text-center pt-4 border-t border-slate-700">
                  <div className="text-2xl font-bold text-white">
                    <CountUp to={overallScore} duration={2} />
                    <span className="text-lg text-slate-400">/10</span>
                  </div>
                  <p className="text-sm text-slate-300">Overall Score</p>
                </div>
              </GradientCardContent>
            </GradientCard>

            {/* Suggested Improvements */}
            <GradientCard variant="feature">
              <GradientCardHeader>
                <GradientCardTitle>Suggested Improvements</GradientCardTitle>
              </GradientCardHeader>
              <GradientCardContent>
                <ul className="space-y-2">
                  {analysisResult.suggestedImprovements?.map((improvement, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm text-slate-300">{improvement}</span>
                    </li>
                  ))}
                </ul>
              </GradientCardContent>
            </GradientCard>

            {/* Core Features */}
            <GradientCard variant="feature">
              <GradientCardHeader>
                <GradientCardTitle>Core Features</GradientCardTitle>
              </GradientCardHeader>
              <GradientCardContent>
                <div className="space-y-3">
                  {analysisResult.coreFeatures?.map((feature) => (
                    <div key={feature.name} className="flex items-start space-x-3">
                      <Check className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-white">{feature.name}</span>
                          <Badge
                            variant={feature.priority === "high" ? "default" : "secondary"}
                            className={cn(
                              "text-xs",
                              feature.priority === "high" && "bg-red-500/20 text-red-400",
                              feature.priority === "medium" && "bg-yellow-500/20 text-yellow-400",
                              feature.priority === "low" && "bg-green-500/20 text-green-400",
                            )}
                          >
                            {feature.priority}
                          </Badge>
                        </div>
                        <p className="text-sm text-slate-400">{feature.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </GradientCardContent>
            </GradientCard>

            {/* Technical Requirements */}
            <GradientCard variant="feature">
              <GradientCardHeader>
                <GradientCardTitle>Technical Requirements</GradientCardTitle>
              </GradientCardHeader>
              <GradientCardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(analysisResult.technicalRequirements || {}).map(([category, technologies]) => (
                    <div key={category}>
                      <h4 className="font-medium text-white capitalize mb-2">{category}</h4>
                      <div className="flex flex-wrap gap-2">
                        {technologies.map((tech) => (
                          <Badge key={tech} variant="outline" className="text-xs border-slate-600 text-slate-300">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </GradientCardContent>
            </GradientCard>

            {/* Pricing Model */}
            <GradientCard variant="feature">
              <GradientCardHeader>
                <GradientCardTitle>Suggested Pricing Model</GradientCardTitle>
              </GradientCardHeader>
              <GradientCardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {analysisResult.pricingModel?.map((tier) => (
                    <div
                      key={tier.name}
                      className={cn(
                        "p-4 rounded-lg border transition-all",
                        tier.recommended ? "border-blue-500 bg-blue-500/10" : "border-slate-600 bg-slate-800/50",
                      )}
                    >
                      <div className="text-center">
                        <h4 className="font-medium text-white">{tier.name}</h4>
                        <div className="text-2xl font-bold text-white mt-2">
                          {tier.name.toLowerCase().includes('custom') ?
                            'Custom' :
                            tier.price === 0 ?
                              'Free' :
                              `$${tier.price}`
                          }
                          {!tier.name.toLowerCase().includes('custom') && tier.price > 0 && (
                            <span className="text-sm text-slate-400">/month</span>
                          )}
                        </div>
                        {tier.recommended && <Badge className="mt-2 bg-blue-500 text-white">Recommended</Badge>}
                      </div>
                      <ul className="mt-4 space-y-2">
                        {tier.features.map((feature) => (
                          <li key={feature} className="flex items-center space-x-2">
                            <Check className="w-4 h-4 text-green-400" />
                            <span className="text-sm text-slate-300">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </GradientCardContent>
            </GradientCard>

            {/* Action Buttons */}
            <div className="flex justify-center space-x-4 pt-6">
              <Button
                variant="outline"
                onClick={handleStartOver}
                className="border-slate-600 text-slate-300 hover:bg-slate-800"
              >
                Start Over
              </Button>
              <Button
                variant="outline"
                onClick={handleEditDetails}
                disabled={isEditing}
                className="border-slate-600 text-slate-300 hover:bg-slate-800 disabled:opacity-50"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit Details
              </Button>
              <Button
                onClick={handleSaveIdea}
                disabled={isSaving}
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white disabled:opacity-50 shadow-lg hover:shadow-xl transition-all duration-300 border-0 hover:scale-105"
              >
                {isSaving ? "Saving..." : "Save Idea"}
              </Button>
            </div>
            
            {/* Save Error Message */}
            {saveError && (
              <div className="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
                <p className="text-red-400 text-sm">{saveError}</p>
              </div>
            )}
            </>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
