"use client"

import { Button, type ButtonProps } from "@/components/ui/button"
import { <PERSON>rk<PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface AIAnalysisButtonProps extends ButtonProps {
  variant?: "default" | "outline" | "ghost" | "link" | "premium"
  showIcon?: boolean
}

export default function AIAnalysisButton({
  children = "Generate AI Blueprint",
  variant = "premium",
  showIcon = true,
  className,
  ...props
}: AIAnalysisButtonProps) {
  return (
    <Button
      variant={variant === "premium" ? "default" : variant}
      className={cn(
        variant === "premium" &&
          "bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 border-0 hover:scale-105",
        className,
      )}
      {...props}
    >
      {showIcon && <Sparkles className="w-4 h-4 mr-2" />}
      {children}
    </Button>
  )
}
