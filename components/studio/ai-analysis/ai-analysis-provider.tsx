"use client"

import { create<PERSON>ontext, useContext, useState, type <PERSON>actN<PERSON> } from "react"
import AIAnalysisModal from "./ai-analysis-modal"

interface AIAnalysisContextType {
  isOpen: boolean
  openModal: () => void
  closeModal: () => void
}

const AIAnalysisContext = createContext<AIAnalysisContextType | undefined>(undefined)

export function useAIAnalysisModal() {
  const context = useContext(AIAnalysisContext)
  if (!context) {
    throw new Error("useAIAnalysisModal must be used within AIAnalysisProvider")
  }
  return context
}

// Also export as useAIAnalysis for backward compatibility
export const useAIAnalysis = useAIAnalysisModal

interface AIAnalysisProviderProps {
  children: ReactNode
}

export function AIAnalysisProvider({ children }: AIAnalysisProviderProps) {
  const [isOpen, setIsOpen] = useState(false)

  const openModal = () => setIsOpen(true)
  const closeModal = () => setIsOpen(false)

  return (
    <AIAnalysisContext.Provider value={{ isOpen, openModal, closeModal }}>
      {children}
      <AIAnalysisModal open={isOpen} onOpenChange={setIsOpen} />
    </AIAnalysisContext.Provider>
  )
}
