"use client"

import { AIAnalysisResult } from './ai-analysis-service'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'

interface AIAnalysisResultsProps {
  analysis: AIAnalysisResult
}

export default function AIAnalysisResults({ analysis }: AIAnalysisResultsProps) {
  // Extract service used from description if available
  const serviceUsed = analysis.projectDescription.includes("OpenAI") 
    ? "OpenAI" 
    : analysis.projectDescription.includes("Gemini") 
      ? "Gemini" 
      : "AI";
  
  return (
    <div className="space-y-6 text-white">
      {/* Project Overview */}
      <div>
        <h3 className="text-lg font-semibold mb-2 text-white">Project Overview</h3>
        <p className="text-gray-300 mb-2">{analysis.projectDescription}</p>
        <div className="text-xs text-blue-400">Generated using {serviceUsed}</div>
      </div>

      {/* Tabs for different sections */}
      <Tabs defaultValue="market" className="w-full">
        <TabsList className="grid grid-cols-5 mb-4 bg-[#131c31]">
          <TabsTrigger value="market" className="text-white data-[state=active]:bg-blue-900 data-[state=active]:text-white">Market Analysis</TabsTrigger>
          <TabsTrigger value="improvements" className="text-white data-[state=active]:bg-blue-900 data-[state=active]:text-white">Improvements</TabsTrigger>
          <TabsTrigger value="features" className="text-white data-[state=active]:bg-blue-900 data-[state=active]:text-white">Core Features</TabsTrigger>
          <TabsTrigger value="tech" className="text-white data-[state=active]:bg-blue-900 data-[state=active]:text-white">Tech Stack</TabsTrigger>
          <TabsTrigger value="pricing" className="text-white data-[state=active]:bg-blue-900 data-[state=active]:text-white">Pricing</TabsTrigger>
        </TabsList>
        
        {/* Market Analysis Tab */}
        <TabsContent value="market" className="space-y-4">
          <h4 className="text-lg font-medium text-white">Market Feasibility Analysis</h4>
          <p className="text-gray-300 mb-4">Analysis based on 6 core pillars of SaaS market feasibility</p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {analysis.marketFeasibility.pillars.map((pillar, index) => (
              <Card key={index} className="bg-[#131c31] border-gray-700 text-white">
                <CardHeader className="pb-2">
                  <CardTitle className="text-md font-medium">{pillar.name}</CardTitle>
                  <CardDescription className="text-gray-400">{pillar.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold">{pillar.score.toFixed(1)}</span>
                    <Progress 
                      value={pillar.score * 10} 
                      className="w-2/3 h-2 bg-gray-700" 
                      indicatorClassName={
                        pillar.score >= 8 ? "bg-green-500" : 
                        pillar.score >= 6 ? "bg-yellow-500" : "bg-red-500"
                      }
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <Card className="bg-[#131c31] border-gray-700 text-white">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center justify-between">
                <span>Overall Feasibility Score</span>
                <span className="text-2xl font-bold">{analysis.marketFeasibility.overallScore.toFixed(1)}/10</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Progress 
                value={analysis.marketFeasibility.overallScore * 10} 
                className="w-full h-3 bg-gray-700" 
                indicatorClassName={
                  analysis.marketFeasibility.overallScore >= 8 ? "bg-green-500" : 
                  analysis.marketFeasibility.overallScore >= 6 ? "bg-yellow-500" : "bg-red-500"
                }
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Improvements Tab */}
        <TabsContent value="improvements" className="space-y-4">
          <h4 className="text-lg font-medium text-white">Suggested Improvements</h4>
          <p className="text-gray-300 mb-4">Recommendations to enhance your SaaS project's market potential</p>
          
          <Card className="bg-[#131c31] border-gray-700 text-white">
            <CardContent className="pt-6">
              <ul className="space-y-3 list-disc pl-5">
                {analysis.suggestedImprovements.map((improvement, index) => (
                  <li key={index} className="text-gray-300">{improvement}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Core Features Tab */}
        <TabsContent value="features" className="space-y-4">
          <h4 className="text-lg font-medium text-white">Core Features</h4>
          <p className="text-gray-300 mb-4">Essential features your SaaS application should include</p>
          
          {analysis.coreFeatures.map((feature, index) => (
            <Card key={index} className="bg-[#131c31] border-gray-700 text-white">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-md font-medium">{feature.name}</CardTitle>
                  <Badge variant={
                    feature.priority === "high" ? "destructive" : 
                    feature.priority === "medium" ? "default" : "outline"
                  } className="capitalize">
                    {feature.priority} priority
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
        
        {/* Technical Stack Tab */}
        <TabsContent value="tech" className="space-y-4">
          <h4 className="text-lg font-medium text-white">Technical Requirements</h4>
          <p className="text-gray-300 mb-4">Recommended technologies for your SaaS project</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-[#131c31] border-gray-700 text-white">
              <CardHeader>
                <CardTitle className="text-md flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  Frontend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc pl-5 space-y-1 text-gray-300">
                  {analysis.technicalRequirements.frontend.map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              </CardContent>
            </Card>
            <Card className="bg-[#131c31] border-gray-700 text-white">
              <CardHeader>
                <CardTitle className="text-md flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                  </svg>
                  Backend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc pl-5 space-y-1 text-gray-300">
                  {analysis.technicalRequirements.backend.map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              </CardContent>
            </Card>
            <Card className="bg-[#131c31] border-gray-700 text-white">
              <CardHeader>
                <CardTitle className="text-md flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path>
                  </svg>
                  Database
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc pl-5 space-y-1 text-gray-300">
                  {analysis.technicalRequirements.database.map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              </CardContent>
            </Card>
            <Card className="bg-[#131c31] border-gray-700 text-white">
              <CardHeader>
                <CardTitle className="text-md flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                  Infrastructure
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc pl-5 space-y-1 text-gray-300">
                  {analysis.technicalRequirements.infrastructure.map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Pricing Model Tab */}
        <TabsContent value="pricing" className="space-y-4">
          <h4 className="text-lg font-medium text-white">Recommended Pricing Model</h4>
          <p className="text-gray-300 mb-4">Suggested pricing tiers for your SaaS product</p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {analysis.pricingModel.map((tier, index) => (
              <Card key={index} className={`bg-[#131c31] border-gray-700 text-white ${tier.recommended ? "border-2 border-blue-500" : ""}`}>
                {tier.recommended && (
                  <div className="bg-blue-600 text-white text-center py-1 text-sm font-medium">
                    Recommended
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{tier.name}</CardTitle>
                  <CardDescription className="text-2xl font-bold text-white">
                    {tier.name.toLowerCase().includes('custom') ?
                      'Custom' :
                      tier.price === 0 ?
                        'Free' :
                        `$${tier.price}`
                    }
                    {!tier.name.toLowerCase().includes('custom') && tier.price > 0 && "/mo"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {tier.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-2 text-gray-300">
                        <svg className="w-4 h-4 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 