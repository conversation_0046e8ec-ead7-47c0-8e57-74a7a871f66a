"use server"

import { z } from "zod"
import { getFallbackAnalysis } from "@/lib/services/openai-service"
import { analyzeProjectWithGemini } from "@/lib/services/gemini-service"

// Define the schema for the input data
const projectInputSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().min(50, "Project description must be at least 50 characters"),
})

// Define the schema for the output data
export interface AIAnalysisResult {
  id?: string
  projectName: string
  projectDescription: string
  marketFeasibility: {
    pillars: Array<{
      name: string
      score: number
      description: string
    }>
    overallScore: number
  }
  suggestedImprovements: string[]
  coreFeatures: Array<{
    name: string
    description: string
    priority: "high" | "medium" | "low"
  }>
  technicalRequirements: {
    frontend: string[]
    backend: string[]
    database: string[]
    infrastructure: string[]
  }
  pricingModel: Array<{
    name: string
    price: number
    features: string[]
    recommended?: boolean
  }>
}

// Function to validate and fix pricing model
function validatePricingModel(pricingModel: Array<{
  name: string
  price: number
  features: string[]
  recommended?: boolean
}>): Array<{
  name: string
  price: number
  features: string[]
  recommended?: boolean
}> {
  return pricingModel.map((tier, index) => {
    let validatedPrice = tier.price

    // Fix unrealistic prices (including very large numbers like 100250500)
    if (validatedPrice > 500 || validatedPrice < 0 || isNaN(validatedPrice) || !isFinite(validatedPrice)) {
      console.warn(`Invalid pricing detected: ${validatedPrice} for tier ${tier.name}. Applying realistic pricing.`)

      // Generate realistic price based on tier name
      if (tier.name.toLowerCase().includes('free') || tier.name.toLowerCase().includes('basic')) {
        validatedPrice = 0
      } else if (tier.name.toLowerCase().includes('starter') || tier.name.toLowerCase().includes('personal')) {
        validatedPrice = 9
      } else if (tier.name.toLowerCase().includes('pro') || tier.name.toLowerCase().includes('standard') || tier.name.toLowerCase().includes('professional')) {
        validatedPrice = 29
      } else if (tier.name.toLowerCase().includes('enterprise') || tier.name.toLowerCase().includes('business') || tier.name.toLowerCase().includes('team')) {
        validatedPrice = 99
      } else if (tier.name.toLowerCase().includes('custom')) {
        validatedPrice = 0 // Custom pricing shown as "Custom"
      } else {
        // Default fallback based on position
        const defaultPrices = [0, 19, 49, 99]
        validatedPrice = defaultPrices[index] || 19
      }
    }

    // Ensure price is a whole number
    validatedPrice = Math.round(validatedPrice)

    return {
      ...tier,
      price: validatedPrice
    }
  })
}

export async function analyzeProject(input: {
  name: string
  description: string
}): Promise<AIAnalysisResult> {
  try {
    // Validate the input data
    const validatedData = projectInputSchema.parse(input)
    
    console.log("Starting AI analysis for project:", validatedData.name)
    
    // Use Gemini as the primary AI service
    try {
      // Check if Gemini API key is available
      if (process.env.GEMINI_API_KEY) {
        console.log("Using Gemini for analysis...")
        const result = await analyzeProjectWithGemini(validatedData)
        console.log("Gemini analysis completed successfully")

        // Validate and fix pricing model
        result.pricingModel = validatePricingModel(result.pricingModel)

        return result
      }

      // If no Gemini API key or Gemini fails, use fallback
      console.log("No Gemini API key available, using fallback analysis")
      const fallbackResult = await getFallbackAnalysis(validatedData)

      // Validate and fix pricing model
      fallbackResult.pricingModel = validatePricingModel(fallbackResult.pricingModel)

      return fallbackResult
    } catch (aiError) {
      console.error("Gemini service error:", aiError)
      console.log("Using fallback analysis due to AI service error")
      const fallbackResult = await getFallbackAnalysis(validatedData)

      // Validate and fix pricing model
      fallbackResult.pricingModel = validatePricingModel(fallbackResult.pricingModel)

      return fallbackResult
    }
  } catch (error) {
    console.error("Error analyzing project:", error)
    throw new Error("Failed to analyze project")
  }
}
