"use client"

import { create<PERSON>rowser<PERSON>lient } from "@supabase/ssr"
import { AIAnalysisResult } from "./ai-analysis-service"

// Initialize Supabase client
const getSupabaseClient = () => {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// Save a project analysis to the database
export async function saveProjectAnalysisClient(analysis: AIAnalysisResult): Promise<string | null> {
  try {
    console.log("Saving project analysis to database...")
    const supabase = getSupabaseClient()
    
    // Get current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error("Error getting session:", sessionError)
      return null
    }
    
    if (!session?.user) {
      console.log("No authenticated user found, cannot save analysis")
      return null
    }
    
    const userId = session.user.id
    console.log("User authenticated, ID:", userId)
    
    // Create project data
    const projectData = {
      user_id: userId,
      project_name: analysis.projectName,
      project_description: analysis.projectDescription,
      market_feasibility: analysis.marketFeasibility,
      suggested_improvements: analysis.suggestedImprovements,
      core_features: analysis.coreFeatures,
      technical_requirements: analysis.technicalRequirements,
      pricing_model: analysis.pricingModel,
      progress: 25 // Initial progress
    }
    
    console.log("Inserting project data into database...")
    
    // Insert into database
    const { data, error } = await supabase
      .from('projects')
      .insert(projectData)
      .select('id')
      .single()
      
    if (error) {
      console.error('Error saving project analysis:', error)
      return null
    }

    if (!data) {
      console.error('Error saving project analysis: No data returned after insert.')
      return null
    }
    
    console.log("Project saved successfully with ID:", data.id)
    return data.id
  } catch (error) {
    console.error('Error in saveProjectAnalysisClient:', error)
    return null
  }
}

// Get all projects for the current user
export async function getUserProjectsClient(): Promise<AIAnalysisResult[]> {
  try {
    console.log("Fetching user projects from database...")
    const supabase = getSupabaseClient()
    
    // Get current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error('Error getting user session:', sessionError)
      return []
    }
    
    if (!session?.user) {
      console.log('No authenticated user found, returning empty projects array')
      return []
    }
    
    const userId = session.user.id
    console.log("User authenticated, fetching projects for user ID:", userId)
    
    // Fetch projects for the user
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      
    if (error) {
      console.error('Error fetching user projects:', error)
      return []
    }
    
    console.log(`Found ${data.length} projects for user`)
    
    // Transform to AIAnalysisResult interface
    return data.map((item: any) => ({
      id: item.id,
      projectName: item.project_name,
      projectDescription: item.project_description,
      marketFeasibility: item.market_feasibility,
      suggestedImprovements: item.suggested_improvements,
      coreFeatures: item.core_features,
      technicalRequirements: item.technical_requirements,
      pricingModel: item.pricing_model || []
    }))
  } catch (error) {
    console.error('Error in getUserProjectsClient:', error)
    return []
  }
} 