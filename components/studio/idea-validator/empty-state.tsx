"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rk<PERSON>, Lightbulb, PlusCircle, TrendingUp, Target, Code, DollarSign } from "lucide-react"
import PremiumContainer from "@/components/studio/layout/premium-container"

interface EmptyStateProps {
  onCreateNew: () => void
}

export default function EmptyState({ onCreateNew }: EmptyStateProps) {
  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <PremiumContainer variant="glass" glow className="max-w-2xl mx-auto text-center border-blue-500/20">
        <div className="space-y-8">
          {/* Icon and Title */}
          <div className="space-y-4">
            <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto glow-blue">
              <Lightbulb className="w-10 h-10 text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold text-white mb-2">
                Validate Your First SaaS Idea
              </h2>
              <p className="text-slate-300 text-lg">
                Get AI-powered insights and comprehensive analysis for your SaaS concepts
              </p>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-2 gap-4 text-left">
            <div className="flex items-start gap-3">
              <TrendingUp className="w-5 h-5 text-blue-400 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-white font-medium text-sm">Market Analysis</h3>
                <p className="text-slate-400 text-xs">Market size, competition, and demand assessment</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Code className="w-5 h-5 text-green-400 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-white font-medium text-sm">Technical Feasibility</h3>
                <p className="text-slate-400 text-xs">Development complexity and resource requirements</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <DollarSign className="w-5 h-5 text-yellow-400 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-white font-medium text-sm">Business Viability</h3>
                <p className="text-slate-400 text-xs">Revenue projections and business model validation</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Target className="w-5 h-5 text-purple-400 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-white font-medium text-sm">Strategic Recommendations</h3>
                <p className="text-slate-400 text-xs">Next steps and actionable insights</p>
              </div>
            </div>
          </div>

          {/* CTA Button */}
          <Button
            onClick={onCreateNew}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-medium shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl border-0 hover:scale-105 glow-blue"
          >
            <Sparkles className="w-5 h-5 mr-3" />
            Validate Your First Idea
            <PlusCircle className="w-5 h-5 ml-3" />
          </Button>
        </div>
      </PremiumContainer>
    </div>
  )
}
