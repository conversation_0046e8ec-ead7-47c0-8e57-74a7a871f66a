"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Trash2 } from "lucide-react"
import { IdeaValidationRecord } from "@/lib/services/idea-validation-db-service"
import {
  getValidationScoreColor,
  getRiskLevelColor
} from "@/lib/services/idea-validator-service"

interface ValidationCardProps {
  validation: IdeaValidationRecord
  onView: (validation: IdeaValidationRecord) => void
  onDelete: (id: string) => void
}

export default function ValidationCard({ validation, onView, onDelete }: ValidationCardProps) {
  return (
    <Card className="bg-slate-900/50 border-slate-800 hover:border-slate-700 transition-all duration-300 hover:scale-105 hover:shadow-xl group cursor-pointer">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 pr-2">
            <CardTitle className="text-white text-base font-semibold line-clamp-1 group-hover:text-blue-300 transition-colors">
              {validation.idea_title}
            </CardTitle>
            <div className="flex items-center gap-2 mt-1">
              <Calendar className="h-3 w-3 text-slate-500" />
              <span className="text-slate-500 text-xs">
                {new Date(validation.created_at).toLocaleDateString()}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <div className={`text-lg font-bold ${getValidationScoreColor(validation.validation_result.overallScore)}`}>
              {validation.validation_result.overallScore}
            </div>
            <span className="text-slate-400 text-xs">/100</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <p className="text-slate-400 text-xs leading-relaxed overflow-hidden" style={{ display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical' }}>
            {validation.idea_description}
          </p>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="bg-slate-800/50 rounded p-2">
              <div className="text-slate-500 mb-1">Success Rate</div>
              <div className={`font-semibold ${getValidationScoreColor(validation.validation_result.successProbability)}`}>
                {validation.validation_result.successProbability}%
              </div>
            </div>
            <div className="bg-slate-800/50 rounded p-2">
              <div className="text-slate-500 mb-1">Risk Level</div>
              <div className={`font-semibold capitalize ${getRiskLevelColor(validation.validation_result.riskLevel)}`}>
                {validation.validation_result.riskLevel}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onView(validation)}
              className="flex-1 text-xs"
            >
              View Details
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onDelete(validation.id)
              }}
              className="text-red-400 hover:text-red-300 hover:bg-red-500/20 p-2"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
