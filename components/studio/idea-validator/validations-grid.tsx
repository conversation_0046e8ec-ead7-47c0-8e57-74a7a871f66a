"use client"

import ValidationCard from "./validation-card"
import { IdeaValidationRecord } from "@/lib/services/idea-validation-db-service"

interface ValidationsGridProps {
  validations: IdeaValidationRecord[]
  onValidationView: (validation: IdeaValidationRecord) => void
  onValidationDelete: (id: string) => void
}

export default function ValidationsGrid({ 
  validations, 
  onValidationView, 
  onValidationDelete 
}: ValidationsGridProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 mb-8">
      {validations.map((validation, index) => (
        <ValidationCard
          key={validation.id || `validation-${index}`}
          validation={validation}
          onView={onValidationView}
          onDelete={onValidationDelete}
        />
      ))}
    </div>
  )
}
