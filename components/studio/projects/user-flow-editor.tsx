"use client"

import { useState, use<PERSON><PERSON>back, useR<PERSON>, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Plus, Save, Download, Share, Trash2, Edit, X, Check, ArrowRight, Sparkles, Loader2, Code, BarChart4, Users } from "lucide-react"
import { GradientCard, GradientCardContent, GradientCardHeader, GradientCardTitle } from "@/components/ui/gradient-card"
import { useRouter } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import Link from "next/link"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"

interface UserFlowEditorProps {
  projectId: string
}

interface FlowNode {
  id: string
  type: 'screen' | 'action' | 'decision'
  label: string
  x: number
  y: number
  connections: string[]
  details?: string[]
  responsibilities?: string[]
  fixed: boolean
  icon?: string
}

// New interface for project analysis results
interface ProjectAnalysis {
  uniqueness: number;
  stickiness: number;
  growthPotential: number;
  pricingModel: number;
  upsellPotential: number;
  customerPurchasingPower: number;
  overallScore: number;
  improvements: string[];
  coreFeatures: string[];
  techStack: string[];
  suggestedPricing: string;
}

// Update OpenAI API integration to return application features instead of database fields
const generateFlowWithAI = async (description: string): Promise<FlowNode[]> => {
  try {
    // This would be a real API call in production
    console.log('Generating flow with description:', description);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Return simplified data with application features
    return [
      { 
        id: '1', 
        type: 'screen', 
        label: 'LANDING PAGE', 
        x: 400, 
        y: 50, 
        connections: ['2', '3'],
        details: [
          'Hero section with CTA',
          'Feature showcase',
          'Testimonials slider',
          'Newsletter signup'
        ],
        fixed: true,
        icon: 'home'
      },
      { 
        id: '2', 
        type: 'screen', 
        label: 'AUTHENTICATION', 
        x: 200, 
        y: 300, 
        connections: ['4'],
        details: [
          'Login form',
          'Registration form',
          'Password reset',
          'OAuth providers'
        ],
        fixed: true,
        icon: 'user'
      },
      { 
        id: '3', 
        type: 'screen', 
        label: 'DASHBOARD', 
        x: 600, 
        y: 300, 
        connections: ['5'],
        details: [
          'User statistics',
          'Recent activity',
          'Quick actions',
          'Notifications'
        ],
        fixed: true,
        icon: 'category'
      },
      { 
        id: '4', 
        type: 'screen', 
        label: 'USER PROFILE', 
        x: 200, 
        y: 550, 
        connections: [],
        details: [
          'Personal information',
          'Account settings',
          'Subscription details',
          'Connected accounts'
        ],
        fixed: true,
        icon: 'user'
      },
      {
        id: '5',
        type: 'screen',
        label: 'ANALYTICS',
        x: 600,
        y: 550,
        connections: [],
        details: [
          'Performance metrics',
          'Usage statistics',
          'Data visualization',
          'Export options'
        ],
        fixed: true,
        icon: 'chart'
      }
    ];
  } catch (error) {
    console.error('Error generating flow:', error);
    throw error;
  }
};

// Helper function to get icon for node
const getNodeIcon = (iconName: string) => {
  switch (iconName) {
    case 'home':
      return <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M9 22V12h6v10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>;
    case 'user':
      return <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2M12 11a4 4 0 100-8 4 4 0 000 8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>;
    case 'category':
      return <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 3H4a1 1 0 00-1 1v6a1 1 0 001 1h6a1 1 0 001-1zM20 3h-6a1 1 0 00-1 1v6a1 1 0 001 1h6a1 1 0 001-1zM10 13H4a1 1 0 00-1 1v6a1 1 0 001 1h6a1 1 0 001-1zM20 13h-6a1 1 0 00-1 1v6a1 1 0 001 1h6a1 1 0 001-1z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>;
    case 'chart':
      return <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18 20V10M12 20V4M6 20v-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>;
    default:
      return <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>;
  }
};

export default function UserFlowEditor({ projectId }: UserFlowEditorProps) {
  const router = useRouter()
  const [isEditing, setIsEditing] = useState(false)
  const [nodes, setNodes] = useState<FlowNode[]>([])
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [editingLabel, setEditingLabel] = useState<string | null>(null)
  const [newLabel, setNewLabel] = useState("")
  const [draggingNode, setDraggingNode] = useState<string | null>(null)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [connectingFrom, setConnectingFrom] = useState<string | null>(null)
  const [showEmptyState, setShowEmptyState] = useState(true)
  const canvasRef = useRef<HTMLDivElement>(null)
  const [analysisPoints, setAnalysisPoints] = useState<string[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [activeTab, setActiveTab] = useState('user-flow')
  const [projectDescription, setProjectDescription] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [projectAnalysis, setProjectAnalysis] = useState<ProjectAnalysis | null>(null);
  const [projectIdea, setProjectIdea] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [projectData, setProjectData] = useState<any>(null);
  
  // Fetch project data from Supabase
  useEffect(() => {
    const fetchProjectData = async () => {
      try {
        setIsLoading(true);
        
        // In a real implementation, this would call the Supabase service
        // For now, we'll simulate a delay and use the projectId
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Fetch project from Supabase using the projectId
        // This would be replaced with a real API call to getProjectById
        const project = {
          id: projectId,
          projectName: "Sample Project",
          projectDescription: "This is a sample project description for demonstration purposes.",
          marketFeasibility: {
            pillars: [
              { name: "Uniqueness", score: 8, description: "Highly differentiated from existing solutions" },
              { name: "Stickiness", score: 7, description: "Good user retention potential" },
              { name: "Growth Trend", score: 9, description: "Strong market growth trajectory" },
              { name: "Pricing Potential", score: 8, description: "Can command premium pricing" },
              { name: "Upsell Potential", score: 6, description: "Moderate additional revenue opportunities" },
              { name: "Customer Purchasing Power", score: 8, description: "Target audience has strong purchasing power" }
            ],
            overallScore: 7.7
          },
          suggestedImprovements: [
            "Consider adding AI-driven personalization features to increase user engagement",
            "Implement interactive roadmap visualization for better user experience",
            "Add financial modeling tools to strengthen the value proposition",
            "Explore integration with popular project management tools"
          ],
          coreFeatures: [
            { name: "User Authentication", description: "Secure login and registration system", priority: "high" },
            { name: "Project Dashboard", description: "Central hub for managing projects", priority: "high" },
            { name: "AI Analysis Engine", description: "Core AI-powered analysis functionality", priority: "high" },
            { name: "Collaboration Tools", description: "Team collaboration and sharing features", priority: "medium" },
            { name: "Export & Integration", description: "Export data and integrate with other tools", priority: "medium" }
          ],
          technicalRequirements: {
            frontend: ["Next.js", "React", "TypeScript", "Tailwind CSS"],
            backend: ["Node.js", "Express", "OpenAI API", "Stripe"],
            database: ["PostgreSQL", "Redis", "Supabase"],
            infrastructure: ["Vercel", "AWS", "Docker", "GitHub Actions"]
          }
        };
        
        setProjectData(project);
        setProjectDescription(project.projectDescription);
        
        // Convert project data to ProjectAnalysis format
        const analysis: ProjectAnalysis = {
          uniqueness: project.marketFeasibility.pillars[0].score * 10,
          stickiness: project.marketFeasibility.pillars[1].score * 10,
          growthPotential: project.marketFeasibility.pillars[2].score * 10,
          pricingModel: project.marketFeasibility.pillars[3].score * 10,
          upsellPotential: project.marketFeasibility.pillars[4].score * 10,
          customerPurchasingPower: project.marketFeasibility.pillars[5].score * 10,
          overallScore: project.marketFeasibility.overallScore * 10,
          improvements: project.suggestedImprovements,
          coreFeatures: project.coreFeatures.map(f => f.name),
          techStack: [
            ...project.technicalRequirements.frontend,
            ...project.technicalRequirements.backend,
            ...project.technicalRequirements.database
          ],
          suggestedPricing: "Based on market analysis"
        };
        
        setProjectAnalysis(analysis);
        
        // Generate initial flow based on project data
        generateInitialFlowFromAnalysis(analysis);
      } catch (error) {
        console.error("Error fetching project data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchProjectData();
  }, [projectId]);
  
  // Generate initial flow based on analysis
  const generateInitialFlowFromAnalysis = (analysis: ProjectAnalysis) => {
    // Create nodes based on core features
    const nodes: FlowNode[] = [
      { 
        id: '1', 
        type: 'screen', 
        label: 'LANDING PAGE', 
        x: 400, 
        y: 50, 
        connections: ['2', '3'],
        details: [
          'Hero section with value proposition',
          'Feature showcase',
          'Pricing options',
          'Call to action'
        ],
        fixed: true,
        icon: 'home'
      }
    ];
    
    // Add nodes for each core feature
    analysis.coreFeatures.forEach((feature: string, index: number) => {
      const nodeId = (index + 2).toString();
      const prevNodeId = index === 0 ? '1' : (index + 1).toString();
      
      nodes.push({
        id: nodeId,
        type: 'screen',
        label: feature.toUpperCase(),
        x: 300 + (index % 2) * 400,
        y: 200 + Math.floor(index / 2) * 250,
        connections: [],
        details: ['Feature details will be added here'],
        fixed: true,
        icon: index % 4 === 0 ? 'home' : index % 4 === 1 ? 'user' : index % 4 === 2 ? 'category' : 'chart'
      });
      
      // Add connection from previous node
      if (index > 0) {
        const prevNode = nodes.find(n => n.id === prevNodeId);
        if (prevNode) {
          prevNode.connections.push(nodeId);
        }
      }
    });
    
    setNodes(nodes);
    setShowEmptyState(false);
  };
  
  // Initialize with some sample nodes when creating first flow
  const createInitialFlow = () => {
    const initialNodes: FlowNode[] = [
      { 
        id: '1', 
        type: 'screen', 
        label: 'LANDING PAGE', 
        x: 400, 
        y: 50, 
        connections: ['2', '3'],
        details: [
          'Hero section with CTA',
          'Feature showcase',
          'Testimonials slider',
          'Newsletter signup'
        ],
        fixed: true,
        icon: 'home'
      },
      { 
        id: '2', 
        type: 'screen', 
        label: 'AUTHENTICATION', 
        x: 200, 
        y: 300, 
        connections: ['4'],
        details: [
          'Login form',
          'Registration form',
          'Password reset',
          'OAuth providers'
        ],
        fixed: true,
        icon: 'user'
      },
      { 
        id: '3', 
        type: 'screen', 
        label: 'DASHBOARD', 
        x: 600, 
        y: 300, 
        connections: ['5'],
        details: [
          'User statistics',
          'Recent activity',
          'Quick actions',
          'Notifications'
        ],
        fixed: true,
        icon: 'category'
      },
      { 
        id: '4', 
        type: 'screen', 
        label: 'USER PROFILE', 
        x: 200, 
        y: 550, 
        connections: [],
        details: [
          'Personal information',
          'Account settings',
          'Subscription details',
          'Connected accounts'
        ],
        fixed: true,
        icon: 'user'
      },
      {
        id: '5',
        type: 'screen',
        label: 'ANALYTICS',
        x: 600,
        y: 550,
        connections: [],
        details: [
          'Performance metrics',
          'Usage statistics',
          'Data visualization',
          'Export options'
        ],
        fixed: true,
        icon: 'chart'
      }
    ]
    setNodes(initialNodes)
    setShowEmptyState(false)
    setIsEditing(true)
    
    // Auto-analyze after creating
    setTimeout(() => {
      analyzeUserFlow()
    }, 500)
  }
  
  const handleNodeMouseDown = (e: React.MouseEvent, nodeId: string) => {
    e.stopPropagation()
    setSelectedNode(nodeId)
    
    // Start dragging
    const node = nodes.find(n => n.id === nodeId)
    if (node) {
      setDraggingNode(nodeId)
      const rect = (e.target as HTMLElement).getBoundingClientRect()
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      })
    }
  }
  
  const handleCanvasMouseMove = (e: React.MouseEvent) => {
    if (draggingNode) {
      const canvas = canvasRef.current
      if (canvas) {
        const canvasRect = canvas.getBoundingClientRect()
        // Only update Y position, keep X position fixed
        const node = nodes.find(n => n.id === draggingNode)
        if (node && !node.fixed) {
          const y = e.clientY - canvasRect.top - dragOffset.y
          
          setNodes(nodes.map(n => 
            n.id === draggingNode 
              ? { ...n, y } 
              : n
          ))
        }
      }
    }
  }
  
  const handleCanvasMouseUp = () => {
    setDraggingNode(null)
  }
  
  const handleCanvasClick = () => {
    setSelectedNode(null)
    setConnectingFrom(null)
  }
  
  const handleStartConnection = (e: React.MouseEvent, nodeId: string) => {
    e.stopPropagation()
    setConnectingFrom(nodeId)
  }
  
  const handleCompleteConnection = (e: React.MouseEvent, targetId: string) => {
    e.stopPropagation()
    if (connectingFrom && connectingFrom !== targetId) {
      setNodes(nodes.map(node => 
        node.id === connectingFrom
          ? { ...node, connections: [...node.connections.filter(id => id !== targetId), targetId] }
          : node
      ))
      setConnectingFrom(null)
    }
  }
  
  const handleDeleteNode = (nodeId: string) => {
    setNodes(nodes.filter(node => node.id !== nodeId).map(node => ({
      ...node,
      connections: node.connections.filter(id => id !== nodeId)
    })))
    setSelectedNode(null)
  }
  
  const handleEditLabel = (nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId)
    if (node) {
      setEditingLabel(nodeId)
      setNewLabel(node.label)
    }
  }
  
  const handleSaveLabel = () => {
    if (editingLabel) {
      setNodes(nodes.map(node => 
        node.id === editingLabel
          ? { ...node, label: newLabel }
          : node
      ))
      setEditingLabel(null)
    }
  }
  
  const handleAddNode = (type: 'screen' | 'action' | 'decision') => {
    const id = (Math.max(0, ...nodes.map(n => parseInt(n.id))) + 1).toString()
    
    // Find the lowest node to place the new one below it
    const lowestNode = nodes.reduce((lowest, current) => 
      current.y > lowest.y ? current : lowest, 
      { y: 0 } as { y: number }
    )
    
    const newY = lowestNode.y + 250 // Increased vertical spacing between nodes
    const newX = type === 'decision' ? 400 : type === 'action' ? 250 : 100
    
    const newNode: FlowNode = {
      id,
      type,
      label: `New ${type}`,
      x: newX,
      y: newY,
      connections: [],
      details: ['Feature 1', 'Feature 2', 'Feature 3', 'Feature 4'],
      fixed: false
    }
    
    setNodes([...nodes, newNode])
    setSelectedNode(id)
  }
  
  const handleSaveFlow = () => {
    // Here you would save the flow data to your backend
    console.log('Saving flow:', nodes)
    setIsEditing(false)
  }

  const analyzeUserFlow = () => {
    setIsAnalyzing(true)
    
    // Simulate AI analysis (in a real app, this would call an API)
    setTimeout(() => {
      if (projectAnalysis) {
        const points = [
          `Uniqueness score: ${projectAnalysis.uniqueness}% - The project has distinctive features in the market`,
          `Stickiness score: ${projectAnalysis.stickiness}% - Users are likely to return regularly`,
          `Growth potential: ${projectAnalysis.growthPotential}% - Strong opportunity for user base expansion`,
          `Overall feasibility: ${projectAnalysis.overallScore}% - Project has good chances of success`
        ];
        setAnalysisPoints(points);
      } else {
        const points = [
          "Users can complete the sign-up flow in 2 steps with clear decision points",
          "The dashboard is accessible immediately after valid sign-up, improving user experience",
          "Consider adding a confirmation step after sign-up to reduce errors",
          "The flow has a logical structure with appropriate validation checkpoints"
        ];
        setAnalysisPoints(points);
      }
      setIsAnalyzing(false)
    }, 1500)
  }

  const generateFlowFromDescription = async () => {
    if (!projectDescription.trim()) {
      return;
    }
    
    try {
      setIsGenerating(true);
      const generatedNodes = await generateFlowWithAI(projectDescription);
      setNodes(generatedNodes);
      setShowEmptyState(false);
      setIsEditing(true);
      
      // Auto-analyze after creating
      setTimeout(() => {
        analyzeUserFlow();
      }, 500);
    } catch (error) {
      console.error('Error generating flow:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Animation for nodes appearing
  useEffect(() => {
    if (!showEmptyState && nodes.length > 0) {
      const nodeElements = document.querySelectorAll('.flow-node')
      nodeElements.forEach((el, index) => {
        setTimeout(() => {
          (el as HTMLElement).style.opacity = '1'
          ;(el as HTMLElement).style.transform = 'translateY(0)'
        }, index * 100)
      })
    }
  }, [showEmptyState, nodes, activeTab]) // Add activeTab as dependency

  // Add useEffect to preserve nodes when switching tabs
  useEffect(() => {
    // When switching back to user-flow tab, ensure nodes are visible
    if (activeTab === 'user-flow' && nodes.length > 0) {
      setTimeout(() => {
        const nodeElements = document.querySelectorAll('.flow-node')
        nodeElements.forEach((el) => {
          (el as HTMLElement).style.opacity = '1'
          ;(el as HTMLElement).style.transform = 'translateY(0)'
        })
      }, 100)
    }
  }, [activeTab])

  // Modify the tab switching logic to preserve node state
  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    
    // If switching away from user-flow, save the current state
    if (activeTab === 'user-flow' && tab !== 'user-flow') {
      // We don't need to do anything here as the state is already preserved
    }
    
    // If switching back to user-flow, restore the nodes
    if (tab === 'user-flow' && nodes.length > 0 && !showEmptyState) {
      // The useEffect above will handle this
    }
  }

  // New function to analyze project based on six core pillars
  const analyzeProject = () => {
    setIsAnalyzing(true);
    
    // This would be a real API call in production
    // Simulate API delay
    setTimeout(() => {
      // Generate mock analysis based on the six core pillars
      const analysis: ProjectAnalysis = {
        uniqueness: Math.floor(Math.random() * 40) + 60, // 60-100
        stickiness: Math.floor(Math.random() * 40) + 60,
        growthPotential: Math.floor(Math.random() * 40) + 60,
        pricingModel: Math.floor(Math.random() * 40) + 60,
        upsellPotential: Math.floor(Math.random() * 40) + 60,
        customerPurchasingPower: Math.floor(Math.random() * 40) + 60,
        overallScore: Math.floor(Math.random() * 20) + 75, // 75-95
        improvements: [
          "Add social sharing features to increase viral growth",
          "Implement a tiered pricing model to capture different market segments",
          "Consider adding a freemium option to increase user acquisition",
          "Develop a mobile app to increase stickiness and daily usage"
        ],
        coreFeatures: [
          "User authentication and profiles",
          "Dashboard with analytics",
          "Content management system",
          "Payment processing integration",
          "Notification system"
        ],
        techStack: [
          "Next.js for frontend",
          "Node.js backend with Express",
          "PostgreSQL database",
          "Redis for caching",
          "AWS for hosting"
        ],
        suggestedPricing: "$29/mo for basic, $79/mo for pro, $199/mo for enterprise"
      };
      
      setProjectAnalysis(analysis);
      
      // Generate user flow nodes based on analysis
      const generatedNodes: FlowNode[] = [
        { 
          id: '1', 
          type: 'screen', 
          label: 'LANDING PAGE', 
          x: 400, 
          y: 50, 
          connections: ['2', '3'],
          details: [
            'Hero section with value proposition',
            'Feature showcase based on core features',
            'Pricing options',
            'Call to action'
          ],
          fixed: true,
          icon: 'home'
        },
        { 
          id: '2', 
          type: 'screen', 
          label: 'SIGN UP', 
          x: 200, 
          y: 300, 
          connections: ['4'],
          details: [
            'Simple registration form',
            'Social login options',
            'Free trial offer',
            'Terms acceptance'
          ],
          fixed: true,
          icon: 'user'
        },
        { 
          id: '3', 
          type: 'screen', 
          label: 'PRICING', 
          x: 600, 
          y: 300, 
          connections: ['2'],
          details: [
            'Tiered pricing options',
            'Feature comparison',
            'FAQ section',
            'Money-back guarantee'
          ],
          fixed: true,
          icon: 'category'
        },
        { 
          id: '4', 
          type: 'screen', 
          label: 'DASHBOARD', 
          x: 200, 
          y: 550, 
          connections: ['5'],
          details: [
            'User statistics',
            'Quick actions',
            'Recent activity',
            'Upgrade prompts'
          ],
          fixed: true,
          icon: 'chart'
        },
        {
          id: '5',
          type: 'screen',
          label: 'SETTINGS',
          x: 600,
          y: 550,
          connections: [],
          details: [
            'Account management',
            'Subscription details',
            'Payment methods',
            'Notification preferences'
          ],
          fixed: true,
          icon: 'user'
        }
      ];
      
      setNodes(generatedNodes);
      setShowEmptyState(false);
      setIsEditing(true);
      
      // Generate analysis points based on the flow
      const points = [
        `Uniqueness score: ${analysis.uniqueness}% - The project has distinctive features in the market`,
        `Stickiness score: ${analysis.stickiness}% - Users are likely to return regularly`,
        `Growth potential: ${analysis.growthPotential}% - Strong opportunity for user base expansion`,
        `Overall feasibility: ${analysis.overallScore}% - Project has good chances of success`
      ];
      setAnalysisPoints(points);
      
      setIsAnalyzing(false);
    }, 2000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()} className="text-slate-400 hover:text-white">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-white">User Flow Designer</h1>
            <p className="text-slate-400 mt-1">Create and manage user flows for your project</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-800">
            <Share className="w-4 h-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-800">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          {!showEmptyState && (
            <Button 
              variant="outline" 
              size="sm" 
              className="border-slate-600 text-purple-300 hover:bg-slate-800"
              onClick={analyzeUserFlow}
              disabled={isAnalyzing}
            >
              <Sparkles className="w-4 h-4 mr-2" />
              {isAnalyzing ? "Analyzing..." : "Analyze Flow"}
            </Button>
          )}
          {isEditing ? (
            <Button
              onClick={handleSaveFlow}
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
            >
              <Save className="w-4 h-4 mr-2" />
              Save Flow
            </Button>
          ) : (
          <Button
              onClick={() => setIsEditing(true)}
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Flow
            </Button>
          )}
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-slate-700">
        <div className="flex space-x-8">
          <button
            className={cn(
              "py-2 border-b-2 font-medium text-sm",
              activeTab === 'user-flow' 
                ? "border-blue-500 text-blue-500" 
                : "border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-400"
            )}
            onClick={() => setActiveTab('user-flow')}
          >
            User Flow
          </button>
          <button
            className={cn(
              "py-2 border-b-2 font-medium text-sm",
              activeTab === 'tasks-board' 
                ? "border-blue-500 text-blue-500" 
                : "border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-400"
            )}
            onClick={() => setActiveTab('tasks-board')}
          >
            Tasks Board
          </button>
          <button
            className={cn(
              "py-2 border-b-2 font-medium text-sm",
              activeTab === 'overview' 
                ? "border-blue-500 text-blue-500" 
                : "border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-400"
            )}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={cn(
              "py-2 border-b-2 font-medium text-sm",
              activeTab === 'memory-bank' 
                ? "border-blue-500 text-blue-500" 
                : "border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-400"
            )}
            onClick={() => setActiveTab('memory-bank')}
          >
            Memory Bank
          </button>
        </div>
      </div>

      {activeTab === 'user-flow' && (
        <>
          {/* Simple Project Info Banner */}
          {!showEmptyState && (
            <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-4 mb-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-white font-medium">Project: {projectIdea}</h3>
                  {projectAnalysis && (
                    <p className="text-slate-300 text-sm mt-1">
                      Overall Feasibility: <span className="text-blue-400 font-medium">{projectAnalysis.overallScore}%</span>
                    </p>
                  )}
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="border-slate-600 text-purple-300 hover:bg-slate-800"
                  onClick={() => setActiveTab('overview')}
                >
                  <BarChart4 className="w-4 h-4 mr-2" />
                  View Analysis
                </Button>
              </div>
            </div>
          )}

      {/* Flow Canvas */}
      <GradientCard variant="feature" className="min-h-[600px]">
            <GradientCardHeader className="flex justify-between items-center">
              <GradientCardTitle>User Flow</GradientCardTitle>
              {isEditing && !showEmptyState && (
                <div className="flex gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-800">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Node
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-56 bg-slate-800 border-slate-700 text-slate-300">
                      <div className="flex flex-col gap-2">
                        <Button 
                          variant="ghost" 
                          onClick={() => handleAddNode('screen')}
                          className="justify-start text-slate-300 hover:bg-slate-700"
                        >
                          <Badge className="bg-blue-500/20 text-blue-400 mr-2">Screen</Badge>
                          Add Screen
                        </Button>
                        <Button 
                          variant="ghost" 
                          onClick={() => handleAddNode('action')}
                          className="justify-start text-slate-300 hover:bg-slate-700"
                        >
                          <Badge className="bg-green-500/20 text-green-400 mr-2">Action</Badge>
                          Add Action
                        </Button>
                        <Button 
                          variant="ghost" 
                          onClick={() => handleAddNode('decision')}
                          className="justify-start text-slate-300 hover:bg-slate-700"
                        >
                          <Badge className="bg-yellow-500/20 text-yellow-400 mr-2">Decision</Badge>
                          Add Decision
                        </Button>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              )}
        </GradientCardHeader>
        <GradientCardContent className="h-full">
              {showEmptyState ? (
                <div className="flex flex-col items-center justify-center h-96 border-2 border-dashed border-slate-600 rounded-lg p-6">
                  <div className="text-center max-w-md">
              <div className="text-slate-400 mb-4">
                <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
                    <h3 className="text-lg font-medium text-white mb-4">AI is Analyzing Your Project</h3>
                    <p className="text-slate-400 mb-6">
                      We're automatically analyzing your project idea to generate an optimal user flow.
                    </p>
                    
                    <div className="space-y-4">
                      {projectIdea ? (
                        <div className="bg-slate-800 border border-slate-700 rounded-lg p-4">
                          <h4 className="text-white text-sm font-medium mb-2">Project Idea:</h4>
                          <p className="text-slate-300 text-sm">{projectIdea}</p>
                        </div>
                      ) : (
                        <Textarea
                          value={projectDescription}
                          onChange={(e) => setProjectDescription(e.target.value)}
                          placeholder="Describe your project (e.g., 'A job board website with user profiles, forum, and category pages')"
                          className="bg-slate-800 border-slate-700 text-white min-h-[100px]"
                        />
                      )}
                      
                      <div className="flex gap-3">
                        {isAnalyzing ? (
                          <Button
                            disabled
                            className="bg-gradient-to-r from-blue-500 to-purple-600 w-full"
                          >
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Analyzing Project...
                          </Button>
                        ) : projectIdea ? (
                          <Button
                            onClick={analyzeProject}
                            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 w-full"
                          >
                            <Sparkles className="w-4 h-4 mr-2" />
                            Generate User Flow
                          </Button>
                        ) : (
                          <Button
                            onClick={generateFlowFromDescription}
                            disabled={isGenerating || !projectDescription.trim()}
                            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 w-full"
                          >
                            {isGenerating ? (
                              <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Generating...
                              </>
                            ) : (
                              <>
                                <Sparkles className="w-4 h-4 mr-2" />
                                Generate with AI
                              </>
                            )}
                          </Button>
                        )}
                        
                        <Button
                          onClick={createInitialFlow}
                          variant="outline"
                          className="border-slate-600 text-slate-300 hover:bg-slate-800"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Use Template
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div 
                  ref={canvasRef}
                  className="relative h-[800px] bg-slate-900 border border-slate-700 rounded-lg overflow-auto"
                  style={{
                    backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'100\' height=\'100\' viewBox=\'0 0 100 100\'%3E%3Cg fill-rule=\'evenodd\'%3E%3Cg fill=\'%232d374b\' fill-opacity=\'0.2\'%3E%3Cpath opacity=\'.5\' d=\'M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9z\' stroke=\'currentColor\' stroke-width=\'0.5\' stroke-linecap=\'round\' stroke-linejoin=\'round\'/></svg>")'
                  }}
                  onClick={handleCanvasClick}
                  onMouseMove={handleCanvasMouseMove}
                  onMouseUp={handleCanvasMouseUp}
                >
                  {/* Connection lines */}
                  <svg className="absolute inset-0 w-full h-full pointer-events-none">
                    {nodes.map(node => 
                      node.connections.map(targetId => {
                        const target = nodes.find(n => n.id === targetId);
                        if (!target) return null;
                        
                        // Calculate line coordinates for right-angled arrows
                        const x1 = node.x + 250; // right edge of node
                        const y1 = node.y + 40; // middle of node height
                        const x2 = target.x; // left edge of target
                        const y2 = target.y + 40; // middle of target height

                        // Create a path with right angles and straight dotted lines
                        const midX = x1 + (x2 - x1) / 2;
                        const path = `M${x1},${y1} L${midX},${y1} L${midX},${y2} L${x2},${y2}`;
                        
                        return (
                          <path
                            key={`${node.id}-${targetId}`}
                            d={path}
                            stroke="#6366f1"
                            strokeWidth="3"
                            strokeDasharray="8,4"
                            fill="none"
                          />
                        );
                      })
                    )}
                    
                    {/* Active connection line */}
                    {connectingFrom && (
                      <line
                        x1={nodes.find(n => n.id === connectingFrom)?.x ? (nodes.find(n => n.id === connectingFrom)!.x + 125) : 0}
                        y1={nodes.find(n => n.id === connectingFrom)?.y ? (nodes.find(n => n.id === connectingFrom)!.y + 40) : 0}
                        x2={dragOffset.x}
                        y2={dragOffset.y}
                        stroke="#6366f1"
                        strokeWidth="2"
                        strokeDasharray="4,4"
                      />
                    )}
                  </svg>
                  
                  {/* Nodes */}
                  {nodes.map(node => (
                    <div
                      key={node.id}
                      className={cn(
                        "flow-node absolute p-5 w-[250px] border-2 rounded-lg shadow-md transition-all duration-200 opacity-0 transform translate-y-4",
                        node.type === 'screen' ? "bg-slate-800 border-blue-500 text-white" : 
                        node.type === 'action' ? "bg-slate-800 border-green-500 text-white" : 
                        "bg-slate-800 border-yellow-500 text-white",
                        selectedNode === node.id && "ring-2 ring-purple-500 transform scale-105",
                        node.type === 'screen' && "hover:bg-blue-500/30",
                        node.type === 'action' && "hover:bg-green-500/30",
                        node.type === 'decision' && "hover:bg-yellow-500/30"
                      )}
                      style={{ 
                        left: `${node.x}px`, 
                        top: `${node.y}px`,
                      }}
                      onMouseDown={(e) => handleNodeMouseDown(e, node.id)}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          {node.icon && (
                            <div className="mr-2 text-slate-300">
                              {getNodeIcon(node.icon)}
                            </div>
                          )}
                          {editingLabel === node.id ? (
                            <Input
                              value={newLabel}
                              onChange={(e) => setNewLabel(e.target.value)}
                              onKeyDown={(e) => e.key === 'Enter' && handleSaveLabel()}
                              autoFocus
                              className="bg-slate-700 border-slate-600 text-white h-7 py-1 px-2"
                            />
                          ) : (
                            <h3 className="font-medium text-white">
                              {node.label}
                            </h3>
                          )}
                        </div>
                        <div className="flex space-x-1">
                          {editingLabel === node.id ? (
                            <Button 
                              size="icon" 
                              variant="ghost" 
                              className="h-6 w-6 text-slate-300 hover:text-white hover:bg-slate-700"
                              onClick={handleSaveLabel}
                            >
                              <Check className="h-3 w-3" />
                            </Button>
                          ) : (
                            <>
                              <Button 
                                size="icon" 
                                variant="ghost" 
                                className="h-6 w-6 text-slate-300 hover:text-white hover:bg-slate-700"
                                onClick={() => handleEditLabel(node.id)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              {!node.fixed && (
                                <Button 
                                  size="icon" 
                                  variant="ghost" 
                                  className="h-6 w-6 text-slate-300 hover:text-white hover:bg-slate-700"
                                  onClick={() => handleDeleteNode(node.id)}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                      
                      {node.details && node.details.length > 0 && (
                        <div className="space-y-1 mb-3">
                          {node.details.map((detail, idx) => (
                            <div key={idx} className="flex items-start text-xs">
                              <div className="bg-slate-700 text-slate-300 rounded-full w-4 h-4 flex items-center justify-center flex-shrink-0 mr-2 mt-0.5">
                                {idx + 1}
                              </div>
                              <p className="text-slate-300">{detail}</p>
                            </div>
                          ))}
                        </div>
                      )}
                      
                      <div className="flex justify-between mt-2">
                        <Badge 
                          className={cn(
                            "text-xs",
                            node.type === 'screen' ? "bg-blue-500/20 text-blue-400" : 
                            node.type === 'action' ? "bg-green-500/20 text-green-400" : 
                            "bg-yellow-500/20 text-yellow-400"
                          )}
                        >
                          {node.type}
                        </Badge>
                        
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 text-xs text-slate-300 hover:text-white hover:bg-slate-700"
                          onClick={(e) => handleStartConnection(e, node.id)}
                        >
                          <ArrowRight className="h-3 w-3 mr-1" />
                          Connect
                        </Button>
                      </div>
                      
                      {/* Connection target area */}
                      <div 
                        className="absolute top-0 left-0 right-0 h-4 -translate-y-4 cursor-pointer"
                        onClick={(e) => connectingFrom && handleCompleteConnection(e, node.id)}
                      />
                    </div>
                  ))}
                </div>
              )}
            </GradientCardContent>
          </GradientCard>
          
          {/* Simple Flow Analysis Summary */}
          {analysisPoints.length > 0 && !showEmptyState && (
            <div className="mt-6 bg-slate-800/50 border border-slate-700 rounded-lg p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-white font-medium">Flow Analysis Summary</h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="border-slate-600 text-purple-300 hover:bg-slate-800"
                  onClick={() => setActiveTab('overview')}
                >
                  <BarChart4 className="w-4 h-4 mr-2" />
                  View Full Analysis
                </Button>
              </div>
              <p className="text-slate-300 text-sm">
                {analysisPoints[0]}
              </p>
            </div>
          )}
        </>
      )}
      
      {activeTab === 'tasks-board' && (
        <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-6">
          <h3 className="text-xl font-medium text-white mb-4">Tasks Board Coming Soon</h3>
          <p className="text-slate-300">
            The tasks board will allow you to create and manage tasks related to your user flow.
            You'll be able to track progress, assign tasks to team members, and set deadlines.
          </p>
        </div>
      )}
      
      {activeTab === 'overview' && (
        <>
          {projectAnalysis ? (
            <div className="space-y-6">
              {/* Project Analysis Summary */}
              <GradientCard variant="feature">
                <GradientCardHeader>
                  <GradientCardTitle>Project Analysis</GradientCardTitle>
                </GradientCardHeader>
                <GradientCardContent>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-white font-medium mb-3">Project Idea</h3>
                      <p className="text-slate-300">{projectIdea}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-white font-medium mb-3">Feasibility Scores</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {/* Score Cards */}
                        <Card className="bg-slate-800/50 border border-slate-700">
                          <CardContent className="pt-6">
                            <div className="text-center">
                              <div className="text-3xl font-bold text-blue-400 mb-1">{projectAnalysis.uniqueness}%</div>
                              <p className="text-slate-300 text-sm">Uniqueness</p>
                            </div>
                          </CardContent>
                        </Card>
                        <Card className="bg-slate-800/50 border border-slate-700">
                          <CardContent className="pt-6">
                            <div className="text-center">
                              <div className="text-3xl font-bold text-green-400 mb-1">{projectAnalysis.stickiness}%</div>
                              <p className="text-slate-300 text-sm">Stickiness</p>
                            </div>
                          </CardContent>
                        </Card>
                        <Card className="bg-slate-800/50 border border-slate-700">
                          <CardContent className="pt-6">
                            <div className="text-center">
                              <div className="text-3xl font-bold text-purple-400 mb-1">{projectAnalysis.growthPotential}%</div>
                              <p className="text-slate-300 text-sm">Growth Potential</p>
                            </div>
                          </CardContent>
                        </Card>
                        <Card className="bg-slate-800/50 border border-slate-700">
                          <CardContent className="pt-6">
                            <div className="text-center">
                              <div className="text-3xl font-bold text-yellow-400 mb-1">{projectAnalysis.pricingModel}%</div>
                              <p className="text-slate-300 text-sm">Pricing Model</p>
                            </div>
                          </CardContent>
                        </Card>
                        <Card className="bg-slate-800/50 border border-slate-700">
                          <CardContent className="pt-6">
                            <div className="text-center">
                              <div className="text-3xl font-bold text-red-400 mb-1">{projectAnalysis.upsellPotential}%</div>
                              <p className="text-slate-300 text-sm">Upsell Potential</p>
                            </div>
                          </CardContent>
                        </Card>
                        <Card className="bg-slate-800/50 border border-slate-700">
                          <CardContent className="pt-6">
                            <div className="text-center">
                              <div className="text-3xl font-bold text-orange-400 mb-1">{projectAnalysis.customerPurchasingPower}%</div>
                              <p className="text-slate-300 text-sm">Customer Purchasing Power</p>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-white font-medium mb-3">Overall Feasibility</h3>
                        <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-slate-300">Score</span>
                            <span className="text-2xl font-bold text-white">{projectAnalysis.overallScore}%</span>
                          </div>
                          <div className="w-full bg-slate-700 rounded-full h-2.5">
                            <div 
                              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2.5 rounded-full" 
                              style={{ width: `${projectAnalysis.overallScore}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="text-white font-medium mb-3">Suggested Pricing</h3>
                        <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-4">
                          <p className="text-slate-300">{projectAnalysis.suggestedPricing}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </GradientCardContent>
              </GradientCard>

              {/* AI Flow Analysis */}
              {analysisPoints.length > 0 && (
                <GradientCard variant="feature">
                  <GradientCardHeader>
                    <div className="flex justify-between items-center">
                      <GradientCardTitle>AI Flow Analysis</GradientCardTitle>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="border-slate-600 text-purple-300 hover:bg-slate-800"
                        onClick={analyzeProject}
                        disabled={isAnalyzing}
                      >
                        <Sparkles className="w-4 h-4 mr-2" />
                        {isAnalyzing ? "Refreshing..." : "Refresh Analysis"}
                      </Button>
                    </div>
                  </GradientCardHeader>
                  <GradientCardContent>
                    <div className="text-slate-300 mb-4">
                      Here are the key insights from analyzing your project:
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {analysisPoints.map((point, index) => (
                        <Card key={index} className="bg-slate-800/50 border border-slate-700">
                          <CardContent className="pt-6">
                            <div className="flex gap-3">
                              <div className="bg-blue-500/20 text-blue-400 rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0">
                                {index + 1}
                              </div>
                              <p className="text-slate-300">{point}</p>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </GradientCardContent>
                </GradientCard>
              )}

              {/* Recommended Features */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <GradientCard variant="feature">
                  <GradientCardHeader>
                    <GradientCardTitle>Recommended Features</GradientCardTitle>
                  </GradientCardHeader>
                  <GradientCardContent>
                    <div className="space-y-3">
                      {projectAnalysis.coreFeatures.map((feature, index) => (
                        <div key={index} className="flex items-start">
                          <div className="bg-green-500/20 text-green-400 rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mr-3">
                            {index + 1}
                          </div>
                          <p className="text-slate-300">{feature}</p>
                        </div>
                      ))}
                    </div>
                  </GradientCardContent>
                </GradientCard>

                <GradientCard variant="feature">
                  <GradientCardHeader>
                    <GradientCardTitle>Suggested Tech Stack</GradientCardTitle>
                  </GradientCardHeader>
                  <GradientCardContent>
                    <div className="space-y-3">
                      {projectAnalysis.techStack.map((tech, index) => (
                        <div key={index} className="flex items-start">
                          <div className="bg-purple-500/20 text-purple-400 rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mr-3">
                            {index + 1}
                          </div>
                          <p className="text-slate-300">{tech}</p>
                        </div>
                      ))}
                    </div>
                  </GradientCardContent>
                </GradientCard>
              </div>

              {/* Improvement Suggestions */}
              <GradientCard variant="feature">
                <GradientCardHeader>
                  <GradientCardTitle>Improvement Suggestions</GradientCardTitle>
                </GradientCardHeader>
                <GradientCardContent>
                  <div className="space-y-3">
                    {projectAnalysis.improvements.map((improvement, index) => (
                      <div key={index} className="flex items-start">
                        <div className="bg-yellow-500/20 text-yellow-400 rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mr-3">
                          {index + 1}
                        </div>
                        <p className="text-slate-300">{improvement}</p>
                      </div>
                    ))}
                  </div>
                </GradientCardContent>
              </GradientCard>

              {/* Actions */}
              <div className="flex justify-end">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="border-slate-600 text-slate-300 hover:bg-slate-800 mr-2"
                  onClick={() => setActiveTab('user-flow')}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to User Flow
                </Button>
                <Button
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                  onClick={analyzeProject}
                  disabled={isAnalyzing}
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  {isAnalyzing ? "Analyzing..." : "Refresh Analysis"}
                </Button>
              </div>
            </div>
          ) : (
            <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-6">
              <h3 className="text-xl font-medium text-white mb-4">Project Overview</h3>
              <p className="text-slate-300 mb-6">
                No project analysis available yet. Please generate a user flow first.
              </p>
              <Button
                onClick={() => setActiveTab('user-flow')}
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go to User Flow
              </Button>
            </div>
          )}
        </>
      )}
      
      {activeTab === 'memory-bank' && (
        <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-6">
          <h3 className="text-xl font-medium text-white mb-4">Memory Bank Coming Soon</h3>
          <p className="text-slate-300">
            The memory bank will store important information about your project for easy reference.
            You'll be able to save notes, documents, and other resources related to your project.
          </p>
          </div>
      )}
    </div>
  )
}