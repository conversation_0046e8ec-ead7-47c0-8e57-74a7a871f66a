"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, BarChart3, Users, DollarSign, TrendingUp, Target, Sparkles, Download, Share } from "lucide-react"
import { GradientCard, GradientCardContent, GradientCardHeader, GradientCardTitle } from "@/components/ui/gradient-card"
import CountUp from "@/components/animations/count-up"
import SplitText from "@/components/animations/split-text"
import { useRouter } from "next/navigation"

interface ProjectAnalysisViewProps {
  projectId: string
}

// Mock data - in real app, this would come from API
const mockAnalysisData = {
  projectName: "E-commerce Platform",
  projectDescription:
    "A comprehensive e-commerce solution with advanced product recommendations and AI-powered customer insights",
  overallScore: 8.5,
  marketFeasibility: {
    pillars: [
      {
        name: "Uniqueness",
        score: 8,
        description: "Highly differentiated from existing solutions",
        icon: <Sparkles className="w-4 h-4" />,
      },
      {
        name: "Stickiness",
        score: 7,
        description: "Good user retention potential",
        icon: <Target className="w-4 h-4" />,
      },
      {
        name: "Growth Trend",
        score: 9,
        description: "Strong market growth trajectory",
        icon: <TrendingUp className="w-4 h-4" />,
      },
      {
        name: "Pricing Potential",
        score: 8,
        description: "Can command premium pricing",
        icon: <DollarSign className="w-4 h-4" />,
      },
      {
        name: "Upsell Potential",
        score: 6,
        description: "Moderate additional revenue opportunities",
        icon: <BarChart3 className="w-4 h-4" />,
      },
      {
        name: "Customer Purchasing Power",
        score: 8,
        description: "Target audience has strong purchasing power",
        icon: <Users className="w-4 h-4" />,
      },
    ],
  },
  suggestedImprovements: [
    "Consider adding AI-driven personalization features to increase user engagement",
    "Implement interactive product visualization for better user experience",
    "Add financial modeling tools to strengthen the value proposition",
    "Integrate social commerce features to leverage network effects",
  ],
  coreFeatures: [
    {
      name: "Product Catalog Management",
      description: "Comprehensive product management system",
      priority: "high" as const,
    },
    {
      name: "AI Recommendations",
      description: "Machine learning-powered product suggestions",
      priority: "high" as const,
    },
    { name: "Payment Processing", description: "Secure multi-gateway payment system", priority: "high" as const },
    {
      name: "Inventory Management",
      description: "Real-time inventory tracking and alerts",
      priority: "medium" as const,
    },
    { name: "Analytics Dashboard", description: "Comprehensive sales and user analytics", priority: "medium" as const },
  ],
  technicalRequirements: {
    frontend: ["Next.js", "React", "TypeScript", "Tailwind CSS"],
    backend: ["Node.js", "Express", "PostgreSQL", "Redis"],
    database: ["PostgreSQL", "Redis", "Elasticsearch"],
    infrastructure: ["AWS", "Docker", "Kubernetes", "CloudFront"],
  },
}

export default function ProjectAnalysisView({ projectId }: ProjectAnalysisViewProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-slate-700 rounded animate-pulse" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-64 bg-slate-700 rounded animate-pulse" />
          <div className="h-64 bg-slate-700 rounded animate-pulse" />
        </div>
        <div className="h-96 bg-slate-700 rounded animate-pulse" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()} className="text-slate-400 hover:text-white">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <SplitText text={mockAnalysisData.projectName} className="text-2xl font-bold text-white" delay={50} />
            <p className="text-slate-400 mt-1">Detailed AI Analysis</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-800">
            <Share className="w-4 h-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-800">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overall Score */}
      <GradientCard variant="feature">
        <GradientCardContent className="text-center py-8">
          <div className="text-4xl font-bold text-white mb-2">
            <CountUp to={mockAnalysisData.overallScore} duration={2} decimals={1} />
            <span className="text-2xl text-slate-400">/10</span>
          </div>
          <p className="text-slate-300">Overall Feasibility Score</p>
        </GradientCardContent>
      </GradientCard>

      {/* Market Feasibility Analysis */}
      <GradientCard variant="feature">
        <GradientCardHeader>
          <GradientCardTitle className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5" />
            <span>Market Feasibility Analysis</span>
          </GradientCardTitle>
        </GradientCardHeader>
        <GradientCardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {mockAnalysisData.marketFeasibility.pillars.map((pillar) => (
              <div key={pillar.name} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {pillar.icon}
                    <span className="font-medium text-white">{pillar.name}</span>
                  </div>
                  <span className="text-blue-400 font-medium">{pillar.score}/10</span>
                </div>
                <Progress value={pillar.score * 10} className="h-3" />
                <p className="text-sm text-slate-400">{pillar.description}</p>
              </div>
            ))}
          </div>
        </GradientCardContent>
      </GradientCard>

      {/* Suggested Improvements */}
      <GradientCard variant="feature">
        <GradientCardHeader>
          <GradientCardTitle>Suggested Improvements</GradientCardTitle>
        </GradientCardHeader>
        <GradientCardContent>
          <ul className="space-y-3">
            {mockAnalysisData.suggestedImprovements.map((improvement, index) => (
              <li key={index} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-slate-300">{improvement}</span>
              </li>
            ))}
          </ul>
        </GradientCardContent>
      </GradientCard>

      {/* Core Features */}
      <GradientCard variant="feature">
        <GradientCardHeader>
          <GradientCardTitle>Core Features</GradientCardTitle>
        </GradientCardHeader>
        <GradientCardContent>
          <div className="space-y-4">
            {mockAnalysisData.coreFeatures.map((feature) => (
              <div key={feature.name} className="flex items-start space-x-3 p-3 rounded-lg bg-slate-800/50">
                <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-white text-xs">✓</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-white">{feature.name}</span>
                    <Badge
                      variant="outline"
                      className={
                        feature.priority === "high"
                          ? "bg-red-500/20 text-red-400 border-red-500/30"
                          : feature.priority === "medium"
                            ? "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
                            : "bg-green-500/20 text-green-400 border-green-500/30"
                      }
                    >
                      {feature.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-slate-400">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </GradientCardContent>
      </GradientCard>

      {/* Technical Requirements */}
      <GradientCard variant="feature">
        <GradientCardHeader>
          <GradientCardTitle>Technical Requirements</GradientCardTitle>
        </GradientCardHeader>
        <GradientCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Object.entries(mockAnalysisData.technicalRequirements).map(([category, technologies]) => (
              <div key={category}>
                <h4 className="font-medium text-white capitalize mb-3">{category}</h4>
                <div className="flex flex-wrap gap-2">
                  {technologies.map((tech) => (
                    <Badge key={tech} variant="outline" className="border-slate-600 text-slate-300">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </GradientCardContent>
      </GradientCard>
    </div>
  )
}
