"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Plus, Filter, Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { <PERSON>radientCard, GradientCardContent, <PERSON>radientCardHeader, GradientCardTitle } from "@/components/ui/gradient-card"
import { useRouter } from "next/navigation"

interface KanbanBoardProps {
  projectId: string
}

const mockColumns = [
  { id: "todo", title: "To Do", tasks: 5 },
  { id: "inprogress", title: "In Progress", tasks: 3 },
  { id: "review", title: "Review", tasks: 2 },
  { id: "done", title: "Done", tasks: 8 },
]

export default function KanbanBoard({ projectId }: KanbanBoardProps) {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()} className="text-slate-400 hover:text-white">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-white">Tasks Board</h1>
            <p className="text-slate-400 mt-1">Manage project tasks with Kanban board</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-800">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
            <Plus className="w-4 h-4 mr-2" />
            Add Task
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
        <Input
          placeholder="Search tasks..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 bg-slate-800 border-slate-600 text-white placeholder:text-slate-400"
        />
      </div>

      {/* Kanban Board */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {mockColumns.map((column) => (
          <GradientCard key={column.id} variant="feature" className="h-fit">
            <GradientCardHeader>
              <GradientCardTitle className="flex items-center justify-between">
                <span>{column.title}</span>
                <span className="text-sm bg-slate-700 px-2 py-1 rounded">{column.tasks}</span>
              </GradientCardTitle>
            </GradientCardHeader>
            <GradientCardContent>
              <div className="space-y-3 min-h-[400px]">
                {/* Placeholder for tasks */}
                <div className="flex items-center justify-center h-32 border-2 border-dashed border-slate-600 rounded-lg">
                  <div className="text-center">
                    <p className="text-slate-400 text-sm">Drop tasks here</p>
                    <Button variant="ghost" size="sm" className="mt-2 text-slate-400 hover:text-white">
                      <Plus className="w-4 h-4 mr-1" />
                      Add Task
                    </Button>
                  </div>
                </div>
              </div>
            </GradientCardContent>
          </GradientCard>
        ))}
      </div>
    </div>
  )
}
