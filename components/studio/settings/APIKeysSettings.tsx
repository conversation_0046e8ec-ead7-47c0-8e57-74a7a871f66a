"use client";
import { useEffect, useState } from "react";
import { createBrowserClient } from "@supabase/ssr";

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

function generateApiKey() {
  // Simple random key generator (for demo)
  return (
    Math.random().toString(36).slice(2) +
    Math.random().toString(36).slice(2) +
    Math.random().toString(36).slice(2)
  ).slice(0, 40);
}

export default function APIKeysSettings() {
  const [apiKeys, setApiKeys] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [creating, setCreating] = useState(false);
  const [description, setDescription] = useState("");
  const [success, setSuccess] = useState(false);

  async function fetchApiKeys() {
    setLoading(true);
    setError(null);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Not authenticated");
      const { data, error } = await supabase
        .from("api_keys")
        .select("id, key, description, created_at, last_used")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });
      if (error) throw error;
      setApiKeys(data || []);
    } catch (err: any) {
      setError(err.message || "Failed to load API keys");
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchApiKeys();
  }, []);

  async function handleCreateKey() {
    setCreating(true);
    setError(null);
    setSuccess(false);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Not authenticated");
      const newKey = generateApiKey();
      const { error } = await supabase
        .from("api_keys")
        .insert({ user_id: user.id, key: newKey, description });
      if (error) throw error;
      setDescription("");
      setSuccess(true);
      fetchApiKeys();
    } catch (err: any) {
      setError(err.message || "Failed to create API key");
    } finally {
      setCreating(false);
      setTimeout(() => setSuccess(false), 2000);
    }
  }

  async function handleDeleteKey(id: string) {
    setError(null);
    try {
      const { error } = await supabase.from("api_keys").delete().eq("id", id);
      if (error) throw error;
      fetchApiKeys();
    } catch (err: any) {
      setError(err.message || "Failed to delete API key");
    }
  }

  if (loading) return <div className="p-6 text-slate-400">Loading API keys...</div>;
  if (error) return <div className="p-6 text-red-500">{error}</div>;

  return (
    <div className="space-y-8 max-w-xl bg-slate-900 rounded-xl p-8 shadow-lg border border-slate-800">
      <div className="space-y-2">
        <div className="text-lg font-bold text-white">API Keys</div>
        <div className="text-slate-400 text-sm">Generate and manage your API keys for integrations.</div>
      </div>
      <div className="space-y-4">
        <input
          type="text"
          className="w-full bg-slate-800 text-white rounded px-3 py-2 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-600 placeholder:text-slate-500"
          value={description}
          onChange={e => setDescription(e.target.value)}
          placeholder="Description (optional)"
        />
        <button
          onClick={handleCreateKey}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold shadow focus:outline-none focus:ring-2 focus:ring-blue-400"
          disabled={creating}
        >
          {creating ? "Creating..." : "Generate API Key"}
        </button>
        {success && <div className="text-green-500 mt-2">API key created!</div>}
      </div>
      <div className="mt-8">
        <div className="text-white font-semibold mb-2">Your API Keys</div>
        {apiKeys.length === 0 ? (
          <div className="text-slate-400">No API keys found.</div>
        ) : (
          <ul className="divide-y divide-slate-700">
            {apiKeys.map((key: any) => (
              <li key={key.id} className="py-3 flex items-center justify-between">
                <div>
                  <div className="text-white font-mono text-sm">{key.key}</div>
                  <div className="text-slate-400 text-xs">{key.description || "No description"}</div>
                  <div className="text-slate-500 text-xs">Created: {new Date(key.created_at).toLocaleString()}</div>
                  {key.last_used && <div className="text-slate-500 text-xs">Last Used: {new Date(key.last_used).toLocaleString()}</div>}
                </div>
                <button
                  onClick={() => handleDeleteKey(key.id)}
                  className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-xs font-semibold shadow focus:outline-none focus:ring-2 focus:ring-red-400"
                >
                  Delete
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
} 