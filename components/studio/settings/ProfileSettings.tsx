"use client";
import { useEffect, useState, useRef } from "react";
import { createBrowserClient } from "@supabase/ssr";

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function ProfileSettings() {
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [avatarUploading, setAvatarUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    async function fetchProfile() {
      setLoading(true);
      setError(null);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error("Not authenticated");
        const { data, error } = await supabase
          .from("user_profiles")
          .select("*")
          .eq("id", user.id)
          .single();
        if (error) throw error;
        setProfile(data);
      } catch (err: any) {
        setError(err.message || "Failed to load profile");
      } finally {
        setLoading(false);
      }
    }
    fetchProfile();
  }, []);

  async function handleSave() {
    setSaving(true);
    setError(null);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Not authenticated");
      const { error } = await supabase
        .from("user_profiles")
        .upsert({
          id: user.id,
          full_name: profile.full_name,
          bio: profile.bio,
          avatar_url: profile.avatar_url,
          company: profile.company,
          role: profile.role,
        });
      if (error) throw error;
    } catch (err: any) {
      setError(err.message || "Failed to save profile");
    } finally {
      setSaving(false);
    }
  }

  async function handleAvatarChange(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    setAvatarUploading(true);
    setError(null);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Not authenticated");
      const fileExt = file.name.split('.').pop();
      const filePath = `avatars/${user.id}.${fileExt}`;
      const { error: uploadError } = await supabase.storage.from("avatars").upload(filePath, file, { upsert: true });
      if (uploadError) throw uploadError;
      const { data } = supabase.storage.from("avatars").getPublicUrl(filePath);
      setProfile((p: any) => ({ ...p, avatar_url: data.publicUrl }));
    } catch (err: any) {
      setError(err.message || "Failed to upload avatar");
    } finally {
      setAvatarUploading(false);
    }
  }

  if (loading) return <div className="p-6 text-slate-400">Loading profile...</div>;
  if (error) return <div className="p-6 text-red-500">{error}</div>;

  return (
    <div className="space-y-6 max-w-xl bg-slate-900 rounded-xl p-8 shadow-lg border border-slate-800">
      <div className="flex items-center gap-6">
        <div className="relative w-20 h-20 flex items-center justify-center">
          <img
            src={profile.avatar_url || "/default-avatar.png"}
            alt="Avatar"
            className="w-20 h-20 rounded-full object-cover border-4 border-blue-500 shadow-lg bg-slate-800"
          />
          {!profile.avatar_url && !avatarUploading && (
            <span className="absolute inset-0 flex items-center justify-center text-slate-300 font-semibold text-sm pointer-events-none z-10">Avatar</span>
          )}
          <button
            className="absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-2 shadow hover:bg-blue-700 z-20 border-2 border-slate-900"
            onClick={() => fileInputRef.current?.click()}
            disabled={avatarUploading}
            style={{ minWidth: 40, minHeight: 40 }}
          >
            {avatarUploading ? "..." : "Edit"}
          </button>
          <input
            type="file"
            accept="image/*"
            ref={fileInputRef}
            className="hidden"
            onChange={handleAvatarChange}
          />
        </div>
        <div>
          <div className="text-xl font-bold text-white">{profile.full_name || "No Name"}</div>
        </div>
      </div>
      <div className="grid grid-cols-1 gap-6">
        <div>
          <label className="block text-sm font-medium mb-1 text-slate-300">Full Name</label>
          <input
            className="w-full bg-slate-800 text-white rounded px-3 py-2 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-600 placeholder:text-slate-500"
            value={profile.full_name || ""}
            onChange={e => setProfile((p: any) => ({ ...p, full_name: e.target.value }))}
            placeholder="Enter your name"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1 text-slate-300">Bio</label>
          <textarea
            className="w-full bg-slate-800 text-white rounded px-3 py-2 min-h-[80px] border border-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-600 placeholder:text-slate-500"
            value={profile.bio || ""}
            onChange={e => setProfile((p: any) => ({ ...p, bio: e.target.value }))}
            placeholder="Tell us about yourself"
          />
        </div>
      </div>
      <button
        onClick={handleSave}
        className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold mt-4 shadow focus:outline-none focus:ring-2 focus:ring-blue-400"
        disabled={saving}
      >
        {saving ? "Saving..." : "Save Profile"}
      </button>
    </div>
  );
} 