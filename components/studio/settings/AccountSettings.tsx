"use client";
import { useEffect, useState } from "react";
import { createBrowserClient } from "@supabase/ssr";

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function AccountSettings() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [password, setPassword] = useState("");
  const [passwordSaving, setPasswordSaving] = useState(false);
  const [passwordSuccess, setPasswordSuccess] = useState(false);
  // 2FA is not natively supported in Supabase UI, so we show a placeholder
  // If you use an external 2FA provider, integrate here

  useEffect(() => {
    async function fetchUser() {
      setLoading(true);
      setError(null);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error("Not authenticated");
        setUser(user);
      } catch (err: any) {
        setError(err.message || "Failed to load user");
      } finally {
        setLoading(false);
      }
    }
    fetchUser();
  }, []);

  async function handleChangePassword() {
    setPasswordSaving(true);
    setError(null);
    setPasswordSuccess(false);
    try {
      const { error } = await supabase.auth.updateUser({ password });
      if (error) throw error;
      setPasswordSuccess(true);
      setPassword("");
    } catch (err: any) {
      setError(err.message || "Failed to change password");
    } finally {
      setPasswordSaving(false);
    }
  }

  if (loading) return <div className="p-6 text-slate-400">Loading account...</div>;
  if (error) return <div className="p-6 text-red-500">{error}</div>;

  return (
    <div className="space-y-8 max-w-xl bg-slate-900 rounded-xl p-8 shadow-lg border border-slate-800">
      <div className="space-y-2">
        <div className="text-lg font-bold text-white">Account Security</div>
        <div className="text-slate-400 text-sm">Change your password and manage security settings.</div>
      </div>
      <div className="space-y-4">
        <label className="block text-sm font-medium mb-1 text-slate-300">New Password</label>
        <input
          type="password"
          className="w-full bg-slate-800 text-white rounded px-3 py-2 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-600 placeholder:text-slate-500"
          value={password}
          onChange={e => setPassword(e.target.value)}
          placeholder="Enter new password"
        />
        <button
          onClick={handleChangePassword}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold mt-2 shadow focus:outline-none focus:ring-2 focus:ring-blue-400"
          disabled={passwordSaving || !password}
        >
          {passwordSaving ? "Saving..." : "Change Password"}
        </button>
        {passwordSuccess && <div className="text-green-500 mt-2">Password changed successfully!</div>}
      </div>
      <div className="space-y-2 mt-8">
        <div className="text-lg font-bold text-white">Two-Factor Authentication (2FA)</div>
        <div className="text-slate-400 text-sm mb-2">2FA is not enabled. (Integrate with an external provider for production.)</div>
        <button className="bg-slate-700 text-white px-4 py-2 rounded-lg font-semibold cursor-not-allowed opacity-60" disabled>
          Enable 2FA (Coming Soon)
        </button>
      </div>
      <div className="space-y-2 mt-8">
        <div className="text-lg font-bold text-white">Account Info</div>
        <div className="text-slate-400 text-sm">Account Created: {user?.created_at ? new Date(user.created_at).toLocaleString() : "-"}</div>
        <div className="text-slate-400 text-sm">Last Sign In: {user?.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : "-"}</div>
        <div className="text-slate-400 text-sm">Email: {user?.email}</div>
      </div>
    </div>
  );
} 