"use client";
import { useEffect, useState } from "react";
import { createBrowserClient } from "@supabase/ssr";

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function NotificationSettings() {
  const [preferences, setPreferences] = useState<any>({ email: true, inApp: true });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    async function fetchPreferences() {
      setLoading(true);
      setError(null);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error("Not authenticated");
        const { data, error } = await supabase
          .from("user_profiles")
          .select("preferences")
          .eq("id", user.id)
          .single();
        if (error) throw error;
        setPreferences({ email: true, inApp: true, ...data?.preferences });
      } catch (err: any) {
        setError(err.message || "Failed to load preferences");
      } finally {
        setLoading(false);
      }
    }
    fetchPreferences();
  }, []);

  async function handleSave() {
    setSaving(true);
    setError(null);
    setSuccess(false);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Not authenticated");
      const { error } = await supabase
        .from("user_profiles")
        .update({ preferences })
        .eq("id", user.id);
      if (error) throw error;
      setSuccess(true);
    } catch (err: any) {
      setError(err.message || "Failed to save preferences");
    } finally {
      setSaving(false);
      setTimeout(() => setSuccess(false), 2000);
    }
  }

  if (loading) return <div className="p-6 text-slate-400">Loading notification settings...</div>;
  if (error) return <div className="p-6 text-red-500">{error}</div>;

  return (
    <div className="space-y-8 max-w-xl bg-slate-900 rounded-xl p-8 shadow-lg border border-slate-800">
      <div className="space-y-2">
        <div className="text-lg font-bold text-white">Notification Preferences</div>
        <div className="text-slate-400 text-sm">Manage your email and in-app notification settings.</div>
      </div>
      <div className="space-y-4">
        <label className="flex items-center gap-3 text-slate-300">
          <input
            type="checkbox"
            checked={preferences.email}
            onChange={e => setPreferences((p: any) => ({ ...p, email: e.target.checked }))}
            className="form-checkbox h-5 w-5 text-blue-600 bg-slate-800 border-slate-700 focus:ring-blue-600"
          />
          <span className="text-white">Enable Email Notifications</span>
        </label>
        <label className="flex items-center gap-3 text-slate-300">
          <input
            type="checkbox"
            checked={preferences.inApp}
            onChange={e => setPreferences((p: any) => ({ ...p, inApp: e.target.checked }))}
            className="form-checkbox h-5 w-5 text-blue-600 bg-slate-800 border-slate-700 focus:ring-blue-600"
          />
          <span className="text-white">Enable In-App Notifications</span>
        </label>
        <button
          onClick={handleSave}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold mt-2 shadow focus:outline-none focus:ring-2 focus:ring-blue-400"
          disabled={saving}
        >
          {saving ? "Saving..." : "Save Preferences"}
        </button>
        {success && <div className="text-green-500 mt-2">Preferences saved!</div>}
      </div>
    </div>
  );
} 