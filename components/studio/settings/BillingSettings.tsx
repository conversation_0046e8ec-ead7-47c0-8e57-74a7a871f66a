"use client";
import { useEffect, useState } from "react";
import { createBrowserClient } from "@supabase/ssr";

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function BillingSettings() {
  const [billing, setBilling] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchBilling() {
      setLoading(true);
      setError(null);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error("Not authenticated");
        const { data, error } = await supabase
          .from("billing_subscriptions")
          .select("plan, status, current_period_end, created_at, updated_at")
          .eq("user_id", user.id)
          .order("created_at", { ascending: false })
          .limit(1)
          .single();
        if (error && error.code !== 'PGRST116') throw error;
        setBilling(data);
      } catch (err: any) {
        setError(err.message || "Failed to load billing info");
      } finally {
        setLoading(false);
      }
    }
    fetchBilling();
  }, []);

  if (loading) return <div className="p-6 text-slate-400">Loading billing info...</div>;
  if (error) return <div className="p-6 text-red-500">{error}</div>;

  return (
    <div className="space-y-8 max-w-xl bg-slate-900 rounded-xl p-8 shadow-lg border border-slate-800">
      <div className="space-y-2">
        <div className="text-lg font-bold text-white">Billing & Subscription</div>
        <div className="text-slate-400 text-sm">Manage your subscription and view billing history.</div>
      </div>
      {billing ? (
        <div className="space-y-4">
          <div className="text-white">Plan: <span className="font-semibold">{billing.plan || "-"}</span></div>
          <div className="text-white">Status: <span className="font-semibold">{billing.status || "-"}</span></div>
          <div className="text-white">Current Period End: <span className="font-semibold">{billing.current_period_end ? new Date(billing.current_period_end).toLocaleString() : "-"}</span></div>
          <div className="text-slate-400 text-sm">Last Updated: {billing.updated_at ? new Date(billing.updated_at).toLocaleString() : "-"}</div>
        </div>
      ) : (
        <div className="text-slate-400">No billing subscription found.</div>
      )}
      {/* Billing history and management actions can be added here if available */}
    </div>
  );
} 