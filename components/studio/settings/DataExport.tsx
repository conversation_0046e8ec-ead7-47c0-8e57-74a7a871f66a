"use client";
import { useState } from "react";
import { createBrowserClient } from "@supabase/ssr";

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

function downloadJSON(data: any, filename: string) {
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  a.click();
  URL.revokeObjectURL(url);
}

export default function DataExport() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  async function handleExport() {
    setLoading(true);
    setError(null);
    setSuccess(false);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Not authenticated");
      const [profileRes, apiKeysRes, billingRes] = await Promise.all([
        supabase.from("user_profiles").select("*" ).eq("id", user.id).single(),
        supabase.from("api_keys").select("*" ).eq("user_id", user.id),
        supabase.from("billing_subscriptions").select("*" ).eq("user_id", user.id),
      ]);
      if (profileRes.error) throw profileRes.error;
      if (apiKeysRes.error) throw apiKeysRes.error;
      if (billingRes.error) throw billingRes.error;
      const exportData = {
        profile: profileRes.data,
        apiKeys: apiKeysRes.data,
        billing: billingRes.data,
      };
      downloadJSON(exportData, "my_saasifyx_data.json");
      setSuccess(true);
    } catch (err: any) {
      setError(err.message || "Failed to export data");
    } finally {
      setLoading(false);
      setTimeout(() => setSuccess(false), 2000);
    }
  }

  return (
    <div className="space-y-8 max-w-xl bg-slate-900 rounded-xl p-8 shadow-lg border border-slate-800">
      <div className="space-y-2">
        <div className="text-lg font-bold text-white">Export Your Data</div>
        <div className="text-slate-400 text-sm">Download all your profile, API key, and billing data as a JSON file.</div>
      </div>
      <button
        onClick={handleExport}
        className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold shadow focus:outline-none focus:ring-2 focus:ring-blue-400"
        disabled={loading}
      >
        {loading ? "Exporting..." : "Export My Data"}
      </button>
      {error && <div className="text-red-500 mt-2">{error}</div>}
      {success && <div className="text-green-500 mt-2">Data exported!</div>}
    </div>
  );
} 