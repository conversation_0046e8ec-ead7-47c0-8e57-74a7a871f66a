"use client"
import { useAIAnalysis } from "@/components/studio/ai-analysis/ai-analysis-provider"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Spark<PERSON> } from "lucide-react"

interface NewUserModalProps {
  userName: string
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function NewUserModal({ userName, open, onOpenChange }: NewUserModalProps) {
  const { openAnalysisModal } = useAIAnalysis()

  const handleGetStarted = () => {
    onOpenChange(false)
    openAnalysisModal()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-slate-900 border-slate-700 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">Welcome to SaaS Ideas, {userName}!</DialogTitle>
          <DialogDescription className="text-slate-300 text-center">
            Let's get started by creating your first SaaS blueprint
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex justify-center">
            <div className="w-24 h-24 rounded-full bg-blue-500/20 flex items-center justify-center">
              <Sparkles className="w-12 h-12 text-blue-400" />
            </div>
          </div>

          <div className="text-center space-y-2">
            <h3 className="font-medium text-lg">Generate your first AI blueprint</h3>
            <p className="text-sm text-slate-400">
              Our AI can analyze your SaaS idea and provide valuable insights on market feasibility, core features, and
              technical requirements.
            </p>
          </div>
        </div>

        <div className="flex justify-center pt-2">
          <Button
            onClick={handleGetStarted}
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-6 text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300 border-0 hover:scale-105"
          >
            <Sparkles className="w-5 h-5 mr-2" />
            Get Started
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
