"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { X, Filter, RotateCcw } from "lucide-react"
import { type UserFilters } from "@/lib/services/user-management-service"

interface UserFiltersPanelProps {
  filters: UserFilters
  onFiltersChange: (filters: UserFilters) => void
  onClose: () => void
}

export default function UserFiltersPanel({ 
  filters, 
  onFiltersChange, 
  onClose 
}: UserFiltersPanelProps) {
  const [localFilters, setLocalFilters] = useState<UserFilters>(filters)

  // Handle filter changes
  const handleFilterChange = (key: keyof UserFilters, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }))
  }

  // Apply filters
  const applyFilters = () => {
    onFiltersChange(localFilters)
    onClose()
  }

  // Reset filters
  const resetFilters = () => {
    const emptyFilters: UserFilters = {}
    setLocalFilters(emptyFilters)
    onFiltersChange(emptyFilters)
  }

  // Get today's date for date inputs
  const today = new Date().toISOString().split('T')[0]

  return (
    <Card className="p-6 bg-slate-800/50 border-slate-700">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
          <Filter className="w-5 h-5" />
          Filter Users
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-slate-400 hover:text-white hover:bg-slate-700/50"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Status Filter */}
        <div>
          <Label htmlFor="status-filter" className="text-slate-300 text-sm font-medium">
            Status
          </Label>
          <Select
            value={localFilters.status || ""}
            onValueChange={(value) => handleFilterChange('status', value)}
          >
            <SelectTrigger className="mt-1 bg-slate-700 border-slate-600 text-white">
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              <SelectItem value="">All statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Role Filter */}
        <div>
          <Label htmlFor="role-filter" className="text-slate-300 text-sm font-medium">
            Role
          </Label>
          <Select
            value={localFilters.role || ""}
            onValueChange={(value) => handleFilterChange('role', value)}
          >
            <SelectTrigger className="mt-1 bg-slate-700 border-slate-600 text-white">
              <SelectValue placeholder="All roles" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              <SelectItem value="">All roles</SelectItem>
              <SelectItem value="user">User</SelectItem>
              <SelectItem value="moderator">Moderator</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Date From Filter */}
        <div>
          <Label htmlFor="date-from-filter" className="text-slate-300 text-sm font-medium">
            Registered From
          </Label>
          <Input
            id="date-from-filter"
            type="date"
            value={localFilters.dateFrom || ""}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            max={today}
            className="mt-1 bg-slate-700 border-slate-600 text-white"
          />
        </div>

        {/* Date To Filter */}
        <div>
          <Label htmlFor="date-to-filter" className="text-slate-300 text-sm font-medium">
            Registered To
          </Label>
          <Input
            id="date-to-filter"
            type="date"
            value={localFilters.dateTo || ""}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            max={today}
            min={localFilters.dateFrom || ""}
            className="mt-1 bg-slate-700 border-slate-600 text-white"
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={resetFilters}
          className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          Reset Filters
        </Button>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50"
          >
            Cancel
          </Button>
          <Button
            onClick={applyFilters}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Apply Filters
          </Button>
        </div>
      </div>

      {/* Active Filters Display */}
      {Object.keys(localFilters).some(key => localFilters[key as keyof UserFilters]) && (
        <div className="mt-4 pt-4 border-t border-slate-700">
          <h4 className="text-sm font-medium text-slate-300 mb-2">Active Filters:</h4>
          <div className="flex flex-wrap gap-2">
            {localFilters.status && (
              <div className="flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded text-xs">
                Status: {localFilters.status}
                <button
                  onClick={() => handleFilterChange('status', '')}
                  className="ml-1 hover:text-blue-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
            
            {localFilters.role && (
              <div className="flex items-center gap-1 px-2 py-1 bg-purple-500/20 text-purple-400 border border-purple-500/30 rounded text-xs">
                Role: {localFilters.role}
                <button
                  onClick={() => handleFilterChange('role', '')}
                  className="ml-1 hover:text-purple-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
            
            {localFilters.dateFrom && (
              <div className="flex items-center gap-1 px-2 py-1 bg-green-500/20 text-green-400 border border-green-500/30 rounded text-xs">
                From: {new Date(localFilters.dateFrom).toLocaleDateString()}
                <button
                  onClick={() => handleFilterChange('dateFrom', '')}
                  className="ml-1 hover:text-green-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
            
            {localFilters.dateTo && (
              <div className="flex items-center gap-1 px-2 py-1 bg-orange-500/20 text-orange-400 border border-orange-500/30 rounded text-xs">
                To: {new Date(localFilters.dateTo).toLocaleDateString()}
                <button
                  onClick={() => handleFilterChange('dateTo', '')}
                  className="ml-1 hover:text-orange-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </Card>
  )
}
