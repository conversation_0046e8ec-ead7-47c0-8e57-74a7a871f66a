"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  User as UserIcon, 
  Mail, 
  Phone, 
  Building, 
  MapPin, 
  Globe, 
  Calendar,
  Activity,
  Settings,
  Save,
  X
} from "lucide-react"
import {
  updateUser,
  getUserActivity,
  type User,
  type UserActivity
} from "@/lib/services/user-management-service"
import { useCurrentUser } from "@/hooks/use-current-user"
import { toast } from "sonner"

interface UserDetailsModalProps {
  user: User
  open: boolean
  onOpenChange: (open: boolean) => void
  onUserUpdated: () => void
}

export default function UserDetailsModal({
  user,
  open,
  onOpenChange,
  onUserUpdated
}: UserDetailsModalProps) {
  const [editMode, setEditMode] = useState(false)
  const [loading, setLoading] = useState(false)
  const [activities, setActivities] = useState<UserActivity[]>([])
  const [activitiesLoading, setActivitiesLoading] = useState(false)
  const [formData, setFormData] = useState<Partial<User>>({})

  // Get current user permissions
  const { user: currentUser, canManageUsers, isAdmin } = useCurrentUser()

  // Check if this is the user's own profile
  const isOwnProfile = currentUser?.id === user?.id

  // Check if user can edit this profile
  const canEdit = isOwnProfile || canManageUsers

  // Initialize form data when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email,
        phone: user.phone || '',
        company: user.company || '',
        job_title: user.job_title || '',
        bio: user.bio || '',
        location: user.location || '',
        website: user.website || '',
        status: user.status,
        role: user.role,
      })
    }
  }, [user])

  // Load user activities
  const loadActivities = async () => {
    if (!user) return
    
    try {
      setActivitiesLoading(true)
      const result = await getUserActivity(user.id, 1, 10)
      setActivities(result.activities)
    } catch (error) {
      console.error('Error loading activities:', error)
      toast.error('Failed to load user activities')
    } finally {
      setActivitiesLoading(false)
    }
  }

  useEffect(() => {
    if (open && user) {
      loadActivities()
    }
  }, [open, user])

  // Handle form submission
  const handleSave = async () => {
    if (!user) return

    try {
      setLoading(true)
      await updateUser(user.id, formData)
      toast.success('User updated successfully')
      setEditMode(false)
      onUserUpdated()
    } catch (error) {
      toast.error('Failed to update user')
    } finally {
      setLoading(false)
    }
  }

  // Handle input changes
  const handleInputChange = (field: keyof User, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Get status badge
  const getStatusBadge = (status: User['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">Active</Badge>
      case 'suspended':
        return <Badge variant="destructive">Suspended</Badge>
      case 'pending':
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">Pending</Badge>
      case 'inactive':
        return <Badge variant="outline">Inactive</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Get role badge
  const getRoleBadge = (role: User['role']) => {
    switch (role) {
      case 'admin':
        return <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">Admin</Badge>
      case 'moderator':
        return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">Moderator</Badge>
      case 'user':
        return <Badge variant="outline">User</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  if (!user) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-900 border-slate-700">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold text-white flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold">
                {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
              </div>
              <div>
                <div className="flex items-center gap-2">
                  {user.name || 'No name'}
                  {getStatusBadge(user.status)}
                  {getRoleBadge(user.role)}
                </div>
                <p className="text-sm text-slate-400 font-normal">{user.email}</p>
              </div>
            </DialogTitle>
            <div className="flex items-center gap-2">
              {canEdit && (
                <>
                  {editMode ? (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditMode(false)}
                        className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50"
                      >
                        <X className="w-4 h-4 mr-1" />
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={handleSave}
                        disabled={loading}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Save className="w-4 h-4 mr-1" />
                        {loading ? 'Saving...' : 'Save'}
                      </Button>
                    </>
                  ) : (
                    <Button
                      size="sm"
                      onClick={() => setEditMode(true)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Settings className="w-4 h-4 mr-1" />
                      {isOwnProfile ? 'Edit Profile' : 'Edit User'}
                    </Button>
                  )}
                </>
              )}
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="profile" className="mt-6">
          <TabsList className="grid w-full grid-cols-3 bg-slate-800 border-slate-700">
            <TabsTrigger value="profile" className="data-[state=active]:bg-slate-700 data-[state=active]:text-white">
              <UserIcon className="w-4 h-4 mr-2" />
              Profile
            </TabsTrigger>
            <TabsTrigger value="activity" className="data-[state=active]:bg-slate-700 data-[state=active]:text-white">
              <Activity className="w-4 h-4 mr-2" />
              Activity
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-slate-700 data-[state=active]:text-white">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <Card className="p-6 bg-slate-800/50 border-slate-700">
                <h3 className="text-lg font-semibold text-white mb-4">Basic Information</h3>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name" className="text-slate-300">Full Name</Label>
                    {editMode ? (
                      <Input
                        id="name"
                        value={formData.name || ''}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="mt-1 bg-slate-700 border-slate-600 text-white"
                      />
                    ) : (
                      <p className="mt-1 text-white">{user.name || 'Not provided'}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="email" className="text-slate-300">Email</Label>
                    {editMode ? (
                      <Input
                        id="email"
                        type="email"
                        value={formData.email || ''}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="mt-1 bg-slate-700 border-slate-600 text-white"
                      />
                    ) : (
                      <p className="mt-1 text-white flex items-center gap-2">
                        <Mail className="w-4 h-4" />
                        {user.email}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="phone" className="text-slate-300">Phone</Label>
                    {editMode ? (
                      <Input
                        id="phone"
                        value={formData.phone || ''}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="mt-1 bg-slate-700 border-slate-600 text-white"
                      />
                    ) : (
                      <p className="mt-1 text-white flex items-center gap-2">
                        <Phone className="w-4 h-4" />
                        {user.phone || 'Not provided'}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="location" className="text-slate-300">Location</Label>
                    {editMode ? (
                      <Input
                        id="location"
                        value={formData.location || ''}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        className="mt-1 bg-slate-700 border-slate-600 text-white"
                      />
                    ) : (
                      <p className="mt-1 text-white flex items-center gap-2">
                        <MapPin className="w-4 h-4" />
                        {user.location || 'Not provided'}
                      </p>
                    )}
                  </div>
                </div>
              </Card>

              {/* Professional Information */}
              <Card className="p-6 bg-slate-800/50 border-slate-700">
                <h3 className="text-lg font-semibold text-white mb-4">Professional Information</h3>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="company" className="text-slate-300">Company</Label>
                    {editMode ? (
                      <Input
                        id="company"
                        value={formData.company || ''}
                        onChange={(e) => handleInputChange('company', e.target.value)}
                        className="mt-1 bg-slate-700 border-slate-600 text-white"
                      />
                    ) : (
                      <p className="mt-1 text-white flex items-center gap-2">
                        <Building className="w-4 h-4" />
                        {user.company || 'Not provided'}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="job_title" className="text-slate-300">Job Title</Label>
                    {editMode ? (
                      <Input
                        id="job_title"
                        value={formData.job_title || ''}
                        onChange={(e) => handleInputChange('job_title', e.target.value)}
                        className="mt-1 bg-slate-700 border-slate-600 text-white"
                      />
                    ) : (
                      <p className="mt-1 text-white">{user.job_title || 'Not provided'}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="website" className="text-slate-300">Website</Label>
                    {editMode ? (
                      <Input
                        id="website"
                        value={formData.website || ''}
                        onChange={(e) => handleInputChange('website', e.target.value)}
                        className="mt-1 bg-slate-700 border-slate-600 text-white"
                      />
                    ) : (
                      <p className="mt-1 text-white flex items-center gap-2">
                        <Globe className="w-4 h-4" />
                        {user.website ? (
                          <a href={user.website} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">
                            {user.website}
                          </a>
                        ) : (
                          'Not provided'
                        )}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="bio" className="text-slate-300">Bio</Label>
                    {editMode ? (
                      <Textarea
                        id="bio"
                        value={formData.bio || ''}
                        onChange={(e) => handleInputChange('bio', e.target.value)}
                        className="mt-1 bg-slate-700 border-slate-600 text-white"
                        rows={3}
                      />
                    ) : (
                      <p className="mt-1 text-white">{user.bio || 'Not provided'}</p>
                    )}
                  </div>
                </div>
              </Card>

              {/* Account Information */}
              <Card className="p-6 bg-slate-800/50 border-slate-700 md:col-span-2">
                <h3 className="text-lg font-semibold text-white mb-4">Account Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label className="text-slate-300">Status</Label>
                    {editMode && canManageUsers && !isOwnProfile ? (
                      <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                        <SelectTrigger className="mt-1 bg-slate-700 border-slate-600 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="mt-1">{getStatusBadge(user.status)}</div>
                    )}
                  </div>

                  <div>
                    <Label className="text-slate-300">Role</Label>
                    {editMode && isAdmin && !isOwnProfile ? (
                      <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
                        <SelectTrigger className="mt-1 bg-slate-700 border-slate-600 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          <SelectItem value="user">User</SelectItem>
                          <SelectItem value="moderator">Moderator</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="mt-1">{getRoleBadge(user.role)}</div>
                    )}
                  </div>

                  <div>
                    <Label className="text-slate-300">Member Since</Label>
                    <p className="mt-1 text-white flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      {new Date(user.created_at).toLocaleDateString()}
                    </p>
                  </div>

                  {/* Show who created this user */}
                  {user.created_by_email && (
                    <div>
                      <Label className="text-slate-300">Added By</Label>
                      <p className="mt-1 text-white flex items-center gap-2">
                        <UserIcon className="w-4 h-4" />
                        {user.created_by_name || user.created_by_email}
                        {user.created_by_email !== user.email && (
                          <span className="text-xs text-slate-400">({user.created_by_email})</span>
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="mt-6">
            <Card className="p-6 bg-slate-800/50 border-slate-700">
              <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
              {activitiesLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                </div>
              ) : activities.length === 0 ? (
                <p className="text-slate-400 text-center py-8">No recent activity</p>
              ) : (
                <div className="space-y-4">
                  {activities.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-slate-700/30">
                      <div className="w-2 h-2 rounded-full bg-blue-500 mt-2"></div>
                      <div className="flex-1">
                        <p className="text-white font-medium">{activity.action}</p>
                        {activity.resource_type && (
                          <p className="text-sm text-slate-400">
                            {activity.resource_type} {activity.resource_id}
                          </p>
                        )}
                        <p className="text-xs text-slate-500 mt-1">
                          {new Date(activity.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <Card className="p-6 bg-slate-800/50 border-slate-700">
              <h3 className="text-lg font-semibold text-white mb-4">Account Settings</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 rounded-lg bg-slate-700/30">
                  <div>
                    <h4 className="text-white font-medium">Email Verified</h4>
                    <p className="text-sm text-slate-400">User's email verification status</p>
                  </div>
                  <Badge variant={user.email_verified ? "default" : "destructive"}>
                    {user.email_verified ? "Verified" : "Not Verified"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-4 rounded-lg bg-slate-700/30">
                  <div>
                    <h4 className="text-white font-medium">Last Login</h4>
                    <p className="text-sm text-slate-400">When the user last logged in</p>
                  </div>
                  <span className="text-white">
                    {user.last_login ? new Date(user.last_login).toLocaleString() : 'Never'}
                  </span>
                </div>

                <div className="flex items-center justify-between p-4 rounded-lg bg-slate-700/30">
                  <div>
                    <h4 className="text-white font-medium">Account Created</h4>
                    <p className="text-sm text-slate-400">When the user account was created</p>
                  </div>
                  <span className="text-white">
                    {new Date(user.created_at).toLocaleString()}
                  </span>
                </div>

                <div className="flex items-center justify-between p-4 rounded-lg bg-slate-700/30">
                  <div>
                    <h4 className="text-white font-medium">Last Updated</h4>
                    <p className="text-sm text-slate-400">When the user profile was last updated</p>
                  </div>
                  <span className="text-white">
                    {new Date(user.updated_at).toLocaleString()}
                  </span>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
