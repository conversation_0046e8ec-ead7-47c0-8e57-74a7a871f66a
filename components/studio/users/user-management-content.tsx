"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  UserPlus, 
  Search, 
  Filter, 
  Download,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  UserCheck,
  UserX,
  Calendar,
  Mail,
  Phone,
  Building,
  MapPin
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  getUsers, 
  getUserStats, 
  updateUser, 
  deleteUser,
  type User, 
  type UserStats, 
  type UserFilters 
} from "@/lib/services/user-management-service"
import UserStatsCards from "./user-stats-cards"
import UserDetailsModal from "./user-details-modal"
import UserFiltersPanel from "./user-filters-panel"
import CreateUserModal from "./create-user-modal"
import { useCurrentUser } from "@/hooks/use-current-user"
import { toast } from "sonner"

export default function UserManagementContent() {
  const [users, setUsers] = useState<User[]>([])
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalUsers, setTotalUsers] = useState(0)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showUserModal, setShowUserModal] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<UserFilters>({})
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])

  // Get current user permissions
  const { user: currentUser, canManageUsers, isAdmin } = useCurrentUser()

  const pageSize = 10

  // Load users and stats
  const loadData = async () => {
    try {
      setLoading(true)
      const [usersResult, statsResult] = await Promise.all([
        getUsers(currentPage, pageSize, { ...filters, search: searchTerm }),
        getUserStats()
      ])
      
      setUsers(usersResult.users)
      setTotalPages(usersResult.totalPages)
      setTotalUsers(usersResult.total)
      setStats(statsResult)
    } catch (error) {
      console.error('Error loading data:', error)
      toast.error('Failed to load user data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [currentPage, filters])

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1)
    loadData()
  }

  // Handle user status change
  const handleStatusChange = async (userId: string, status: User['status']) => {
    try {
      await updateUser(userId, { status })
      toast.success(`User ${status === 'active' ? 'activated' : status === 'suspended' ? 'suspended' : 'updated'} successfully`)
      loadData()
    } catch (error) {
      toast.error('Failed to update user status')
    }
  }

  // Handle user deletion
  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return
    }

    try {
      await deleteUser(userId)
      toast.success('User deleted successfully')
      loadData()
    } catch (error) {
      toast.error('Failed to delete user')
    }
  }

  // Handle bulk actions
  const handleBulkAction = async (action: string) => {
    if (selectedUsers.length === 0) {
      toast.error('Please select users first')
      return
    }

    if (!confirm(`Are you sure you want to ${action} ${selectedUsers.length} user(s)?`)) {
      return
    }

    try {
      const promises = selectedUsers.map(userId => {
        switch (action) {
          case 'activate':
            return updateUser(userId, { status: 'active' })
          case 'suspend':
            return updateUser(userId, { status: 'suspended' })
          case 'delete':
            return deleteUser(userId)
          default:
            return Promise.resolve()
        }
      })

      await Promise.all(promises)
      toast.success(`Successfully ${action}d ${selectedUsers.length} user(s)`)
      setSelectedUsers([])
      loadData()
    } catch (error) {
      toast.error(`Failed to ${action} users`)
    }
  }

  // Get status badge variant
  const getStatusBadge = (status: User['status']) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">Active</Badge>
      case 'suspended':
        return <Badge variant="destructive">Suspended</Badge>
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">Pending</Badge>
      case 'inactive':
        return <Badge variant="outline">Inactive</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Get role badge
  const getRoleBadge = (role: User['role']) => {
    switch (role) {
      case 'admin':
        return <Badge variant="default" className="bg-purple-500/20 text-purple-400 border-purple-500/30">Admin</Badge>
      case 'moderator':
        return <Badge variant="default" className="bg-blue-500/20 text-blue-400 border-blue-500/30">Moderator</Badge>
      case 'user':
        return <Badge variant="outline">User</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  return (
    <div className="flex flex-col space-y-8">
      {/* Header */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">User Management</h1>
            <div className="mt-2 space-y-1">
              <p className="text-slate-400">Manage users, roles, and permissions</p>
              {currentUser && (
                <div className="text-sm text-slate-500 space-y-1">
                  {/* Show who added this user if they were added by someone else */}
                  {currentUser.created_by_email && currentUser.created_by_email !== currentUser.email ? (
                    <p>
                      Account added by: <span className="text-slate-300">{currentUser.created_by_name || currentUser.created_by_email}</span>
                      <span className="ml-2 px-2 py-1 bg-green-500/20 text-green-400 border border-green-500/30 rounded text-xs">
                        Added User
                      </span>
                    </p>
                  ) : (
                    <p>
                      Logged in as: <span className="text-slate-300">{currentUser.email}</span>
                      {canManageUsers ? (
                        <span className="ml-2 px-2 py-1 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded text-xs">
                          {isAdmin ? 'Admin' : 'Moderator'} Access
                        </span>
                      ) : (
                        <span className="ml-2 px-2 py-1 bg-slate-500/20 text-slate-400 border border-slate-500/30 rounded text-xs">
                          View Only
                        </span>
                      )}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
            {canManageUsers && (
              <Button
                variant="outline"
                className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50"
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            )}
            {canManageUsers && (
              <Button
                onClick={() => setShowCreateModal(true)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <UserPlus className="w-4 h-4 mr-2" />
                Add User
              </Button>
            )}
          </div>
        </div>

        {/* Search Bar */}
        <div className="flex items-center gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <Input
              placeholder="Search users by email or name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="pl-10 bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
            />
          </div>
          <Button onClick={handleSearch} className="bg-blue-600 hover:bg-blue-700">
            Search
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && <UserStatsCards stats={stats} />}

      {/* Filters Panel */}
      {showFilters && (
        <UserFiltersPanel
          filters={filters}
          onFiltersChange={setFilters}
          onClose={() => setShowFilters(false)}
        />
      )}

      {/* Bulk Actions - Only for users with management permissions */}
      {canManageUsers && selectedUsers.length > 0 && (
        <Card className="p-4 bg-slate-800/50 border-slate-700">
          <div className="flex items-center justify-between">
            <span className="text-white">
              {selectedUsers.length} user(s) selected
            </span>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('activate')}
                className="border-green-600 text-green-400 hover:bg-green-600/20"
              >
                <UserCheck className="w-4 h-4 mr-1" />
                Activate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('suspend')}
                className="border-yellow-600 text-yellow-400 hover:bg-yellow-600/20"
              >
                <UserX className="w-4 h-4 mr-1" />
                Suspend
              </Button>
              {isAdmin && (
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleBulkAction('delete')}
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  Delete
                </Button>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* Users Table */}
      <Card className="bg-slate-800/50 border-slate-700">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center gap-2">
              <Users className="w-5 h-5" />
              Users ({totalUsers})
            </h2>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <div className="space-y-2">
                <p className="text-slate-400">No users found</p>
                {totalUsers === 0 && (
                  <div className="mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                    <p className="text-blue-400 text-sm">
                      💡 <strong>Database Setup Required:</strong> Please run the database setup SQL in your Supabase dashboard.
                    </p>
                    <p className="text-blue-300 text-xs mt-1">
                      Check the <code>DATABASE_SETUP_INSTRUCTIONS.md</code> file for detailed instructions.
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {users.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-4 rounded-lg bg-slate-700/30 border border-slate-600/50 hover:bg-slate-700/50 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    {/* Only show checkboxes for users with management permissions */}
                    {canManageUsers && (
                      <input
                        type="checkbox"
                        checked={selectedUsers.includes(user.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedUsers([...selectedUsers, user.id])
                          } else {
                            setSelectedUsers(selectedUsers.filter(id => id !== user.id))
                          }
                        }}
                        className="rounded border-slate-600"
                      />
                    )}
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold">
                      {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium text-white">{user.name || 'No name'}</h3>
                        {getStatusBadge(user.status)}
                        {getRoleBadge(user.role)}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-slate-400 mt-1">
                        <span className="flex items-center gap-1">
                          <Mail className="w-3 h-3" />
                          {user.email}
                        </span>
                        {user.company && (
                          <span className="flex items-center gap-1">
                            <Building className="w-3 h-3" />
                            {user.company}
                          </span>
                        )}
                        {user.location && (
                          <span className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            {user.location}
                          </span>
                        )}
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {new Date(user.created_at).toLocaleDateString()}
                        </span>
                        {/* Show who added this user */}
                        {user.created_by_email && (
                          <span className="flex items-center gap-1 text-green-400">
                            <UserPlus className="w-3 h-3" />
                            Added by {user.created_by_name || user.created_by_email}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
                      <DropdownMenuItem
                        onClick={() => {
                          setSelectedUser(user)
                          setShowUserModal(true)
                        }}
                        className="text-slate-300 hover:text-white hover:bg-slate-700"
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        View Details
                      </DropdownMenuItem>

                      {/* Only show admin actions if user has permissions and it's not their own account */}
                      {canManageUsers && currentUser?.id !== user.id && (
                        <>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedUser(user)
                              setShowUserModal(true)
                            }}
                            className="text-slate-300 hover:text-white hover:bg-slate-700"
                          >
                            <Edit className="w-4 h-4 mr-2" />
                            Edit User
                          </DropdownMenuItem>

                          {user.status === 'active' ? (
                            <DropdownMenuItem
                              onClick={() => handleStatusChange(user.id, 'suspended')}
                              className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-600/20"
                            >
                              <UserX className="w-4 h-4 mr-2" />
                              Suspend User
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem
                              onClick={() => handleStatusChange(user.id, 'active')}
                              className="text-green-400 hover:text-green-300 hover:bg-green-600/20"
                            >
                              <UserCheck className="w-4 h-4 mr-2" />
                              Activate User
                            </DropdownMenuItem>
                          )}

                          {/* Only admins can delete users */}
                          {isAdmin && (
                            <DropdownMenuItem
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-red-400 hover:text-red-300 hover:bg-red-600/20"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete User
                            </DropdownMenuItem>
                          )}
                        </>
                      )}

                      {/* If it's the user's own account, only show view details */}
                      {currentUser?.id === user.id && (
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedUser(user)
                            setShowUserModal(true)
                          }}
                          className="text-slate-300 hover:text-white hover:bg-slate-700"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Edit My Profile
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-6 border-t border-slate-700">
              <p className="text-sm text-slate-400">
                Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalUsers)} of {totalUsers} users
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50"
                >
                  Previous
                </Button>
                <span className="text-sm text-slate-400">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50"
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* User Details Modal */}
      {selectedUser && (
        <UserDetailsModal
          user={selectedUser}
          open={showUserModal}
          onOpenChange={setShowUserModal}
          onUserUpdated={loadData}
        />
      )}

      {/* Create User Modal */}
      <CreateUserModal
        open={showCreateModal}
        onOpenChange={setShowCreateModal}
        onUserCreated={loadData}
      />
    </div>
  )
}
