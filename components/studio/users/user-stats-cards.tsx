"use client"

import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  UserCheck, 
  UserPlus, 
  UserX, 
  Clock,
  TrendingUp,
  TrendingDown
} from "lucide-react"
import { type UserStats } from "@/lib/services/user-management-service"

interface UserStatsCardsProps {
  stats: UserStats
}

export default function UserStatsCards({ stats }: UserStatsCardsProps) {
  const statsCards = [
    {
      title: "Total Users",
      value: stats.total_users,
      icon: Users,
      color: "blue",
      description: "All registered users",
    },
    {
      title: "Active Users",
      value: stats.active_users,
      icon: UserCheck,
      color: "green",
      description: "Currently active users",
      percentage: stats.total_users > 0 ? Math.round((stats.active_users / stats.total_users) * 100) : 0,
    },
    {
      title: "New Today",
      value: stats.new_users_today,
      icon: UserPlus,
      color: "purple",
      description: "Users registered today",
      trend: stats.new_users_today > 0 ? "up" : "neutral",
    },
    {
      title: "New This Week",
      value: stats.new_users_this_week,
      icon: TrendingUp,
      color: "indigo",
      description: "Users registered this week",
    },
    {
      title: "New This Month",
      value: stats.new_users_this_month,
      icon: Calendar,
      color: "cyan",
      description: "Users registered this month",
    },
    {
      title: "Suspended",
      value: stats.suspended_users,
      icon: UserX,
      color: "red",
      description: "Suspended user accounts",
      percentage: stats.total_users > 0 ? Math.round((stats.suspended_users / stats.total_users) * 100) : 0,
    },
    {
      title: "Pending",
      value: stats.pending_users,
      icon: Clock,
      color: "yellow",
      description: "Pending verification",
      percentage: stats.total_users > 0 ? Math.round((stats.pending_users / stats.total_users) * 100) : 0,
    },
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case "blue":
        return {
          bg: "bg-blue-500/20",
          text: "text-blue-400",
          border: "border-blue-500/30",
          glow: "glow-blue",
        }
      case "green":
        return {
          bg: "bg-green-500/20",
          text: "text-green-400",
          border: "border-green-500/30",
          glow: "glow-green",
        }
      case "purple":
        return {
          bg: "bg-purple-500/20",
          text: "text-purple-400",
          border: "border-purple-500/30",
          glow: "glow-purple",
        }
      case "indigo":
        return {
          bg: "bg-indigo-500/20",
          text: "text-indigo-400",
          border: "border-indigo-500/30",
          glow: "glow-indigo",
        }
      case "cyan":
        return {
          bg: "bg-cyan-500/20",
          text: "text-cyan-400",
          border: "border-cyan-500/30",
          glow: "glow-cyan",
        }
      case "red":
        return {
          bg: "bg-red-500/20",
          text: "text-red-400",
          border: "border-red-500/30",
          glow: "glow-red",
        }
      case "yellow":
        return {
          bg: "bg-yellow-500/20",
          text: "text-yellow-400",
          border: "border-yellow-500/30",
          glow: "glow-yellow",
        }
      default:
        return {
          bg: "bg-slate-500/20",
          text: "text-slate-400",
          border: "border-slate-500/30",
          glow: "",
        }
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-6">
      {statsCards.map((stat, index) => {
        const colors = getColorClasses(stat.color)
        const IconComponent = stat.icon

        return (
          <Card
            key={index}
            className={`p-6 bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300 ${colors.glow}`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-lg ${colors.bg} ${colors.border} border`}>
                <IconComponent className={`w-6 h-6 ${colors.text}`} />
              </div>
              {stat.trend && (
                <div className="flex items-center">
                  {stat.trend === "up" ? (
                    <TrendingUp className="w-4 h-4 text-green-400" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-400" />
                  )}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white">
                  {stat.value.toLocaleString()}
                </h3>
                {stat.percentage !== undefined && (
                  <Badge
                    variant="outline"
                    className={`${colors.bg} ${colors.text} ${colors.border} border text-xs`}
                  >
                    {stat.percentage}%
                  </Badge>
                )}
              </div>

              <div>
                <p className="text-sm font-medium text-slate-300">{stat.title}</p>
                <p className="text-xs text-slate-400 mt-1">{stat.description}</p>
              </div>
            </div>
          </Card>
        )
      })}
    </div>
  )
}

// Fix the Calendar import
function Calendar({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
      />
    </svg>
  )
}
