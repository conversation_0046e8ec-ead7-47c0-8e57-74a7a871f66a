"use client"

import { useState } from "react"
import { GradientCard } from "@/components/ui/gradient-card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MessageSquare, GitPullRequest, FileText, AlertCircle, FolderPlus, Lightbulb } from "lucide-react"
import { ActivityItem } from "@/lib/services/dashboard-service"

interface ActivityFeedProps {
  activities: ActivityItem[]
}

export default function ActivityFeed({ activities }: ActivityFeedProps) {
  const [filter, setFilter] = useState("all")

  const filteredActivities = filter === "all" ? activities : activities.filter((activity) => activity.type === filter)

  return (
    <GradientCard className="overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-center">
          <h3 className="font-semibold text-slate-50">Recent Activity</h3>
          <Button variant="link" className="text-blue-400 p-0 h-auto">
            View all
          </Button>
        </div>

        <Tabs defaultValue="all" onValueChange={setFilter} className="mt-4">
          <TabsList className="bg-slate-800 w-full justify-start">
            <TabsTrigger value="all" className="text-xs">
              All
            </TabsTrigger>
            <TabsTrigger value="comment" className="text-xs">
              Comments
            </TabsTrigger>
            <TabsTrigger value="update" className="text-xs">
              Updates
            </TabsTrigger>
            <TabsTrigger value="ticket" className="text-xs">
              Tickets
            </TabsTrigger>
            <TabsTrigger value="ai" className="text-xs">
              AI
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="mt-4 space-y-4">
          {filteredActivities.length > 0 ? (
            filteredActivities.map((activity) => (
              <ActivityItemComponent key={activity.id} activity={activity} />
            ))
          ) : (
            <div className="text-center py-12">
              <div className="w-12 h-12 mx-auto rounded-full bg-slate-700/50 flex items-center justify-center mb-4">
                <MessageSquare className="w-6 h-6 text-slate-400" />
              </div>
              <p className="text-slate-300 font-medium mb-2">No recent activity</p>
              <p className="text-xs text-slate-400">
                {filter === "all"
                  ? "Start creating projects to see activity here"
                  : `No ${filter} activity found`}
              </p>
            </div>
          )}
        </div>
      </div>
    </GradientCard>
  )
}

interface ActivityItemComponentProps {
  activity: ActivityItem
}

function ActivityItemComponent({ activity }: ActivityItemComponentProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "comment":
        return <MessageSquare className="h-4 w-4 text-blue-400" />
      case "ticket":
        return <FileText className="h-4 w-4 text-emerald-400" />
      case "project":
        return <FolderPlus className="h-4 w-4 text-purple-400" />
      case "validation":
        return <Lightbulb className="h-4 w-4 text-amber-400" />
      default:
        return <MessageSquare className="h-4 w-4 text-blue-400" />
    }
  }

  return (
    <div className="flex gap-3">
      <Avatar className="h-8 w-8">
        <AvatarImage src={activity.user.avatar || "/placeholder.svg"} alt={activity.user.name} />
        <AvatarFallback className="bg-slate-700 text-slate-300">{activity.user.initials}</AvatarFallback>
      </Avatar>
      <div className="flex-1 space-y-1">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-slate-200">{activity.user.name}</span>
          <span className="text-xs text-slate-400">{activity.time}</span>
        </div>
        <p className="text-sm text-slate-300">
          {activity.action}
          {activity.target && (
            <>
              {" "}<span className="font-medium text-slate-200">{activity.target}</span>
            </>
          )}
          {activity.project && (
            <>
              {" "}in <span className="text-blue-400">{activity.project}</span>
            </>
          )}
        </p>
        <div className="flex items-center gap-1.5 text-xs text-slate-400">
          {getActivityIcon(activity.type)}
          <span className="capitalize">{activity.type}</span>
        </div>
      </div>
    </div>
  )
}
