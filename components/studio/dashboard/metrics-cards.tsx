"use client"

import type React from "react"

import { GradientCard } from "@/components/ui/gradient-card"
import CountUp from "@/components/animations/count-up"
import { BarChart3, Clock, Activity, CheckCircle } from "lucide-react"
import { DashboardMetrics } from "@/lib/services/dashboard-service"

interface MetricsCardsProps {
  metrics: DashboardMetrics | null
}

export default function MetricsCards({ metrics }: MetricsCardsProps) {
  // Use default values if metrics not loaded yet and ensure no NaN values
  const data = metrics || {
    totalProjects: 0,
    completionRate: 0,
    activeTasks: 0,
    timeSaved: 0,
    projectsThisMonth: 0,
    completionTrend: 0,
    tasksToday: 0
  }

  console.log('Metrics data received:', data)

  // Ensure all values are safe numbers and handle null/undefined
  const safeData = {
    totalProjects: (data.totalProjects == null || isNaN(Number(data.totalProjects))) ? 0 : Math.max(0, Number(data.totalProjects)),
    completionRate: (data.completionRate == null || isNaN(Number(data.completionRate))) ? 0 : Math.max(0, Math.min(100, Number(data.completionRate))),
    activeTasks: (data.activeTasks == null || isNaN(Number(data.activeTasks))) ? 0 : Math.max(0, Number(data.activeTasks)),
    timeSaved: (data.timeSaved == null || isNaN(Number(data.timeSaved))) ? 0 : Math.max(0, Number(data.timeSaved)),
    projectsThisMonth: (data.projectsThisMonth == null || isNaN(Number(data.projectsThisMonth))) ? 0 : Math.max(0, Number(data.projectsThisMonth)),
    completionTrend: (data.completionTrend == null || isNaN(Number(data.completionTrend))) ? 0 : Number(data.completionTrend),
    tasksToday: (data.tasksToday == null || isNaN(Number(data.tasksToday))) ? 0 : Math.max(0, Number(data.tasksToday))
  }

  console.log('Safe metrics data:', safeData)

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
      <MetricCard
        title="Total Projects"
        value={safeData.totalProjects}
        icon={<BarChart3 className="h-6 w-6 text-blue-400" />}
        trend={safeData.projectsThisMonth > 0 ? `+${safeData.projectsThisMonth} this month` : "No new projects this month"}
        trendUp={safeData.projectsThisMonth > 0}
        color="blue"
      />
      <MetricCard
        title="Completion Rate"
        value={safeData.completionRate}
        suffix="%"
        icon={<CheckCircle className="h-6 w-6 text-emerald-400" />}
        trend={safeData.completionTrend > 0 ? `+${safeData.completionTrend}% from last month` : "Track your progress"}
        trendUp={safeData.completionTrend > 0}
        color="emerald"
      />
      <MetricCard
        title="Active Tasks"
        value={safeData.activeTasks}
        icon={<Activity className="h-6 w-6 text-purple-400" />}
        trend={safeData.tasksToday > 0 ? `${safeData.tasksToday} created today` : "No tasks created today"}
        trendUp={false}
        color="purple"
      />
      <MetricCard
        title="Time Saved"
        value={safeData.timeSaved}
        suffix="h"
        icon={<Clock className="h-6 w-6 text-amber-400" />}
        trend={safeData.timeSaved > 0 ? "Using AI features" : "Start using AI tools"}
        trendUp={safeData.timeSaved > 0}
        color="amber"
      />
    </div>
  )
}

interface MetricCardProps {
  title: string
  value: number
  suffix?: string
  icon: React.ReactNode
  trend: string
  trendUp: boolean
  color: "blue" | "emerald" | "purple" | "amber"
}

function MetricCard({ title, value, suffix = "", icon, trend, trendUp, color }: MetricCardProps) {
  const colorClasses = {
    blue: "from-blue-500/20 to-blue-600/20 border-blue-500/30",
    emerald: "from-emerald-500/20 to-emerald-600/20 border-emerald-500/30",
    purple: "from-purple-500/20 to-purple-600/20 border-purple-500/30",
    amber: "from-amber-500/20 to-amber-600/20 border-amber-500/30"
  }

  const glowClasses = {
    blue: "glow-blue",
    emerald: "glow-emerald",
    purple: "glow-purple",
    amber: "glow-amber"
  }

  return (
    <div className="group relative">
      {/* Premium glow effect on hover */}
      <div className={`absolute -inset-0.5 bg-gradient-to-r ${colorClasses[color].replace('/20', '/40').replace('/30', '')} rounded-2xl blur opacity-0 group-hover:opacity-30 transition duration-500`}></div>

      <GradientCard className={`relative overflow-hidden bg-slate-900/40 backdrop-blur-xl border ${colorClasses[color]} hover:scale-105 transition-all duration-300 ${glowClasses[color]}`}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xs font-semibold text-slate-300 uppercase tracking-wider">{title}</h3>
            <div className={`rounded-lg bg-gradient-to-br ${colorClasses[color].replace('/20', '/30')} p-2 ${glowClasses[color]}`}>
              {icon}
            </div>
          </div>
          <div className="mb-3">
            <div className="text-3xl font-bold text-white tracking-tight">
              <CountUp end={value} duration={2} />
              {suffix}
            </div>
          </div>
          <div className="flex items-center">
            <span
              className={`inline-flex items-center text-xs font-medium ${
                trendUp ? "text-emerald-400" : "text-slate-400"
              }`}
            >
              {trend}
            </span>
          </div>
        </div>
      </GradientCard>
    </div>
  )
}
