"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { GradientCard } from "@/components/ui/gradient-card"
import { Plus, Zap, Clock, ArrowRight } from "lucide-react"
import { useAIAnalysisModal } from "@/components/studio/ai-analysis/ai-analysis-provider"
import { DashboardProject } from "@/lib/services/dashboard-service"
import Link from "next/link"

interface QuickActionsProps {
  projects: DashboardProject[]
}

export default function QuickActions({ projects }: QuickActionsProps) {
  const { openModal } = useAIAnalysisModal()

  // Get the 2 most recent projects
  const recentProjects = projects.slice(0, 2)

  return (
    <div className="space-y-6">
      {/* Create New Project */}
      <GradientCard variant="blue" className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold text-white mb-2">Start Your Next Big Idea</h3>
            <p className="text-slate-300 mb-4">
              Use AI to analyze, validate, and build your SaaS concept from scratch.
            </p>
            <Button onClick={openModal} className="bg-white text-blue-600 hover:bg-slate-100 font-medium">
              <Plus className="w-4 h-4 mr-2" />
              Create New Project
            </Button>
          </div>
          <div className="hidden md:block">
            <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
              <Zap className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </GradientCard>

      {/* Continue Working */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Continue Working
        </h3>
        <div className="space-y-3">
          {recentProjects.length > 0 ? (
            recentProjects.map((project) => (
              <GradientCard key={project.id} variant="purple" className="p-4 hover:scale-105 transition-all duration-300">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-semibold text-white truncate">{project.name || 'Untitled Project'}</h4>
                      <div className="flex gap-1">
                        {(project.tags || []).slice(0, 2).map((tag, index) => (
                          <span key={index} className="px-2 py-1 text-xs bg-purple-500/20 text-purple-300 rounded-md">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    <p className="text-sm text-slate-300 mb-3 line-clamp-2">{project.description || 'No description available'}</p>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2 flex-1">
                        <div className="flex-1 h-2 bg-slate-700 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-500"
                            style={{ width: `${project.progress || 0}%` }}
                          />
                        </div>
                        <span className="text-xs text-slate-400 font-medium">{project.progress || 0}%</span>
                      </div>
                      <span className="text-xs text-slate-400 whitespace-nowrap">Updated {project.lastUpdated || 'Recently'}</span>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" className="text-white hover:bg-white/10 ml-3 flex-shrink-0" asChild>
                    <Link href={`/studio/ai-insights/${project.id}`}>
                      <ArrowRight className="w-4 h-4" />
                    </Link>
                  </Button>
                </div>
              </GradientCard>
            ))
          ) : (
            <GradientCard variant="slate" className="p-6 text-center">
              <div className="space-y-4">
                <div className="w-12 h-12 mx-auto rounded-full bg-slate-700/50 flex items-center justify-center">
                  <Clock className="w-6 h-6 text-slate-400" />
                </div>
                <div>
                  <p className="text-slate-300 font-medium">No recent projects</p>
                  <p className="text-xs text-slate-400 mt-1">Create your first project to get started</p>
                </div>
                <Button
                  onClick={openModal}
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Start Now
                </Button>
              </div>
            </GradientCard>
          )}
        </div>
      </div>

      {/* AI Analysis Shortcut */}
      <GradientCard variant="emerald" className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white mb-2">AI Analysis</h3>
            <p className="text-slate-300 text-sm mb-4">
              Get instant insights on market feasibility, technical requirements, and growth potential.
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={openModal}
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10 hover:border-white/40 transition-all duration-200"
                size="sm"
              >
                <Zap className="w-4 h-4 mr-2" />
                Analyze Project
              </Button>
              <Button
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10 hover:border-white/40 transition-all duration-200"
                size="sm"
                asChild
              >
                <Link href="/studio/idea-validator">
                  Validate Idea
                </Link>
              </Button>
            </div>
          </div>
          <div className="hidden md:block ml-4">
            <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center">
              <Zap className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
      </GradientCard>
    </div>
  )
}
