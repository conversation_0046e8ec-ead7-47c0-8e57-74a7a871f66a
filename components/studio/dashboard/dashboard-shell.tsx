"use client"

import type React from "react"

import { useState } from "react"
import SidebarNav from "@/components/layout/sidebar-nav"
import Header from "@/components/layout/header"
import AIAnalysisModal from "@/components/studio/ai-analysis/ai-analysis-modal"

interface DashboardShellProps {
  children: React.ReactNode
}

export default function DashboardShell({ children }: DashboardShellProps) {
  const [showAIModal, setShowAIModal] = useState(false)

  return (
    <div className="h-screen flex flex-col overflow-hidden relative">
      {/* Premium Dark Background matching landing page exactly */}
      <div className="absolute inset-0 bg-[#0a0a0a]"></div>

      {/* Landing page gradient overlays for premium feel */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-950/50 via-blue-950/20 to-purple-950/20"></div>
      <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/10 to-black/30" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-blue-900/15 via-transparent to-purple-900/15"></div>

      <div className="flex flex-1 min-h-0 overflow-hidden relative z-10">
        <SidebarNav />
        <div className="flex-1 flex flex-col min-h-0 min-w-0 overflow-hidden">
          <Header onOpenAIModal={() => setShowAIModal(true)} />
          <main className="flex-1 min-h-0 overflow-auto">
            <div className="max-w-full p-6 md:p-8">
              <div className="max-w-7xl mx-auto">
                {children}
              </div>
            </div>
          </main>
        </div>
      </div>

      <AIAnalysisModal open={showAIModal} onOpenChange={setShowAIModal} isNewUser={true} />
    </div>
  )
}
