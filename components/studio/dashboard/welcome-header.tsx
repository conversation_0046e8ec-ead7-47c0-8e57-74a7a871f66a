"use client"

import { useState, useEffect } from "react"
import SplitText from "@/components/animations/split-text"
import { But<PERSON> } from "@/components/ui/button"
import { Plus<PERSON>ircle, <PERSON>rk<PERSON> } from "lucide-react"
import { useAIAnalysis } from "@/components/studio/ai-analysis/ai-analysis-provider"
import { useAuth } from "@/components/providers/auth-provider"

interface WelcomeHeaderProps {
  name?: string
  isNewUser?: boolean
}

export default function WelcomeHeader({ name, isNewUser = false }: WelcomeHeaderProps) {
  const [greeting, setGreeting] = useState("Hello")
  const { openModal } = useAIAnalysis()
  const { user } = useAuth()
  
  // Get user's name from auth if available
  let userName = name;
  if (!userName && user?.user_metadata) {
    const firstName = user.user_metadata.first_name;
    const lastName = user.user_metadata.last_name;
    if (firstName && lastName) {
      userName = `${firstName} ${lastName}`;
    } else if (firstName) {
      userName = firstName;
    } else if (lastName) {
      userName = lastName;
    } else if (user.user_metadata.name) {
      userName = user.user_metadata.name;
    } else if (user.email) {
      userName = user.email.split('@')[0];
    } else {
      userName = "there";
    }
  } else if (!userName && user?.email) {
    userName = user.email.split('@')[0];
  } else if (!userName) {
    userName = "there";
  }

  useEffect(() => {
    const hour = new Date().getHours()
    if (hour < 12) setGreeting("Good morning")
    else if (hour < 18) setGreeting("Good afternoon")
    else setGreeting("Good evening")
  }, [])

  return (
    <div className="bg-slate-900/40 backdrop-blur-xl border border-slate-700/30 rounded-2xl p-8 glow-blue">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
        <div className="space-y-3">
          <h1 className="text-4xl font-bold text-white tracking-tight">
            <SplitText text={`${greeting}, ${userName}`} delay={30} />
          </h1>
          <p className="text-slate-300 text-lg leading-relaxed max-w-2xl">
            {isNewUser
              ? "Welcome to SaaS Ideas! Let's get started with your first project and transform your ideas into reality."
              : "Here's what's happening with your projects today. Ready to build something amazing?"}
          </p>
        </div>
        <div className="flex gap-4">
          <Button
            onClick={openModal}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 glow-blue"
          >
            <Sparkles className="mr-3 h-5 w-5" />
            AI Analysis
          </Button>
        </div>
      </div>
    </div>
  )
}
