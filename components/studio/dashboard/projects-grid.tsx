"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { GradientCard } from "@/components/ui/gradient-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { MoreHorizontal, FileLineChartIcon as FlowChart, Ka<PERSON>ban, LayoutDashboard, Brain, Plus, Sparkles, TrendingUp, RefreshCw } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import ScrollFloat from "@/components/animations/scroll-float"
import { DashboardProject } from "@/lib/services/dashboard-service"
import { useAIAnalysisModal } from "@/components/studio/ai-analysis/ai-analysis-provider"

interface ProjectsGridProps {
  projects: DashboardProject[]
  onRefresh?: () => void
}

export default function ProjectsGrid({ projects, onRefresh }: ProjectsGridProps) {
  const [filter, setFilter] = useState("all")

  // Debug: Log when component receives projects
  useEffect(() => {
    console.log('ProjectsGrid - Received projects:', {
      count: projects.length,
      projects: projects.map(p => ({
        id: p.id,
        name: p.name,
        tags: p.tags,
        description: p.description,
        progress: p.progress,
        lastUpdated: p.lastUpdated,
        status: p.status,
        createdAt: p.createdAt
      }))
    })
  }, [projects])

  // Reset filter to "all" when projects change (e.g., new project added)
  // Only reset if current filter is not "all" to avoid unnecessary re-renders
  useEffect(() => {
    // Only reset filter when projects array changes, not when filter changes
    if (filter !== "all") {
      setFilter("all")
    }
  }, [projects.length]) // Removed 'filter' from dependencies to prevent infinite loop

  // Get unique tags from projects for filtering
  const allTags = Array.from(new Set(projects.flatMap(project => Array.isArray(project.tags) ? project.tags : [])))
  const availableFilters = ["all", ...allTags.slice(0, 4)] // Limit to 4 filters

  // Fixed filtering logic
  const filteredProjects = React.useMemo(() => {
    // If filter is "all" or empty, return all projects
    if (!filter || filter === "all") {
      return projects
    }

    // Filter by tag
    const filtered = projects.filter((project) => {
      const projectTags = Array.isArray(project.tags) ? project.tags : []
      return projectTags.includes(filter)
    })

    return filtered
  }, [projects, filter])

  const handleFilterChange = (newFilter: string) => {
    setFilter(newFilter)
  }



  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <h2 className="text-xl font-semibold text-slate-50">
            Your Projects
            <span className="text-sm text-slate-400 ml-2">
              ({filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''})
            </span>
          </h2>
          {onRefresh && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              className="text-slate-400 hover:text-slate-50 hover:bg-slate-700/50"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          )}
        </div>
        {projects.length > 0 && (
          <div className="flex flex-col gap-2">

            <Tabs value={filter} onValueChange={handleFilterChange} className="w-full sm:w-auto">
              <TabsList className="bg-slate-800">
                <TabsTrigger value="all">All</TabsTrigger>
                {allTags.slice(0, 3).map(tag => (
                  <TabsTrigger key={tag} value={tag} className="capitalize">
                    {tag}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>
        )}
      </div>



      {projects.length === 0 ? (
        <ProjectsEmptyState />
      ) : filteredProjects.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-slate-400 mb-4">
            <LayoutDashboard className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">No projects found</p>
            <p className="text-sm mt-2">Try adjusting your filter or create a new project</p>

          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredProjects.map((project, index) => (
            <div key={project.id} className="h-full">
              <ProjectCard project={project} />
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

interface ProjectCardProps {
  project: DashboardProject
}

function ProjectCard({ project }: ProjectCardProps) {
  return (
    <GradientCard variant="feature" className="h-full flex flex-col hover:scale-105 transition-all duration-300 group">
      <div className="p-4 flex-1">
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-base text-white mb-2 group-hover:text-blue-300 transition-colors">{project.name}</h3>
            <p className="text-xs text-slate-300 line-clamp-2 leading-relaxed">{project.description}</p>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="text-slate-400 hover:text-slate-50 hover:bg-slate-700/50 ml-2 flex-shrink-0">
                <MoreHorizontal className="h-5 w-5" />
                <span className="sr-only">More options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48 bg-slate-900 border border-slate-700 text-slate-50">
              <DropdownMenuItem className="cursor-pointer hover:bg-slate-800">Rename</DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer hover:bg-slate-800">Duplicate</DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer hover:bg-slate-800">Share</DropdownMenuItem>
              <DropdownMenuSeparator className="bg-slate-700" />
              <DropdownMenuItem className="cursor-pointer hover:bg-slate-800 text-red-400">Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-xs font-medium text-slate-400">Progress</span>
            <span className="text-xs font-semibold text-slate-200">{project.progress}%</span>
          </div>
          <Progress value={project.progress} className="h-1.5 bg-slate-700/80" indicatorClassName="bg-gradient-to-r from-blue-500 to-purple-500" />
        </div>

        <div className="mt-3 flex items-center justify-between">
          <div className="flex gap-1 flex-wrap">
            {(Array.isArray(project.tags) ? project.tags : []).slice(0, 2).map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center rounded-md bg-blue-500/10 border border-blue-500/20 px-1.5 py-0.5 text-xs font-medium text-blue-300"
              >
                {tag}
              </span>
            ))}
          </div>
          <span className="text-xs text-slate-400 whitespace-nowrap ml-2">Updated {project.lastUpdated}</span>
        </div>
      </div>

      <div className="p-3 border-t border-slate-700/60 bg-slate-800/30">
        <div className="flex flex-wrap gap-1.5">
          <Button
            variant="ghost"
            size="sm"
            className="h-7 gap-1 text-xs text-slate-300 hover:text-white hover:bg-slate-700/50 border border-transparent hover:border-slate-600/50 px-2"
            asChild
          >
            <Link href={`/studio/ai-insights/${project.id}/user-flow`}>
              <FlowChart className="h-3 w-3" />
              <span>Flow</span>
            </Link>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 gap-1 text-xs text-slate-300 hover:text-white hover:bg-slate-700/50 border border-transparent hover:border-slate-600/50 px-2"
            asChild
          >
            <Link href={`/studio/ai-insights/${project.id}/tickets`}>
              <Kanban className="h-3 w-3" />
              <span>Tasks</span>
            </Link>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 gap-1 text-xs text-slate-300 hover:text-white hover:bg-slate-700/50 border border-transparent hover:border-slate-600/50 px-2"
            asChild
          >
            <Link href={`/studio/ai-insights/${project.id}`}>
              <LayoutDashboard className="h-3 w-3" />
              <span>Overview</span>
            </Link>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 gap-1 text-xs text-slate-300 hover:text-white hover:bg-slate-700/50 border border-transparent hover:border-slate-600/50 px-2"
            asChild
          >
            <Link href={`/studio/ai-insights/${project.id}/memory-bank`}>
              <Brain className="h-3 w-3" />
              <span>Memory</span>
            </Link>
          </Button>
        </div>
      </div>
    </GradientCard>
  )
}

function ProjectsEmptyState() {
  const { openModal } = useAIAnalysisModal()

  return (
    <GradientCard variant="feature" className="p-12 text-center">
      <div className="max-w-lg mx-auto">
        {/* Enhanced visual container */}
        <div className="relative mb-10">
          <div className="w-24 h-24 mx-auto rounded-3xl bg-gradient-to-br from-blue-500/30 to-purple-600/30 border-2 border-blue-500/50 flex items-center justify-center shadow-2xl">
            <LayoutDashboard className="w-12 h-12 text-blue-300" />
          </div>
          {/* Floating icons */}
          <div className="absolute -top-3 -right-10 w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-blue-500 flex items-center justify-center animate-pulse shadow-lg">
            <TrendingUp className="w-5 h-5 text-white" />
          </div>
          <div className="absolute -bottom-3 -left-10 w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center animate-pulse delay-300 shadow-lg">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
        </div>

        <h3 className="text-3xl font-bold text-white mb-6">
          Start Your First Project
        </h3>
        <p className="text-slate-300 mb-10 leading-relaxed text-lg">
          Transform your SaaS ideas into reality with AI-powered analysis, validation, and project management tools.
        </p>

        <div className="space-y-6">
          <Button
            onClick={openModal}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-10 py-4 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
          >
            <Plus className="w-6 h-6 mr-3" />
            Create Your First Project
          </Button>

          <div className="text-slate-400 font-medium">
            Get started with AI analysis and validation
          </div>
        </div>
      </div>
    </GradientCard>
  )
}
