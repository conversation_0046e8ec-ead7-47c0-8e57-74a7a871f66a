"use client"

import { cn } from "@/lib/utils"
import { ReactNode } from "react"

interface PremiumContainerProps {
  children: ReactNode
  className?: string
  variant?: "default" | "glass" | "elevated" | "floating"
  glow?: boolean
  animate?: boolean
}

export default function PremiumContainer({ 
  children, 
  className, 
  variant = "default",
  glow = false,
  animate = true
}: PremiumContainerProps) {
  const variants = {
    default: "bg-slate-900/40 backdrop-blur-xl border border-slate-800/50",
    glass: "bg-white/5 backdrop-blur-2xl border border-white/10 shadow-2xl",
    elevated: "bg-slate-900/60 backdrop-blur-xl border border-slate-700/50 shadow-3xl",
    floating: "bg-slate-900/30 backdrop-blur-2xl border border-blue-500/20 shadow-2xl"
  }

  const glowEffect = glow ? "glow-blue" : ""
  const animationEffect = animate ? "transition-all duration-500 hover:scale-[1.01] hover:shadow-3xl" : ""

  return (
    <div 
      className={cn(
        "rounded-2xl p-6",
        variants[variant],
        glowEffect,
        animationEffect,
        className
      )}
    >
      {children}
    </div>
  )
}

// Premium Section Component
interface PremiumSectionProps {
  children: ReactNode
  title?: string
  subtitle?: string
  className?: string
  headerActions?: ReactNode
}

export function PremiumSection({ 
  children, 
  title, 
  subtitle, 
  className,
  headerActions 
}: PremiumSectionProps) {
  return (
    <div className={cn("space-y-6", className)}>
      {(title || subtitle || headerActions) && (
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            {title && (
              <h2 className="text-2xl font-bold text-white tracking-tight">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-slate-400 text-sm">
                {subtitle}
              </p>
            )}
          </div>
          {headerActions && (
            <div className="flex items-center gap-3">
              {headerActions}
            </div>
          )}
        </div>
      )}
      {children}
    </div>
  )
}

// Premium Grid Component
interface PremiumGridProps {
  children: ReactNode
  cols?: 1 | 2 | 3 | 4 | 5 | 6
  gap?: "sm" | "md" | "lg" | "xl"
  className?: string
}

export function PremiumGrid({ 
  children, 
  cols = 3, 
  gap = "md",
  className 
}: PremiumGridProps) {
  const colsMap = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
    5: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5",
    6: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6"
  }

  const gapMap = {
    sm: "gap-3",
    md: "gap-4",
    lg: "gap-6",
    xl: "gap-8"
  }

  return (
    <div className={cn(
      "grid",
      colsMap[cols],
      gapMap[gap],
      className
    )}>
      {children}
    </div>
  )
}

// Premium Card Component
interface PremiumCardProps {
  children: ReactNode
  className?: string
  hover?: boolean
  glow?: boolean
  gradient?: boolean
}

export function PremiumCard({ 
  children, 
  className,
  hover = true,
  glow = false,
  gradient = false
}: PremiumCardProps) {
  return (
    <div 
      className={cn(
        "rounded-xl p-6 border transition-all duration-300",
        gradient 
          ? "bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 border-slate-700/50" 
          : "bg-slate-900/40 backdrop-blur-xl border-slate-800/50",
        hover && "hover:scale-105 hover:shadow-2xl hover:border-slate-600/50",
        glow && "glow-blue",
        className
      )}
    >
      {children}
    </div>
  )
}
