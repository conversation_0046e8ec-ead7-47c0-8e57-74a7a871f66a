"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"

interface PremiumBackgroundProps {
  children: ReactNode
  className?: string
  variant?: "default" | "subtle" | "intense"
}

export default function PremiumBackground({ 
  children, 
  className,
  variant = "default" 
}: PremiumBackgroundProps) {
  const variants = {
    default: {
      base: "bg-[#0a0a0a]",
      overlay1: "bg-gradient-to-br from-slate-950/50 via-blue-950/20 to-purple-950/20",
      overlay2: "bg-gradient-to-b from-black/20 via-black/10 to-black/30",
      overlay3: "bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-blue-900/15 via-transparent to-purple-900/15"
    },
    subtle: {
      base: "bg-[#0a0a0a]",
      overlay1: "bg-gradient-to-br from-slate-950/30 via-blue-950/10 to-purple-950/10",
      overlay2: "bg-gradient-to-b from-black/10 via-black/5 to-black/15",
      overlay3: "bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-900/10 via-transparent to-purple-900/10"
    },
    intense: {
      base: "bg-[#0a0a0a]",
      overlay1: "bg-gradient-to-br from-slate-950/70 via-blue-950/30 to-purple-950/30",
      overlay2: "bg-gradient-to-b from-black/30 via-black/20 to-black/40",
      overlay3: "bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-blue-900/25 via-transparent to-purple-900/25"
    }
  }

  const currentVariant = variants[variant]

  return (
    <div className={cn("relative min-h-screen", className)}>
      {/* Base dark background matching landing page */}
      <div className={cn("absolute inset-0", currentVariant.base)}></div>
      
      {/* Primary gradient overlay */}
      <div className={cn("absolute inset-0", currentVariant.overlay1)}></div>
      
      {/* Depth gradient */}
      <div className={cn("absolute inset-0", currentVariant.overlay2)}></div>
      
      {/* Radial accent gradient */}
      <div className={cn("absolute inset-0", currentVariant.overlay3)}></div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

// Premium Page Wrapper Component
interface PremiumPageProps {
  children: ReactNode
  title?: string
  description?: string
  className?: string
  backgroundVariant?: "default" | "subtle" | "intense"
}

export function PremiumPage({ 
  children, 
  title, 
  description, 
  className,
  backgroundVariant = "default" 
}: PremiumPageProps) {
  return (
    <PremiumBackground variant={backgroundVariant} className={className}>
      <div className="min-h-screen flex flex-col">
        {title && (
          <div className="px-8 py-6 border-b border-slate-800/50">
            <div className="max-w-7xl mx-auto">
              <h1 className="text-3xl font-bold text-white tracking-tight">
                {title}
              </h1>
              {description && (
                <p className="text-slate-400 mt-2">
                  {description}
                </p>
              )}
            </div>
          </div>
        )}
        <div className="flex-1">
          {children}
        </div>
      </div>
    </PremiumBackground>
  )
}

// Premium Section Background
interface PremiumSectionBackgroundProps {
  children: ReactNode
  className?: string
  glow?: boolean
}

export function PremiumSectionBackground({ 
  children, 
  className,
  glow = false 
}: PremiumSectionBackgroundProps) {
  return (
    <div className={cn(
      "relative rounded-2xl overflow-hidden",
      glow && "glow-blue",
      className
    )}>
      {/* Section background */}
      <div className="absolute inset-0 bg-slate-900/40 backdrop-blur-xl"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-slate-800/30 via-transparent to-slate-900/30"></div>
      
      {/* Border gradient */}
      <div className="absolute inset-0 rounded-2xl border border-slate-700/30"></div>
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}
