"use client"

import { useEffect, useState, ReactElement } from "react"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"
import { generateUserFlowWithGemini, generateFlowExplanationWithGemini } from "@/lib/services/gemini-service"
import { saveUserFlow, getUserFlow, UserFlowStep } from "@/lib/services/user-flow-service"

interface FlowDiagramProps {
  project: AIAnalysisResult
}

export default function FlowDiagram({ project }: FlowDiagramProps) {
  const [userFlowSteps, setUserFlowSteps] = useState<UserFlowStep[]>([])
  const [flowExplanation, setFlowExplanation] = useState<string>("")
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadUserFlow()
  }, [project])

  const loadUserFlow = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // First, try to load existing flow from database
      const existingFlow = await getUserFlow(project.projectName)
      
      if (existingFlow && existingFlow.length > 0) {
        console.log("Loading existing user flow from database")
        const validatedFlow = createHorizontalFlowLayout(existingFlow)
        setUserFlowSteps(validatedFlow)
        // Generate explanation for existing flow
        const explanation = await generateFlowExplanationWithGemini({
          name: project.projectName,
          description: project.projectDescription,
          features: project.coreFeatures,
          flowSteps: existingFlow
        })
        setFlowExplanation(explanation)
      } else {
        // Generate new flow with Gemini only if none exists
        console.log("Generating new user flow with Gemini")
        const flowData = await generateUserFlowWithGemini({
          name: project.projectName,
          description: project.projectDescription,
          features: project.coreFeatures
        })
        
        // Generate explanation for the new flow
        const explanation = await generateFlowExplanationWithGemini({
          name: project.projectName,
          description: project.projectDescription,
          features: project.coreFeatures,
          flowSteps: flowData
        })
        
        // Create horizontal layout before saving
        const validatedFlow = createHorizontalFlowLayout(flowData)
        
        // Save the validated flow to database
        await saveUserFlow(project.projectName, validatedFlow)
        setUserFlowSteps(validatedFlow)
        setFlowExplanation(explanation)
      }
    } catch (err) {
      console.error("Error loading user flow:", err)
      setError("Failed to load user flow")
      // Fallback to default flow
      const defaultFlow = getDefaultUserFlow(project)
      const validatedDefaultFlow = createHorizontalFlowLayout(defaultFlow)
      setUserFlowSteps(validatedDefaultFlow)
      setFlowExplanation("This user flow diagram represents the core user journey through your application based on the AI analysis of your project idea. The diagram shows the main screens and interactions that users will experience.")
    } finally {
      setLoading(false)
    }
  }

  const getDefaultUserFlow = (project: AIAnalysisResult): UserFlowStep[] => {
    return [
      {
        id: "landing",
        title: "LANDING PAGE",
        icon: "🏠",
        points: [
          "Hero section with value proposition",
          "Feature highlights",
          "Pricing options",
          "Call to action"
        ],
        position: { x: 50, y: 50 },
        connections: ["auth"]
      },
      {
        id: "auth",
        title: "USER AUTHENTICATION",
        icon: "🔐",
        points: [
          "Sign up form",
          "Login functionality",
          "Password recovery",
          "Email verification"
        ],
        position: { x: 450, y: 50 },
        connections: ["dashboard"]
      },
      {
        id: "dashboard",
        title: "PROJECT DASHBOARD",
        icon: "📊",
        points: [
          "Project overview",
          "Quick actions",
          "Recent activity",
          "Navigation menu"
        ],
        position: { x: 850, y: 50 },
        connections: ["features", "collaboration"]
      },
      {
        id: "features",
        title: project.coreFeatures[0]?.name.toUpperCase() || "CORE FEATURES",
        icon: "⚡",
        points: [
          "Main functionality",
          "User interactions",
          "Data processing",
          "Results display"
        ],
        position: { x: 50, y: 400 },
        connections: ["export"]
      },
      {
        id: "collaboration",
        title: "COLLABORATION TOOLS",
        icon: "👥",
        points: [
          "Team management",
          "Sharing features",
          "Comments system",
          "Real-time updates"
        ],
        position: { x: 450, y: 400 },
        connections: ["export"]
      },
      {
        id: "export",
        title: "EXPORT & INTEGRATION",
        icon: "📤",
        points: [
          "Data export options",
          "API integrations",
          "Third-party connections",
          "Backup features"
        ],
        position: { x: 850, y: 400 },
        connections: []
      }
    ]
  }

  // Function to create intelligent flow layout with optimal spacing
  const createHorizontalFlowLayout = (steps: UserFlowStep[]): UserFlowStep[] => {
    const cardWidth = 320
    const cardHeight = 280
    const horizontalSpacing = 180 // Increased spacing to prevent connection overlap
    const verticalSpacing = 160 // Increased vertical spacing for better connection routing
    const topMargin = 100 // Increased space for arrows above cards
    const leftMargin = 60

    // Determine optimal layout based on number of steps
    let cardsPerRow: number
    if (steps.length <= 4) {
      cardsPerRow = steps.length // Single row for 4 or fewer cards
    } else if (steps.length <= 8) {
      cardsPerRow = Math.ceil(steps.length / 2) // 2 rows for 5-8 cards
    } else {
      cardsPerRow = Math.ceil(steps.length / 3) // 3 rows for more cards
    }

    return steps.map((step, index) => {
      const row = Math.floor(index / cardsPerRow)
      const col = index % cardsPerRow

      return {
        ...step,
        position: {
          x: leftMargin + col * (cardWidth + horizontalSpacing),
          y: topMargin + row * (cardHeight + verticalSpacing)
        }
      }
    })
  }

  const renderConnections = () => {
    const connections: ReactElement[] = []
    const markers: ReactElement[] = []

    userFlowSteps.forEach(step => {
      step.connections.forEach(connectionId => {
        const targetStep = userFlowSteps.find(s => s.id === connectionId)
        if (!targetStep) return

        const cardWidth = 320
        const cardHeight = 280

        const markerId = `arrowhead-${step.id}-${connectionId}`

        // Add marker definition
        markers.push(
          <marker
            key={markerId}
            id={markerId}
            markerWidth="12"
            markerHeight="12"
            refX="10"
            refY="6"
            orient="auto"
            markerUnits="strokeWidth"
          >
            <polygon
              points="0 3, 9 6, 0 9"
              fill="#3b82f6"
              stroke="#3b82f6"
              strokeWidth="1"
            />
          </marker>
        )

        // Calculate smart connection points that avoid card overlap
        const sourceIndex = userFlowSteps.findIndex(s => s.id === step.id)
        const targetIndex = userFlowSteps.findIndex(s => s.id === connectionId)
        
        // Determine layout parameters
        let cardsPerRow: number
        if (userFlowSteps.length <= 4) {
          cardsPerRow = userFlowSteps.length
        } else if (userFlowSteps.length <= 8) {
          cardsPerRow = Math.ceil(userFlowSteps.length / 2)
        } else {
          cardsPerRow = Math.ceil(userFlowSteps.length / 3)
        }

        const sourceRow = Math.floor(sourceIndex / cardsPerRow)
        const targetRow = Math.floor(targetIndex / cardsPerRow)
        const sourceCol = sourceIndex % cardsPerRow
        const targetCol = targetIndex % cardsPerRow

        // Connection points on card edges
        const startX = step.position.x + cardWidth
        const startY = step.position.y + cardHeight / 2
        const endX = targetStep.position.x
        const endY = targetStep.position.y + cardHeight / 2

        let pathData: string

        if (sourceRow === targetRow) {
          // Same row - route above or below cards
          const routeY = sourceCol < targetCol 
            ? step.position.y - 50 // Route above for forward connections
            : step.position.y + cardHeight + 50 // Route below for backward connections
          
          pathData = `M ${startX} ${startY} 
                      L ${startX + 30} ${startY} 
                      L ${startX + 30} ${routeY} 
                      L ${endX - 30} ${routeY} 
                      L ${endX - 30} ${endY} 
                      L ${endX} ${endY}`
        } else {
          // Different rows - create smooth curved path that avoids all cards
          const isGoingDown = sourceRow < targetRow
          const horizontalDistance = Math.abs(endX - startX)
          const verticalDistance = Math.abs(endY - startY)
          
          // Calculate intermediate points that route around cards
          const clearance = 60
          const routeX1 = startX + 40
          const routeX2 = endX - 40
          
          let routeY1: number, routeY2: number
          
          if (isGoingDown) {
            // Going down - route below source card and above target card
            routeY1 = step.position.y + cardHeight + clearance
            routeY2 = targetStep.position.y - clearance
          } else {
            // Going up - route above source card and below target card  
            routeY1 = step.position.y - clearance
            routeY2 = targetStep.position.y + cardHeight + clearance
          }

          // Create smooth path with proper clearance
          if (horizontalDistance > 200) {
            // Long horizontal distance - use direct routing with curves
            const midX = startX + (endX - startX) / 2
            pathData = `M ${startX} ${startY} 
                        L ${routeX1} ${startY} 
                        L ${routeX1} ${routeY1} 
                        L ${midX} ${routeY1} 
                        L ${midX} ${routeY2} 
                        L ${routeX2} ${routeY2} 
                        L ${routeX2} ${endY} 
                        L ${endX} ${endY}`
          } else {
            // Short horizontal distance - use wider routing to avoid overlap
            const wideRouteX = Math.max(startX + 100, endX + 100)
            pathData = `M ${startX} ${startY} 
                        L ${routeX1} ${startY} 
                        L ${routeX1} ${routeY1} 
                        L ${wideRouteX} ${routeY1} 
                        L ${wideRouteX} ${routeY2} 
                        L ${routeX2} ${routeY2} 
                        L ${routeX2} ${endY} 
                        L ${endX} ${endY}`
          }
        }

        // Add connection path with improved styling
        connections.push(
          <g key={`${step.id}-${connectionId}`}>
            {/* Shadow/glow effect */}
            <path
              d={pathData}
              stroke="#1e40af"
              strokeWidth="6"
              fill="none"
              opacity="0.3"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            {/* Main connection line */}
            <path
              d={pathData}
              stroke="#3b82f6"
              strokeWidth="3"
              fill="none"
              opacity="0.9"
              markerEnd={`url(#${markerId})`}
              strokeDasharray="10,5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            {/* Highlight effect */}
            <path
              d={pathData}
              stroke="#60a5fa"
              strokeWidth="1"
              fill="none"
              opacity="0.8"
              strokeDasharray="10,5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </g>
        )
      })
    })

    return (
      <>
        <defs>{markers}</defs>
        {connections}
      </>
    )
  }

  if (loading) {
    return (
      <div className="w-full h-96 bg-slate-900 rounded-lg border border-slate-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-slate-400">Generating user flow with AI...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="w-full h-96 bg-slate-900 rounded-lg border border-slate-800 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <p className="text-slate-400">Using default flow structure</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col space-y-6 h-full">
      {/* Flow Diagram */}
      <div className="flex flex-col bg-slate-900 rounded-lg border border-slate-800 flex-1 min-h-0">
        <div className="p-6 border-b border-slate-800 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-white">{project.projectName} User Flow</h2>
              <p className="text-slate-400 text-sm mt-1">Visual representation of the user journey through your application</p>
            </div>
          </div>
        </div>

        {/* Scrollable diagram container */}
        <div className="overflow-auto bg-slate-950/50 flex-1 min-h-0">
          <div
            className="relative p-8"
            style={{
              width: `${(() => {
                const cardWidth = 320
                const horizontalSpacing = 180
                const leftMargin = 60

                let cardsPerRow: number
                if (userFlowSteps.length <= 4) {
                  cardsPerRow = userFlowSteps.length
                } else if (userFlowSteps.length <= 8) {
                  cardsPerRow = Math.ceil(userFlowSteps.length / 2)
                } else {
                  cardsPerRow = Math.ceil(userFlowSteps.length / 3)
                }

                return Math.max(800, leftMargin * 2 + cardsPerRow * (cardWidth + horizontalSpacing))
              })()}px`,
              height: `${(() => {
                const cardHeight = 280
                const verticalSpacing = 160
                const topMargin = 100
                const bottomMargin = 60

                let cardsPerRow: number
                if (userFlowSteps.length <= 4) {
                  cardsPerRow = userFlowSteps.length
                } else if (userFlowSteps.length <= 8) {
                  cardsPerRow = Math.ceil(userFlowSteps.length / 2)
                } else {
                  cardsPerRow = Math.ceil(userFlowSteps.length / 3)
                }

                const rows = Math.ceil(userFlowSteps.length / cardsPerRow)
                return Math.max(600, topMargin + bottomMargin + rows * cardHeight + (rows - 1) * verticalSpacing)
              })()}px`
            }}
          >
          {/* Background grid for better visibility */}
          <div className="absolute inset-0 opacity-10">
            <svg className="w-full h-full">
              <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#475569" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>
          
          <svg
            className="absolute inset-0 w-full h-full pointer-events-none"
            style={{ zIndex: 1 }}
          >
            {renderConnections()}
          </svg>
          
          {userFlowSteps.map((step) => (
          <div
            key={step.id}
            className="absolute bg-slate-800 border border-slate-700 rounded-lg p-6 shadow-lg hover:shadow-xl transition-all duration-200 hover:border-slate-600"
            style={{
              left: step.position.x,
              top: step.position.y,
              width: '320px',
              minHeight: '280px',
              zIndex: 2
            }}
          >
            {/* Card Header */}
            <div className="flex items-center gap-3 mb-4">
              <div className="text-2xl">{step.icon}</div>
              <div>
                <h3 className="text-white font-semibold text-sm tracking-wide">
                  {step.title}
                </h3>
                <div className="flex items-center gap-2 mt-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-xs text-slate-400">Active</span>
                </div>
              </div>
              <div className="ml-auto">
                <svg 
                  className="w-4 h-4 text-slate-400" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
                  />
                </svg>
              </div>
            </div>

            {/* Card Content - 4 Points */}
            <div className="space-y-3">
              {step.points.map((point, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-slate-700 rounded border border-slate-600 flex items-center justify-center">
                    <span className="text-xs text-slate-300 font-medium">{index + 1}</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-slate-300 leading-relaxed">{point}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Card Footer */}
            <div className="mt-4 pt-3 border-t border-slate-700 flex items-center justify-between">
              <span className="text-xs text-slate-500">
                {step.connections.length} connection{step.connections.length !== 1 ? 's' : ''}
              </span>
              <button className="text-xs text-blue-400 hover:text-blue-300 transition-colors">
                Connect
              </button>
            </div>
          </div>
        ))}
          </div>
        </div>
      </div>

      {/* AI-Generated About This Flow Section */}
      {flowExplanation && (
        <div className="bg-slate-900 rounded-lg border border-slate-800 p-6 flex-shrink-0">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">About This Flow</h3>
            <div className="text-xs text-slate-500 bg-slate-800 px-2 py-1 rounded">
              AI Generated
            </div>
          </div>
          <p className="text-slate-300 leading-relaxed">{flowExplanation}</p>

          <div className="mt-4 space-y-2">
            <h4 className="text-sm font-medium text-slate-400">Key Components:</h4>
            <div className="flex flex-wrap gap-2">
              {userFlowSteps.map((step) => (
                <span
                  key={step.id}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-slate-800 border border-slate-700 rounded text-xs text-slate-300"
                >
                  <span>{step.icon}</span>
                  <span>{step.title}</span>
                </span>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
