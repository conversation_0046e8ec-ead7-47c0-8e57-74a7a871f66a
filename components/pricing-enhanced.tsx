"use client"

import { motion } from 'framer-motion';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Grad<PERSON>Card, GradientCardContent, GradientCardHeader, GradientCardTitle } from "@/components/ui/gradient-card";
import MagneticHover from "@/components/animations/MagneticHover";
import StaggeredText from "@/components/animations/StaggeredText";
import ScrollReveal from "@/components/animations/ScrollReveal";
import { Check, Star, Zap, Crown } from "lucide-react";

const pricingPlans = [
  {
    name: "Starter",
    price: 0,
    period: "forever",
    description: "Perfect for getting started",
    icon: Zap,
    features: [
      "Basic AI Analysis",
      "Community Support", 
      "Limited Projects",
      "Basic Templates"
    ],
    popular: false,
    gradient: "from-slate-600 to-slate-700",
    buttonStyle: "outline"
  },
  {
    name: "Pro",
    price: 29,
    period: "month",
    description: "For serious entrepreneurs",
    icon: Star,
    features: [
      "All Starter Features",
      "Unlimited Projects",
      "Premium AI Tools",
      "Email Support",
      "Advanced Analytics",
      "Custom Integrations"
    ],
    popular: true,
    gradient: "from-blue-500 to-purple-500",
    buttonStyle: "primary"
  },
  {
    name: "Enterprise",
    price: "Custom",
    period: "contact us",
    description: "For large organizations",
    icon: Crown,
    features: [
      "All Pro Features",
      "Custom Integrations",
      "Dedicated Support",
      "SLA Guarantee",
      "Advanced Security",
      "Team Management"
    ],
    popular: false,
    gradient: "from-emerald-500 to-teal-500",
    buttonStyle: "outline"
  }
];

export default function EnhancedPricing() {
  return (
    <section className="relative py-16 sm:py-20 lg:py-24" id="pricing">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-500/5 to-transparent" />
      
      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        {/* Section header */}
        <ScrollReveal>
          <div className="text-center space-y-6 mb-16">
            <div className="inline-flex items-center rounded-full bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-xl border border-purple-500/30 px-4 py-2 text-sm text-purple-300">
              <span className="mr-2">💎</span>
              Simple, Transparent Pricing
            </div>
            
            <StaggeredText
              text="Choose the perfect plan for your journey"
              className="text-3xl sm:text-4xl md:text-5xl font-bold tracking-tight text-white"
              delay={0.2}
              duration={0.6}
              staggerDelay={0.08}
            />
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-lg sm:text-xl text-slate-300 max-w-3xl mx-auto"
            >
              Start free, scale as you grow. No hidden fees, cancel anytime.
            </motion.p>
          </div>
        </ScrollReveal>

        {/* Pricing cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {pricingPlans.map((plan, index) => (
            <ScrollReveal key={plan.name} delay={0.1 + index * 0.1}>
              <MagneticHover strength={0.1}>
                <motion.div
                  whileHover={{
                    scale: plan.popular ? 1.02 : 1.05,
                    rotateY: 5,
                    rotateX: 5,
                    transition: {
                      type: "spring",
                      stiffness: 300,
                      damping: 20
                    }
                  }}
                  style={{ transformStyle: "preserve-3d" }}
                  className={`relative ${plan.popular ? 'md:-mt-4 md:mb-4' : ''}`}
                >
                  <GradientCard 
                    className={`relative overflow-hidden transition-all duration-500 ${
                      plan.popular 
                        ? 'ring-2 ring-blue-500/50 shadow-2xl shadow-blue-500/20' 
                        : 'hover:shadow-xl'
                    }`}
                  >
                    {/* Popular badge */}
                    {plan.popular && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20"
                      >
                        <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                          Most Popular
                        </div>
                      </motion.div>
                    )}

                    <GradientCardHeader className="text-center space-y-4 pb-8">
                      {/* Icon */}
                      <motion.div
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.6 }}
                        className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${plan.gradient} shadow-lg`}
                      >
                        <plan.icon className="w-8 h-8 text-white" />
                      </motion.div>

                      {/* Plan name and description */}
                      <div>
                        <GradientCardTitle className="text-2xl font-bold text-white mb-2">
                          {plan.name}
                        </GradientCardTitle>
                        <p className="text-slate-400">{plan.description}</p>
                      </div>

                      {/* Price */}
                      <div className="space-y-1">
                        <div className="flex items-baseline justify-center">
                          {typeof plan.price === 'number' ? (
                            <>
                              <span className="text-4xl font-bold text-white">${plan.price}</span>
                              <span className="text-slate-400 ml-1">/{plan.period}</span>
                            </>
                          ) : (
                            <span className="text-4xl font-bold text-white">{plan.price}</span>
                          )}
                        </div>
                      </div>
                    </GradientCardHeader>

                    <GradientCardContent className="space-y-6">
                      {/* Features list */}
                      <ul className="space-y-3">
                        {plan.features.map((feature, featureIndex) => (
                          <motion.li
                            key={feature}
                            initial={{ opacity: 0, x: -20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ 
                              duration: 0.4, 
                              delay: 0.1 + featureIndex * 0.05 
                            }}
                            className="flex items-center space-x-3"
                          >
                            <div className={`flex-shrink-0 w-5 h-5 rounded-full bg-gradient-to-r ${plan.gradient} flex items-center justify-center`}>
                              <Check className="w-3 h-3 text-white" />
                            </div>
                            <span className="text-slate-300">{feature}</span>
                          </motion.li>
                        ))}
                      </ul>

                      {/* CTA Button */}
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Button
                          className={`w-full py-3 rounded-xl font-medium text-lg transition-all duration-300 ${
                            plan.buttonStyle === 'primary'
                              ? `bg-gradient-to-r ${plan.gradient} text-white shadow-lg hover:shadow-xl`
                              : 'border-slate-700 text-slate-300 hover:text-white hover:border-slate-600'
                          }`}
                          variant={plan.buttonStyle === 'primary' ? 'default' : 'outline'}
                        >
                          {plan.name === 'Enterprise' ? 'Contact Sales' : 'Get Started'}
                        </Button>
                      </motion.div>
                    </GradientCardContent>

                    {/* Hover glow effect */}
                    <motion.div
                      className={`absolute -inset-1 bg-gradient-to-r ${plan.gradient} rounded-2xl blur-xl opacity-0 group-hover:opacity-20 transition-opacity duration-500`}
                      initial={false}
                      whileHover={{ opacity: 0.2 }}
                    />
                  </GradientCard>
                </motion.div>
              </MagneticHover>
            </ScrollReveal>
          ))}
        </div>

        {/* Bottom CTA */}
        <ScrollReveal>
          <motion.div
            className="text-center mt-16 space-y-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <p className="text-slate-400">
              Need a custom solution? <span className="text-blue-400 hover:text-blue-300 cursor-pointer">Contact our sales team</span>
            </p>
            <p className="text-sm text-slate-500">
              All plans include a 14-day free trial. No credit card required.
            </p>
          </motion.div>
        </ScrollReveal>
      </div>
    </section>
  );
}
