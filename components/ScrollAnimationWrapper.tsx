'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
}

const ScrollAnimationWrapper = ({ children, direction = 'up', delay = 0 }: Props) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['0 1', '1.2 1']
  });

  const getInitialAndTargetValues = () => {
    switch (direction) {
      case 'up':
        return { y: [50, 0], opacity: [0, 1] };
      case 'down':
        return { y: [-50, 0], opacity: [0, 1] };
      case 'left':
        return { x: [50, 0], opacity: [0, 1] };
      case 'right':
        return { x: [-50, 0], opacity: [0, 1] };
      default:
        return { y: [50, 0], opacity: [0, 1] };
    }
  };

  const { x, y, opacity } = getInitialAndTargetValues();

  const animatedY = useTransform(scrollYProgress, [0, 1], y || [0, 0]);
  const animatedX = useTransform(scrollYProgress, [0, 1], x || [0, 0]);
  const animatedOpacity = useTransform(scrollYProgress, [0, 1], opacity);

  return (
    <motion.div
      ref={ref}
      style={{
        x: animatedX,
        y: animatedY,
        opacity: animatedOpacity,
      }}
      transition={{
        duration: 0.8,
        delay,
        ease: [0.17, 0.55, 0.55, 1],
      }}
    >
      {children}
    </motion.div>
  );
};

export default ScrollAnimationWrapper; 