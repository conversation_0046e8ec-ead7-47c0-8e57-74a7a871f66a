"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Brain,
  Users,
  Settings,
  ChevronLeft,
  ChevronRight,
  BookOpen,
  HelpCircle,
  Sparkles,
} from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"

const navigation = [
  {
    name: "Dashboard",
    href: "/studio",
    icon: LayoutDashboard,
  },
  {
    name: "AI Insights",
    href: "/studio/ai-insights",
    icon: Brain,
  },
  {
    name: "AI Idea Validator",
    href: "/studio/idea-validator",
    icon: Sparkles,
  },
  {
    name: "Settings",
    href: "/studio/settings",
    icon: Settings,
  },
]

const resources = [
  {
    name: "Documentation",
    href: "/docs",
    icon: <PERSON><PERSON><PERSON>,
  },
  {
    name: "Help & Support",
    href: "/support",
    icon: HelpCircle,
  },
]

export default function SidebarNav() {
  const [collapsed, setCollapsed] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [projectName, setProjectName] = useState("")
  const [projectDesc, setProjectDesc] = useState("")
  const pathname = usePathname()

  const handleCreateProject = () => {
    // TODO: Implement project creation logic
    setShowModal(false)
    setProjectName("")
    setProjectDesc("")
  }

  return (
    <div
      className={cn(
        "flex flex-col relative transition-all duration-500 flex-shrink-0 h-full overflow-hidden",
        collapsed ? "w-16" : "w-72",
      )}
    >
      {/* Premium Dark Background matching landing page */}
      <div className="absolute inset-0 bg-[#0a0a0a]"></div>

      {/* Landing page gradient overlays */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-950/50 via-blue-950/20 to-purple-950/20"></div>
      <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/10 to-black/30" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-blue-900/15 via-transparent to-purple-900/15"></div>

      {/* Border */}
      <div className="absolute inset-y-0 right-0 w-px bg-slate-700/30"></div>

      {/* Content */}
      <div className="relative z-10 flex flex-col h-full">
        {/* Header */}
        <div className={cn(
          "flex items-center border-b border-slate-700/30 p-6",
          collapsed ? "justify-center" : "justify-between"
        )}>
          {!collapsed && (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center glow-blue">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="font-bold text-white text-lg tracking-tight">SassifyX</span>
            </div>
          )}
          {collapsed && (
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center glow-blue">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
          )}
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-6 overflow-y-auto">
          <div className="space-y-3">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link key={item.name} href={item.href}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full text-slate-300 hover:text-white hover:bg-slate-700/50 rounded-xl transition-all duration-200 h-12",
                      isActive && "bg-gradient-to-r from-blue-600/20 to-purple-600/20 text-white border border-blue-500/30 glow-blue",
                      collapsed ? "justify-center px-0" : "justify-start px-4",
                    )}
                  >
                    <item.icon className={cn("w-5 h-5", isActive && "text-blue-400")} />
                    {!collapsed && <span className="ml-3 font-medium">{item.name}</span>}
                  </Button>
                </Link>
              )
            })}
          </div>

          {/* Resources */}
          <div className="mt-8">
            {!collapsed && (
              <div className="px-4 mb-4">
                <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wider">Resources</h3>
              </div>
            )}
            <div className="space-y-2">
              {resources.map((item) => (
                <Link key={item.name} href={item.href}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-xl transition-all duration-200 h-10",
                      collapsed ? "justify-center px-0" : "justify-start px-4",
                    )}
                  >
                    <item.icon className="w-4 h-4" />
                    {!collapsed && <span className="ml-3 text-sm">{item.name}</span>}
                  </Button>
                </Link>
              ))}
            </div>
          </div>
        </nav>

        {/* Always show collapse/expand button at the bottom */}
        <div className="p-4 border-t border-slate-700/30 flex flex-col gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCollapsed(c => !c)}
            className="w-full justify-center text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-all duration-200 h-10"
          >
            {collapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* Modal for new project */}
      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Input
              placeholder="Project Name"
              value={projectName}
              onChange={e => setProjectName(e.target.value)}
            />
            <Input
              placeholder="Project Description"
              value={projectDesc}
              onChange={e => setProjectDesc(e.target.value)}
            />
            <Button
              className="w-full bg-blue-600 text-white hover:bg-blue-500"
              onClick={handleCreateProject}
              disabled={!projectName.trim()}
            >
              Create
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
