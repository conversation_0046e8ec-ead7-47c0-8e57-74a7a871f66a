"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Search, Bell, User, Settings, LogOut } from "lucide-react"
import { useAuth } from "@/components/providers/auth-provider"
import { useRouter } from "next/navigation"

interface HeaderProps {
  onOpenAIModal?: () => void
}

export default function Header({ onOpenAIModal }: HeaderProps) {
  const { signOut } = useAuth()
  const router = useRouter()

  const handleSignOut = async () => {
    try {
      await signOut()
      router.push("/")
    } catch (error) {
      console.error("Error signing out:", error)
    }
  }

  return (
    <header className="flex items-center justify-between px-8 py-6 bg-slate-900/40 backdrop-blur-2xl border-0 shadow-xl">
      {/* Search */}
      <div className="flex items-center gap-6 flex-1 max-w-lg">
        <div className="relative flex-1">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
          <Input
            placeholder="Search projects, ideas..."
            className="pl-12 pr-4 py-3 bg-slate-800/50 backdrop-blur-sm border-slate-700/50 text-white placeholder-slate-400 focus:border-blue-500/50 focus:bg-slate-800/70 rounded-xl transition-all duration-200"
          />
        </div>
      </div>

      {/* Right side */}
      <div className="flex items-center gap-4">
        {/* Notifications */}
        <Button variant="ghost" size="sm" className="relative text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-xl transition-all duration-200 h-10 w-10">
          <Bell className="w-5 h-5" />
          <Badge className="absolute -top-1 -right-1 w-5 h-5 p-0 flex items-center justify-center bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full glow-amber">
            3
          </Badge>
        </Button>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-xl transition-all duration-200 h-10 w-10">
              <User className="w-5 h-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-64 bg-slate-800/90 backdrop-blur-xl border-slate-700/50 rounded-xl shadow-2xl">
            <DropdownMenuLabel className="text-white font-medium px-4 py-3">My Account</DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-slate-700/50" />
            <DropdownMenuItem className="text-slate-300 hover:text-white hover:bg-slate-700/50 rounded-lg mx-2 my-1 transition-all duration-200">
              <User className="mr-3 h-4 w-4" />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem className="text-slate-300 hover:text-white hover:bg-slate-700/50 rounded-lg mx-2 my-1 transition-all duration-200">
              <Settings className="mr-3 h-4 w-4" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-slate-700/50" />
            <DropdownMenuItem
              onClick={handleSignOut}
              className="text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg mx-2 my-1 cursor-pointer transition-all duration-200"
            >
              <LogOut className="mr-3 h-4 w-4" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}
