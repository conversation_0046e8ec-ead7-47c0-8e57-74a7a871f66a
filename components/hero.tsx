"use client"

import { motion } from 'framer-motion';
import { But<PERSON> } from "@/components/ui/button"
import { GradientCard } from "@/components/ui/gradient-card"
import Link from "next/link"
import { Suspense, lazy } from "react"

// Lazy load the Beams component to improve initial page load
const Beams = lazy(() => import("@/components/backgrounds/beams"))

// Fallback background for when <PERSON><PERSON> is loading or fails
const FallbackBackground = () => (
  <div className="absolute inset-0 bg-[#0a0a0a]">
    <div className="absolute inset-0 bg-gradient-to-br from-slate-950/50 via-blue-950/20 to-purple-950/20"></div>
    <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-500/10 via-transparent to-transparent"></div>
  </div>
)

export default function Hero() {
  return (
    <section className="relative py-20 sm:py-24 lg:py-32">
      {/* Animated badge */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <motion.div
          className="inline-flex items-center rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-xl border border-blue-500/30 px-4 py-2 text-sm text-blue-300"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span className="mr-2">🚀</span>
          Validate and build your SaaS ideas efficiently
        </motion.div>
      </motion.div>

      {/* Main headline */}
      <div className="max-w-4xl">
        <motion.h1
          className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight text-white mb-6 font-display"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.span
            className="block"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            SassifyX
          </motion.span>
          <motion.span
            className="block bg-gradient-to-r from-blue-400 to-purple-400 text-transparent bg-clip-text"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            ideas into reality
          </motion.span>
        </motion.h1>
        {/* Tagline */}
        <motion.div
          className="text-xl sm:text-2xl font-semibold text-slate-300 mb-8 font-display tracking-wide"
          initial={{ opacity: 0, y: 24 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 1 }}
        >
          Sketch it. <span className="text-blue-400">SaaSify</span> it. <span className="text-purple-400">Xecute</span> it.
        </motion.div>

        <motion.p
          className="text-lg sm:text-xl text-slate-300 mb-8 max-w-2xl"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          AI-powered platform that analyzes, validates, and helps you build successful SaaS applications from concept to launch.
        </motion.p>

        {/* CTA Buttons */}
        <motion.div
          className="flex flex-wrap gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              size="lg"
              className="bg-blue-600 hover:bg-blue-500 text-white px-8 py-6 rounded-xl font-medium text-lg relative overflow-hidden group"
            >
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-blue-400/40 to-purple-400/40"
                initial={{ x: '100%' }}
                whileHover={{ x: '-100%' }}
                transition={{ duration: 0.5, ease: 'easeInOut' }}
              />
              <span className="relative z-10">Get Started Free</span>
            </Button>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="outline"
              size="lg"
              className="border-slate-700 text-slate-300 hover:text-white px-8 py-6 rounded-xl font-medium text-lg relative overflow-hidden group"
              onClick={e => {
                e.preventDefault();
                const el = document.getElementById('features');
                if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
              }}
            >
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-slate-800/50 to-slate-700/50"
                initial={{ x: '100%' }}
                whileHover={{ x: '-100%' }}
                transition={{ duration: 0.5, ease: 'easeInOut' }}
              />
              <span className="relative z-10">Explore Features</span>
            </Button>
          </motion.div>
        </motion.div>

        {/* Market Feasibility Score Card */}
        <motion.div
          className="mt-12 bg-slate-900/50 backdrop-blur-lg rounded-2xl p-6 border border-slate-800 max-w-md"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          whileHover={{ 
            scale: 1.02,
            boxShadow: '0 0 20px rgba(59,130,246,0.2)'
          }}
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-slate-200">Market Feasibility</h3>
            <span className="text-2xl font-bold text-blue-400">8.2/10</span>
          </div>
          
          <div className="space-y-4">
            {[
              { label: 'Uniqueness', value: 85, color: 'emerald' },
              { label: 'Stickiness', value: 92, color: 'blue' },
              { label: 'Growth', value: 78, color: 'purple' },
              { label: 'Pricing', value: 88, color: 'teal' }
            ].map(({ label, value, color }) => (
              <div key={label} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">{label}</span>
                  <span className="text-slate-300">{value}%</span>
                </div>
                <motion.div 
                  className="h-1.5 rounded-full bg-slate-800"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 1, delay: 0.8 }}
                >
                  <motion.div
                    className={`h-full rounded-full bg-${color}-500`}
                    initial={{ width: 0 }}
                    animate={{ width: `${value}%` }}
                    transition={{ duration: 1, delay: 1 }}
                  />
                </motion.div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
