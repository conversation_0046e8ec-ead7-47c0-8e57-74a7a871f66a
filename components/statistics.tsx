"use client"

import { GradientCard, GradientCardContent } from "@/components/ui/gradient-card"
import CountUp from "@/components/animations/count-up"
import ScrollFloat from "@/components/animations/scroll-float"
import MagneticHover from "@/components/animations/MagneticHover"
import StaggeredText from "@/components/animations/StaggeredText"
import { motion } from 'framer-motion'

export default function Statistics() {
  return (
    <section className="relative py-24 lg:py-32">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-500/5 via-purple-500/5 to-emerald-500/5" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(59,130,246,0.1)_0%,transparent_50%)]" />

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <div className="flex flex-col items-center justify-center space-y-12 text-center">
          <ScrollFloat direction="up" className="space-y-6 max-w-4xl">
            <div className="inline-flex items-center rounded-full bg-gradient-to-r from-amber-500/20 to-orange-500/20 backdrop-blur-xl border border-amber-500/30 px-4 py-2 text-sm text-amber-300">
              <span className="mr-2">📊</span>
              Proven Results
            </div>

            <StaggeredText
              text="Trusted by developers worldwide"
              className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight text-white"
              delay={0.2}
              duration={0.8}
              staggerDelay={0.08}
            />

            <p className="mx-auto max-w-3xl text-xl md:text-2xl text-slate-300 leading-relaxed">
              Join thousands of successful developers who are building profitable SaaS applications with our premium
              platform.
            </p>
          </ScrollFloat>

          <div className="w-full pt-12">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
              {statistics.map((stat, index) => (
                <ScrollFloat key={stat.label} direction="up" delay={0.05 * (index + 1)} className="w-full">
                  <MagneticHover strength={0.08}>
                    <motion.div
                      whileHover={{
                        scale: 1.05,
                        rotateY: 8,
                        rotateX: 8,
                        transition: {
                          type: "spring",
                          stiffness: 300,
                          damping: 20
                        }
                      }}
                      style={{ transformStyle: "preserve-3d" }}
                    >
                      <GradientCard variant="statistic" className="group hover:glow-emerald transition-all duration-500 relative overflow-hidden">
                        <GradientCardContent className="flex flex-col items-center justify-center p-8 text-center relative z-10">
                          <motion.div
                            className="text-4xl sm:text-5xl lg:text-6xl font-bold gradient-text mb-3"
                            whileHover={{
                              scale: 1.1,
                              transition: { duration: 0.3 }
                            }}
                          >
                            <CountUp from={0} to={stat.value} separator="," duration={1.5} />
                            <span>{stat.suffix}</span>
                          </motion.div>
                          <p className="text-sm sm:text-base text-slate-300 font-medium">{stat.label}</p>
                          <motion.div
                            className="mt-2 h-1 w-12 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"
                            whileHover={{
                              width: "3rem",
                              transition: { duration: 0.3 }
                            }}
                          />
                        </GradientCardContent>

                        {/* Hover effect particles */}
                        <motion.div
                          className="absolute inset-0 opacity-0 group-hover:opacity-100 pointer-events-none"
                          initial={false}
                        >
                          {[...Array(4)].map((_, i) => (
                            <motion.div
                              key={i}
                              className="absolute w-1 h-1 bg-emerald-400 rounded-full"
                              style={{
                                left: `${20 + i * 20}%`,
                                top: `${20 + (i % 2) * 60}%`,
                              }}
                              animate={{
                                scale: [0, 1, 0],
                                opacity: [0, 1, 0],
                              }}
                              transition={{
                                duration: 2,
                                delay: i * 0.2,
                                repeat: Infinity,
                              }}
                            />
                          ))}
                        </motion.div>
                      </GradientCard>
                    </motion.div>
                  </MagneticHover>
                </ScrollFloat>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

const statistics = [
  {
    value: 5000,
    suffix: "+",
    label: "Projects Analyzed",
  },
  {
    value: 87,
    suffix: "%",
    label: "Success Rate",
  },
  {
    value: 3200,
    suffix: "+",
    label: "Active Users",
  },
  {
    value: 42,
    suffix: "%",
    label: "Time Saved",
  },
]
