'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';

const SplashScreen = ({ onComplete }: { onComplete: () => void }) => {
  const [showFullLogo, setShowFullLogo] = useState(false);
  const [progress, setProgress] = useState(0);
  const [done, setDone] = useState(false);

  useEffect(() => {
    // Animate loading bar
    let frame: number;
    let start: number | null = null;
    const duration = 1800; // ms
    function animateBar(ts: number) {
      if (!start) start = ts;
      const elapsed = ts - start;
      const pct = Math.min(100, (elapsed / duration) * 100);
      setProgress(pct);
      if (pct < 100) {
        frame = requestAnimationFrame(animateBar);
      } else {
        setTimeout(() => setDone(true), 350); // short pause at 100%
      }
    }
    frame = requestAnimationFrame(animateBar);

    // Show full logo after X animation
    const timer1 = setTimeout(() => setShowFullLogo(true), 600);

    return () => {
      clearTimeout(timer1);
      cancelAnimationFrame(frame);
    };
  }, []);

  // Call onComplete after exit animation
  useEffect(() => {
    if (done) {
      const timer = setTimeout(() => onComplete(), 500);
      return () => clearTimeout(timer);
    }
  }, [done, onComplete]);

  return (
    <AnimatePresence>
      {!done && (
        <motion.div
          className="fixed inset-0 z-50 flex flex-col items-center justify-center"
          style={{
            background: '#101014',
          }}
          initial={{ opacity: 1, scale: 1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 1.08 }}
          transition={{ duration: 0.5 }}
        >
          {/* Neon grid overlay */}
          <div className="absolute inset-0 z-0 pointer-events-none">
            <svg width="100%" height="100%" className="w-full h-full" style={{ opacity: 0.12 }}>
              <defs>
                <linearGradient id="grokGrid" x1="0" y1="0" x2="1" y2="1">
                  <stop offset="0%" stopColor="#7c3aed" />
                  <stop offset="100%" stopColor="#06b6d4" />
                </linearGradient>
              </defs>
              {Array.from({ length: 20 }).map((_, i) => (
                <line key={i} x1={i * 60} y1={0} x2={i * 60} y2={1200} stroke="url(#grokGrid)" strokeWidth="1" />
              ))}
              {Array.from({ length: 20 }).map((_, i) => (
                <line key={i} x1={0} y1={i * 60} x2={1200} y2={i * 60} stroke="url(#grokGrid)" strokeWidth="1" />
              ))}
            </svg>
          </div>
          <div className="relative flex items-center mb-10 z-10">
            {/* Animate in the "Sassify" text */}
            {showFullLogo && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                className="flex"
              >
                {"Sassify".split('').map((letter, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.3,
                      delay: index * 0.08,
                      ease: "easeOut"
                    }}
                    className="text-7xl font-bold bg-gradient-to-r from-slate-100 to-zinc-400 text-transparent bg-clip-text drop-shadow-[0_0_16px_#7c3aed]"
                    style={{
                      textShadow: '0 0 24px #06b6d4, 0 0 8px #7c3aed',
                    }}
                  >
                    {letter}
                  </motion.span>
                ))}
              </motion.div>
            )}
            {/* Animated X */}
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ 
                scale: 1, 
                rotate: 0,
                transition: { 
                  duration: 1,
                  type: "spring",
                  stiffness: 200
                }
              }}
              className="ml-4"
            >
              <motion.span
                className="text-8xl font-black inline-block"
                style={{
                  background: 'linear-gradient(90deg, #e5e7eb 0%, #a3a3a3 100%)',
                  color: 'transparent',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  textShadow: '0 0 32px #06b6d4, 0 0 16px #7c3aed',
                  filter: 'drop-shadow(0 0 24px #06b6d4cc)'
                }}
                animate={{
                  scale: [1, 1.15, 1],
                  rotate: [0, 10, -10, 0],
                }}
                transition={{
                  duration: 2,
                  ease: "easeInOut",
                  times: [0, 0.5, 1],
                  repeat: Infinity
                }}
              >
                X
              </motion.span>
            </motion.div>
          </div>
          {/* Percentage */}
          <div className="mb-2 text-lg font-semibold tracking-widest drop-shadow z-10" style={{ color: '#e5e7eb', textShadow: '0 0 8px #06b6d4, 0 0 4px #7c3aed' }}>
            {Math.round(progress)}%
          </div>
          {/* Loading Bar */}
          <div className="w-64 h-3 bg-slate-800/60 rounded-full overflow-hidden shadow-lg z-10">
            <motion.div
              className="h-full rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.2, ease: 'linear' }}
              style={{
                width: `${progress}%`,
                background: 'linear-gradient(90deg, #e5e7eb 0%, #a3a3a3 100%)',
                boxShadow: '0 0 16px #06b6d4, 0 0 8px #7c3aed',
              }}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SplashScreen; 