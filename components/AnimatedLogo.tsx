'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const AnimatedLogo = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="relative h-[70vh] w-full flex items-center justify-center bg-gradient-to-b from-black to-gray-900">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 1, ease: "easeOut" }}
        className="flex items-center justify-center"
      >
        <div className="flex items-center">
          {/* Animate each letter of "Sassify" */}
          <div className="flex">
            {"Sassify".split('').map((letter, index) => (
              <motion.span
                key={index}
                className="text-7xl font-bold"
                style={{ color: '#FF5733' }}
                whileHover={{ 
                  scale: 1.1,
                  color: '#FF3300',
                  textShadow: '0 0 8px rgba(255,87,51,0.3)'
                }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                {letter}
              </motion.span>
            ))}
          </div>

          {/* Animated X */}
          <motion.div
            className="ml-4 relative"
            animate={{ 
              rotateY: [0, 10, -10, 0],
              rotateX: [0, 5, -5, 0]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <motion.span
              className="text-8xl font-black"
              style={{ 
                color: '#FF5733',
                display: 'inline-block',
                textShadow: '0 0 20px rgba(255,87,51,0.5)',
                filter: 'drop-shadow(0 0 10px rgba(255,87,51,0.3))'
              }}
              whileHover={{ 
                scale: 1.2,
                textShadow: '0 0 30px rgba(255,87,51,0.8)',
                color: '#FF3300'
              }}
              transition={{ type: "spring", stiffness: 300, damping: 10 }}
            >
              X
            </motion.span>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default AnimatedLogo; 