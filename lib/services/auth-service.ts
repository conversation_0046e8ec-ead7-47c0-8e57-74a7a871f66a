"use server"

import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Interface for User data
export interface User {
  id: string;
  email: string;
  name?: string;
}

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create a Supabase client with cookies for server components
function createServerClient() {
  const cookieStore = cookies();
  return createClient(supabaseUrl, supabaseKey, {
    cookies: {
      get(name) {
        return cookieStore.get(name)?.value;
      },
      set(name, value, options) {
        cookieStore.set(name, value, options);
      },
      remove(name, options) {
        cookieStore.set(name, '', { ...options, maxAge: 0 });
      },
    },
  });
}

// Get the current authenticated user
export async function getCurrentUser(): Promise<User | null> {
  try {
    const supabase = createServerClient();
    
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting user session:', error);
      return null;
    }
    
    if (!data.session?.user) {
      return null;
    }
    
    return {
      id: data.session.user.id,
      email: data.session.user.email || '',
      name: data.session.user.user_metadata?.name
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
} 