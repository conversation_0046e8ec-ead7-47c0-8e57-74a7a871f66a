"use server"

import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Initialize Supabase client with service role for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || '';
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Regular client for user operations
function createServerClient() {
  const cookieStore = cookies();
  return createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '', {
    cookies: {
      get(name) {
        return cookieStore.get(name)?.value;
      },
      set(name, value, options) {
        cookieStore.set(name, value, options);
      },
      remove(name, options) {
        cookieStore.set(name, '', { ...options, maxAge: 0 });
      },
    },
  });
}

// Get current user with role information
export async function getCurrentUserWithRole(): Promise<User | null> {
  try {
    const supabase = createServerClient();

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session?.user) {
      return null;
    }

    // Get user data from our users table
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', session.user.id)
      .single();

    if (error) {
      console.error('Error fetching user role:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getCurrentUserWithRole:', error);
    return null;
  }
}

// Check if current user has admin permissions
export async function isCurrentUserAdmin(): Promise<boolean> {
  try {
    const user = await getCurrentUserWithRole();
    return user?.role === 'admin';
  } catch (error) {
    console.error('Error checking admin permissions:', error);
    return false;
  }
}

// Check if current user can manage other users
export async function canManageUsers(): Promise<boolean> {
  try {
    const user = await getCurrentUserWithRole();
    return user?.role === 'admin' || user?.role === 'moderator';
  } catch (error) {
    console.error('Error checking user management permissions:', error);
    return false;
  }
}

// User interfaces
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  status: 'active' | 'suspended' | 'pending' | 'inactive';
  role: 'user' | 'admin' | 'moderator';
  last_login?: string;
  email_verified: boolean;
  phone?: string;
  company?: string;
  job_title?: string;
  bio?: string;
  location?: string;
  website?: string;
  social_links?: Record<string, string>;
  preferences?: Record<string, any>;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  // User management fields
  created_by?: string; // ID of the admin who created this user
  created_by_email?: string; // Email of the admin who created this user
  created_by_name?: string; // Name of the admin who created this user
}

export interface UserActivity {
  id: string;
  user_id: string;
  action: string;
  resource_type?: string;
  resource_id?: string;
  details?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface UserStats {
  total_users: number;
  active_users: number;
  new_users_today: number;
  new_users_this_week: number;
  new_users_this_month: number;
  suspended_users: number;
  pending_users: number;
}

export interface UserFilters {
  search?: string;
  status?: string;
  role?: string;
  dateFrom?: string;
  dateTo?: string;
}

// Get all users with pagination and filters
export async function getUsers(
  page: number = 1,
  limit: number = 10,
  filters: UserFilters = {}
): Promise<{ users: User[]; total: number; totalPages: number }> {
  try {
    let query = supabaseAdmin
      .from('users')
      .select('*', { count: 'exact' });

    // Apply filters
    if (filters.search) {
      query = query.or(`email.ilike.%${filters.search}%,name.ilike.%${filters.search}%`);
    }

    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    if (filters.role) {
      query = query.eq('role', filters.role);
    }

    if (filters.dateFrom) {
      query = query.gte('created_at', filters.dateFrom);
    }

    if (filters.dateTo) {
      query = query.lte('created_at', filters.dateTo);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(from, to);

    if (error) {
      console.error('Error fetching users:', error);
      // If the table doesn't exist, return empty results instead of throwing
      if (error.code === '42P01') {
        console.warn('Users table does not exist. Please run the database setup.');
        return {
          users: [],
          total: 0,
          totalPages: 0,
        };
      }
      throw new Error('Failed to fetch users');
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return {
      users: data || [],
      total: count || 0,
      totalPages,
    };
  } catch (error) {
    console.error('Error in getUsers:', error);
    throw error;
  }
}

// Get user by ID
export async function getUserById(userId: string): Promise<User | null> {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getUserById:', error);
    return null;
  }
}

// Create new user
export async function createUser(userData: {
  email: string;
  password: string;
  name?: string;
  phone?: string;
  company?: string;
  job_title?: string;
  bio?: string;
  location?: string;
  website?: string;
  status?: User['status'];
  role?: User['role'];
}): Promise<User> {
  try {
    // Get the current user (admin who is creating this user)
    const currentUser = await getCurrentUserWithRole();

    // First, create the user in Supabase Auth
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        name: userData.name,
      }
    });

    if (authError) {
      console.error('Error creating user in auth:', authError);
      throw new Error(`Failed to create user account: ${authError.message}`);
    }

    if (!authData.user) {
      throw new Error('Failed to create user account');
    }

    // Then, create the user record in our users table
    const { data, error } = await supabaseAdmin
      .from('users')
      .insert({
        id: authData.user.id,
        email: userData.email,
        name: userData.name || null,
        phone: userData.phone || null,
        company: userData.company || null,
        job_title: userData.job_title || null,
        bio: userData.bio || null,
        location: userData.location || null,
        website: userData.website || null,
        status: userData.status || 'active',
        role: userData.role || 'user',
        email_verified: true,
        social_links: {},
        preferences: {},
        metadata: {},
        // Store who created this user
        created_by: currentUser?.id || null,
        created_by_email: currentUser?.email || null,
        created_by_name: currentUser?.name || null,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating user in database:', error);
      // If database insert fails, clean up the auth user
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
      throw new Error(`Failed to create user profile: ${error.message}`);
    }

    // Log the activity
    await logUserActivity(authData.user.id, 'user_created', 'user', authData.user.id, {
      created_by: currentUser?.id || 'system',
      created_by_email: currentUser?.email || 'system',
      initial_role: userData.role || 'user',
      initial_status: userData.status || 'active',
    });

    return data;
  } catch (error) {
    console.error('Error in createUser:', error);
    throw error;
  }
}

// Update user
export async function updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating user:', error);
      throw new Error('Failed to update user');
    }

    // Log the activity
    await logUserActivity(userId, 'user_updated', 'user', userId, {
      updated_fields: Object.keys(updates),
    });

    return data;
  } catch (error) {
    console.error('Error in updateUser:', error);
    throw error;
  }
}

// Delete user
export async function deleteUser(userId: string): Promise<boolean> {
  try {
    // First, delete from auth
    const { error: authError } = await supabaseAdmin.auth.admin.deleteUser(userId);
    
    if (authError) {
      console.error('Error deleting user from auth:', authError);
      throw new Error('Failed to delete user from authentication');
    }

    // The user record in the users table should be deleted automatically due to CASCADE
    // But let's make sure by checking
    const { error: dbError } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', userId);

    if (dbError) {
      console.error('Error deleting user from database:', dbError);
      // Don't throw here as the auth deletion was successful
    }

    return true;
  } catch (error) {
    console.error('Error in deleteUser:', error);
    throw error;
  }
}

// Get user statistics
export async function getUserStats(): Promise<UserStats> {
  try {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [
      totalResult,
      activeResult,
      todayResult,
      weekResult,
      monthResult,
      suspendedResult,
      pendingResult,
    ] = await Promise.all([
      supabaseAdmin.from('users').select('id', { count: 'exact', head: true }),
      supabaseAdmin.from('users').select('id', { count: 'exact', head: true }).eq('status', 'active'),
      supabaseAdmin.from('users').select('id', { count: 'exact', head: true }).gte('created_at', today.toISOString()),
      supabaseAdmin.from('users').select('id', { count: 'exact', head: true }).gte('created_at', weekAgo.toISOString()),
      supabaseAdmin.from('users').select('id', { count: 'exact', head: true }).gte('created_at', monthAgo.toISOString()),
      supabaseAdmin.from('users').select('id', { count: 'exact', head: true }).eq('status', 'suspended'),
      supabaseAdmin.from('users').select('id', { count: 'exact', head: true }).eq('status', 'pending'),
    ]);

    return {
      total_users: totalResult.count || 0,
      active_users: activeResult.count || 0,
      new_users_today: todayResult.count || 0,
      new_users_this_week: weekResult.count || 0,
      new_users_this_month: monthResult.count || 0,
      suspended_users: suspendedResult.count || 0,
      pending_users: pendingResult.count || 0,
    };
  } catch (error: any) {
    console.error('Error in getUserStats:', error);
    // If the table doesn't exist, return zero stats
    if (error?.code === '42P01' || error?.message?.includes('does not exist')) {
      console.warn('Users table does not exist. Please run the database setup.');
      return {
        total_users: 0,
        active_users: 0,
        new_users_today: 0,
        new_users_this_week: 0,
        new_users_this_month: 0,
        suspended_users: 0,
        pending_users: 0,
      };
    }
    throw error;
  }
}

// Log user activity
export async function logUserActivity(
  userId: string,
  action: string,
  resourceType?: string,
  resourceId?: string,
  details?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    await supabaseAdmin
      .from('user_activity_logs')
      .insert({
        user_id: userId,
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        details: details || {},
        ip_address: ipAddress,
        user_agent: userAgent,
      });
  } catch (error) {
    console.error('Error logging user activity:', error);
    // Don't throw here as this is a logging function
  }
}

// Get user activity logs
export async function getUserActivity(
  userId: string,
  page: number = 1,
  limit: number = 10
): Promise<{ activities: UserActivity[]; total: number; totalPages: number }> {
  try {
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await supabaseAdmin
      .from('user_activity_logs')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(from, to);

    if (error) {
      console.error('Error fetching user activity:', error);
      throw new Error('Failed to fetch user activity');
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return {
      activities: data || [],
      total: count || 0,
      totalPages,
    };
  } catch (error) {
    console.error('Error in getUserActivity:', error);
    throw error;
  }
}
