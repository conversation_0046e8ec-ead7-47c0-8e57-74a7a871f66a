"use server"

import { AIAnalysisResult } from '@/components/studio/ai-analysis/ai-analysis-service';

// This function simulates an API call but generates mock data locally
export async function analyzeProjectWithMockAPI(input: {
  name: string;
  description: string;
}): Promise<AIAnalysisResult> {
  console.log("Generating mock analysis data...");
  
  try {
    // Add a small delay to simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Generate a unique seed based on project name to ensure different projects get different analyses
    const nameSeed = input.name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    
    // Use the seed to generate "random" but consistent scores for this project
    const getScore = (base: number) => {
      const variance = ((nameSeed % 10) / 10) * 2 - 1; // Value between -1 and 1
      return Math.min(10, Math.max(4, Math.round((base + variance) * 10) / 10));
    };
    
    // Generate scores based on project name seed
    const uniquenessScore = getScore(8);
    const stickinessScore = getScore(7);
    const growthScore = getScore(9);
    const pricingScore = getScore(7.5);
    const upsellScore = getScore(6);
    const customerScore = getScore(8);
    
    // Calculate overall score
    const overallScore = parseFloat(((uniquenessScore + stickinessScore + growthScore + 
                                   pricingScore + upsellScore + customerScore) / 6).toFixed(1));
    
    // Extract keywords from description to customize features
    const keywords = input.description.toLowerCase().split(' ');
    const isAI = keywords.some(word => ['ai', 'intelligence', 'ml', 'machine'].includes(word));
    const isEcommerce = keywords.some(word => ['ecommerce', 'shop', 'store', 'product'].includes(word));
    const isProductivity = keywords.some(word => ['productivity', 'task', 'workflow', 'management'].includes(word));
    
    // Customize core features based on keywords
    let customFeature = { 
      name: `${input.name} Core Engine`, 
      description: `Main functionality specific to ${input.name}`, 
      priority: "high" as const 
    };
    
    if (isAI) {
      customFeature = { 
        name: "AI Analysis Engine", 
        description: "Advanced machine learning analysis capabilities", 
        priority: "high" as const 
      };
    } else if (isEcommerce) {
      customFeature = { 
        name: "Product Catalog", 
        description: "Comprehensive product management system", 
        priority: "high" as const 
      };
    } else if (isProductivity) {
      customFeature = { 
        name: "Task Management", 
        description: "Intuitive task creation and tracking", 
        priority: "high" as const 
      };
    }
    
    // Customize tech stack based on project type
    let customBackend = ["Node.js", "Express", "RESTful API", "Stripe"];
    if (isAI) {
      customBackend = ["Python", "TensorFlow", "Flask", "Machine Learning API"];
    } else if (isEcommerce) {
      customBackend = ["Node.js", "Express", "Stripe API", "Inventory Management"];
    }
    
    console.log("Mock analysis data generated successfully");
    
    return {
      projectName: input.name,
      projectDescription: input.description,
      marketFeasibility: {
        pillars: [
          { name: "Uniqueness", score: uniquenessScore, description: "Differentiated from existing solutions" },
          { name: "Stickiness", score: stickinessScore, description: "Good user retention potential" },
          { name: "Growth Trend", score: growthScore, description: "Strong market growth trajectory" },
          { name: "Pricing Potential", score: pricingScore, description: "Can command competitive pricing" },
          { name: "Upsell Potential", score: upsellScore, description: "Moderate additional revenue opportunities" },
          { name: "Customer Purchasing Power", score: customerScore, description: "Target audience has solid purchasing power" }
        ],
        overallScore: overallScore
      },
      suggestedImprovements: [
        `Enhance ${input.name}'s user onboarding process to improve initial engagement`,
        `Add advanced analytics to provide deeper insights for ${input.name} users`,
        `Implement social features to increase ${input.name}'s organic growth potential`,
        `Consider developing a mobile app version of ${input.name} for broader reach`
      ],
      coreFeatures: [
        customFeature,
        { name: "User Authentication", description: "Secure login and registration system", priority: "high" },
        { name: `${input.name} Dashboard`, description: "Central hub for managing activities", priority: "high" },
        { name: "Collaboration Tools", description: "Team collaboration and sharing features", priority: "medium" },
        { name: "Export & Integration", description: "Export data and integrate with other tools", priority: "medium" }
      ],
      technicalRequirements: {
        frontend: ["Next.js", "React", "TypeScript", "Tailwind CSS"],
        backend: customBackend,
        database: ["PostgreSQL", "Redis", "Supabase"],
        infrastructure: ["Vercel", "AWS", "Docker", "GitHub Actions"]
      },
      pricingModel: [
        { name: "Free", price: 0, features: [`Limited ${input.name} access`, "Basic features", "Community support"] },
        {
          name: "Pro",
          price: Math.round(19 + (nameSeed % 3) * 10), // Generates 19, 29, or 39
          features: ["Unlimited access", "Advanced features", "Priority support", "Export capabilities"],
          recommended: true
        },
        {
          name: "Enterprise",
          price: Math.round(99 + (nameSeed % 5) * 40), // Generates 99, 139, 179, 219, or 259
          features: ["Everything in Pro", "Team collaboration", "Custom integrations", "Dedicated support"]
        }
      ]
    };
  } catch (error) {
    console.error("Error in mock API service:", error);
    throw new Error("Failed to generate mock analysis");
  }
} 