"use server"

import { createClient } from '@supabase/supabase-js';
import { AIAnalysisResult } from '@/components/studio/ai-analysis/ai-analysis-service';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

// Save a project to the database
export async function saveProject({
  name,
  description,
  analysis,
  userId
}: {
  name: string;
  description: string;
  analysis: AIAnalysisResult;
  userId: string;
}) {
  try {
    console.log(`Saving project "${name}" for user ${userId}`);
    
    // Insert the project into the database
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        name,
        description,
        user_id: userId,
        analysis_json: analysis
      })
      .select('id')
      .single();
    
    if (projectError) {
      console.error('Error saving project:', projectError);
      throw new Error(`Failed to save project: ${projectError.message}`);
    }
    
    console.log(`Project saved successfully with ID: ${project.id}`);
    return project;
  } catch (error) {
    console.error('Error in saveProject:', error);
    throw error;
  }
}

// Get a project by ID
export async function getProject(id: string) {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error getting project:', error);
    throw error;
  }
}

// Update a project
export async function updateProject(id: string, updates: {
  name?: string;
  description?: string;
  analysis_json?: any;
}) {
  try {
    console.log(`Updating project ${id} with:`, updates);

    const { data, error } = await supabase
      .from('projects')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating project:', error);
      throw new Error(`Failed to update project: ${error.message}`);
    }

    console.log(`Project updated successfully:`, data);
    return data;
  } catch (error) {
    console.error('Error in updateProject:', error);
    throw error;
  }
}

// Get all projects for a user
export async function getUserProjects(userId: string) {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) {
      throw error;
    }
    
    return data || [];
  } catch (error) {
    console.error('Error getting user projects:', error);
    throw error;
  }
}

// Delete a project
export async function deleteProject(id: string) {
  try {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id);
    
    if (error) {
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error deleting project:', error);
    throw error;
  }
}

// Save user flow data
export async function saveUserFlow(projectName: string, flowData: any[]) {
  try {
    console.log(`Saving user flow for project "${projectName}"`);
    
    // Insert or update the user flow in the database
    const { data, error } = await supabase
      .from('user_flows')
      .upsert({
        project_name: projectName,
        flow_data: flowData,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'project_name'
      })
      .select('id')
      .single();
    
    if (error) {
      console.error('Error saving user flow:', error);
      throw new Error(`Failed to save user flow: ${error.message}`);
    }
    
    console.log(`User flow saved successfully for project: ${projectName}`);
    return data;
  } catch (error) {
    console.error('Error in saveUserFlow:', error);
    throw error;
  }
}

// Get user flow data
export async function getUserFlow(projectName: string) {
  try {
    const { data, error } = await supabase
      .from('user_flows')
      .select('flow_data')
      .eq('project_name', projectName)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        // No data found, return null
        return null;
      }
      throw error;
    }
    
    return data?.flow_data || null;
  } catch (error) {
    console.error('Error getting user flow:', error);
    return null; // Return null instead of throwing to allow fallback
  }
}

// Cleanup function to remove all projects and user flows
export async function cleanupAllData() {
  try {
    console.log('🧹 Starting database cleanup...');
    
    // Delete all user flows
    console.log('Deleting all user flows...');
    const { error: userFlowsError } = await supabase
      .from('user_flows')
      .delete()
      .neq('id', 0); // Delete all rows
    
    if (userFlowsError) {
      console.error('Error deleting user flows:', userFlowsError);
      throw userFlowsError;
    } else {
      console.log('✅ All user flows deleted successfully');
    }
    
    // Delete all projects
    console.log('Deleting all projects...');
    const { error: projectsError } = await supabase
      .from('projects')
      .delete()
      .neq('id', 0); // Delete all rows
    
    if (projectsError) {
      console.error('Error deleting projects:', projectsError);
      throw projectsError;
    } else {
      console.log('✅ All projects deleted successfully');
    }
    
    console.log('🎉 Database cleanup completed!');
    return { success: true, message: 'Database cleaned successfully' };
    
  } catch (error) {
    console.error('Error during cleanup:', error);
    return { success: false, error: error.message };
  }
}

// Save project tasks and explanation
export async function saveProjectTasks(projectName: string, tasks: any[], explanation: string) {
  try {
    console.log(`Saving tasks and explanation for project "${projectName}"`);

    // Update the project with tasks and explanation
    const { data, error } = await supabase
      .from('projects')
      .update({
        generated_tasks: tasks,
        tasks_explanation: explanation,
        tasks_generated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('project_name', projectName)
      .select('id')
      .single();

    if (error) {
      console.error('Error saving project tasks:', error);
      throw new Error(`Failed to save project tasks: ${error.message}`);
    }

    console.log(`Tasks and explanation saved successfully for project: ${projectName}`);
    return data;
  } catch (error) {
    console.error('Error in saveProjectTasks:', error);
    throw error;
  }
}

// Get project tasks and explanation
export async function getProjectTasks(projectName: string) {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('generated_tasks, tasks_explanation, tasks_generated_at')
      .eq('project_name', projectName)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      throw error;
    }

    return {
      tasks: data?.generated_tasks || null,
      explanation: data?.tasks_explanation || null,
      generatedAt: data?.tasks_generated_at || null
    };
  } catch (error) {
    console.error('Error getting project tasks:', error);
    return {
      tasks: null,
      explanation: null,
      generatedAt: null
    };
  }
}

// Task management functions
export interface Task {
  id: string;
  project_id: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'done';
  priority: 'low' | 'medium' | 'high';
  category?: string;
  estimated_hours?: number;
  dependencies?: string[];
  created_at: string;
  updated_at: string;
}

// Get tasks for a project
export async function getProjectTasksFromDB(projectId: string): Promise<Task[]> {
  try {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error getting project tasks from DB:', error);
    throw error;
  }
}

// Save tasks to database
export async function saveTasksToDB(projectId: string, tasks: Omit<Task, 'id' | 'project_id' | 'created_at' | 'updated_at'>[]): Promise<Task[]> {
  try {
    const { data, error } = await supabase
      .from('tasks')
      .insert(
        tasks.map(task => ({
          project_id: projectId,
          ...task
        }))
      )
      .select();

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error saving tasks to DB:', error);
    throw error;
  }
}

// Update task status
export async function updateTaskStatus(taskId: string, status: 'todo' | 'in_progress' | 'done'): Promise<Task> {
  try {
    const { data, error } = await supabase
      .from('tasks')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error updating task status:', error);
    throw error;
  }
}

// Delete task
export async function deleteTask(taskId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error deleting task:', error);
    throw error;
  }
}

// Delete all tasks for a project
export async function deleteAllProjectTasks(projectId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('project_id', projectId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error deleting project tasks:', error);
    throw error;
  }
}

// User Flow management functions
export async function saveUserFlowToDB(projectId: string, flowData: any): Promise<void> {
  try {
    const { error } = await supabase
      .from('user_flows')
      .upsert({
        project_id: projectId,
        flow_data: flowData,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'project_id'
      });

    if (error) {
      throw error;
    }

    // Also update the project table
    await supabase
      .from('projects')
      .update({
        user_flow_data: flowData,
        user_flow_generated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId);

  } catch (error) {
    console.error('Error saving user flow to DB:', error);
    throw error;
  }
}

export async function getUserFlowFromDB(projectId: string): Promise<any> {
  try {
    const { data, error } = await supabase
      .from('user_flows')
      .select('flow_data')
      .eq('project_id', projectId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    return data?.flow_data || null;
  } catch (error) {
    console.error('Error getting user flow from DB:', error);
    return null;
  }
}

// Memory Bank management functions
export async function saveMemoryBankData(projectId: string, memoryData: any): Promise<void> {
  try {
    const { error } = await supabase
      .from('projects')
      .update({
        memory_bank_data: memoryData,
        memory_bank_generated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error saving memory bank data:', error);
    throw error;
  }
}

export async function getMemoryBankData(projectId: string): Promise<any> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('memory_bank_data, memory_bank_generated_at')
      .eq('id', projectId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    return {
      data: data?.memory_bank_data || null,
      generatedAt: data?.memory_bank_generated_at || null
    };
  } catch (error) {
    console.error('Error getting memory bank data:', error);
    return { data: null, generatedAt: null };
  }
}

// Overview management functions
export async function saveOverviewData(projectId: string, overviewData: any): Promise<void> {
  try {
    const { error } = await supabase
      .from('projects')
      .update({
        overview_data: overviewData,
        overview_generated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error saving overview data:', error);
    throw error;
  }
}

export async function getOverviewData(projectId: string): Promise<any> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('overview_data, overview_generated_at')
      .eq('id', projectId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    return {
      data: data?.overview_data || null,
      generatedAt: data?.overview_generated_at || null
    };
  } catch (error) {
    console.error('Error getting overview data:', error);
    return { data: null, generatedAt: null };
  }
}

