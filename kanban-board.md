# SaaS Ideas - Kanban Ticket Board

## Overview

The Kanban Ticket Board is a crucial component of the SaaS Ideas application, providing users with a visual project management tool to track the development progress of their application features. This board helps users organize tasks, monitor progress, and prioritize work effectively.

## Features

- Drag-and-drop interface for managing tickets
- Multiple columns representing different stages of development
- Ticket creation and editing capabilities
- Priority and complexity indicators
- Integration with user flow diagrams
- AI-assisted ticket generation based on project analysis
- Filtering and sorting options
- Progress tracking and metrics

## Implementation

The Kanban Ticket Board is implemented using a combination of React components for the frontend and Supabase for data persistence. It leverages the DND Kit library for drag-and-drop functionality.

### Core Components

#### Kanban Board Component

```typescript
// src/components/tickets/KanbanBoard.tsx

'use client';

import { useEffect, useState } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { SortableContext, arrayMove } from '@dnd-kit/sortable';
import { Feature } from '@/types/feature';
import { supabase } from '@/lib/supabase/client';
import KanbanColumn from './KanbanColumn';
import TicketCard from './TicketCard';
import CreateTicketForm from './CreateTicketForm';
import { toast } from 'react-hot-toast';

interface KanbanBoardProps {
  projectId: string;
}

type StatusColumn = 'backlog' | 'todo' | 'in_progress' | 'review' | 'done';

const COLUMN_TITLES: Record<StatusColumn, string> = {
  backlog: 'Backlog',
  todo: 'To Do',
  in_progress: 'In Progress',
  review: 'Review',
  done: 'Done',
};

export default function KanbanBoard({ projectId }: KanbanBoardProps) {
  const [features, setFeatures] = useState<Feature[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTicket, setActiveTicket] = useState<Feature | null>(null);
  const [isCreatingTicket, setIsCreatingTicket] = useState(false);

  // Configure DND sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  // Fetch features on component mount
  useEffect(() => {
    fetchFeatures();
  }, [projectId]);

  // Fetch features from the database
  async function fetchFeatures() {
    try {
      setIsLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('features')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setFeatures(data as Feature[]);
    } catch (err) {
      console.error('Error fetching features:', err);
      setError('Failed to load tickets');
      toast.error('Failed to load tickets');
    } finally {
      setIsLoading(false);
    }
  }

  // Get features for a specific column
  const getColumnFeatures = (status: StatusColumn) => {
    return features.filter((feature) => feature.status === status);
  };

  // Handle drag start event
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const activeFeature = features.find((feature) => feature.id === active.id);
    
    if (activeFeature) {
      setActiveTicket(activeFeature);
    }
  };

  // Handle drag over event (for column changes)
  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    
    if (!over) return;
    
    const activeId = active.id;
    const overId = over.id;
    
    // Find the active feature
    const activeFeature = features.find((feature) => feature.id === activeId);
    
    // If dragging over a column
    if (overId.toString().startsWith('column-')) {
      const newStatus = overId.toString().replace('column-', '') as StatusColumn;
      
      // Update the feature's status in the UI
      if (activeFeature && activeFeature.status !== newStatus) {
        setFeatures((prevFeatures) =>
          prevFeatures.map((feature) =>
            feature.id === activeId
              ? { ...feature, status: newStatus }
              : feature
          )
        );
      }
    }
  };

  // Handle drag end event
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (!over) return;
    
    const activeId = active.id;
    const overId = over.id;
    
    // Find the active feature
    const activeFeature = features.find((feature) => feature.id === activeId);
    
    // If dragging over a column, update the status in the database
    if (overId.toString().startsWith('column-')) {
      const newStatus = overId.toString().replace('column-', '') as StatusColumn;
      
      if (activeFeature && activeFeature.status !== newStatus) {
        try {
          const { error } = await supabase
            .from('features')
            .update({ status: newStatus })
            .eq('id', activeId);
          
          if (error) {
            throw error;
          }
          
          toast.success(`Moved "${activeFeature.name}" to ${COLUMN_TITLES[newStatus]}`);
        } catch (err) {
          console.error('Error updating feature status:', err);
          toast.error('Failed to update ticket status');
          
          // Revert the UI change
          fetchFeatures();
        }
      }
    }
    
    setActiveTicket(null);
  };

  // Handle creating a new ticket
  const handleCreateTicket = async (newTicket: Omit<Feature, 'id' | 'project_id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const { data, error } = await supabase
        .from('features')
        .insert({
          project_id: projectId,
          ...newTicket,
        })
        .select()
        .single();
      
      if (error) {
        throw error;
      }
      
      setFeatures((prevFeatures) => [data as Feature, ...prevFeatures]);
      setIsCreatingTicket(false);
      toast.success('Ticket created successfully');
    } catch (err) {
      console.error('Error creating ticket:', err);
      toast.error('Failed to create ticket');
    }
  };

  // Handle deleting a ticket
  const handleDeleteTicket = async (ticketId: string) => {
    try {
      const { error } = await supabase
        .from('features')
        .delete()
        .eq('id', ticketId);
      
      if (error) {
        throw error;
      }
      
      setFeatures((prevFeatures) => prevFeatures.filter((feature) => feature.id !== ticketId));
      toast.success('Ticket deleted successfully');
    } catch (err) {
      console.error('Error deleting ticket:', err);
      toast.error('Failed to delete ticket');
    }
  };

  // Handle updating a ticket
  const handleUpdateTicket = async (updatedTicket: Feature) => {
    try {
      const { error } = await supabase
        .from('features')
        .update({
          name: updatedTicket.name,
          description: updatedTicket.description,
          priority: updatedTicket.priority,
          assignee: updatedTicket.assignee,
          due_date: updatedTicket.dueDate,
          tags: updatedTicket.tags,
        })
        .eq('id', updatedTicket.id);
      
      if (error) {
        throw error;
      }
      
      setFeatures((prevFeatures) =>
        prevFeatures.map((feature) =>
          feature.id === updatedTicket.id ? updatedTicket : feature
        )
      );
      toast.success('Ticket updated successfully');
    } catch (err) {
      console.error('Error updating ticket:', err);
      toast.error('Failed to update ticket');
    }
  };

  if (isLoading) {
    return <div className="p-4">Loading tickets...</div>;
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchFeatures}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Kanban Board</h2>
        <button
          onClick={() => setIsCreatingTicket(true)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center gap-2"
        >
          <span>+</span> New Ticket
        </button>
      </div>

      {isCreatingTicket && (
        <div className="mb-6">
          <CreateTicketForm
            onSubmit={handleCreateTicket}
            onCancel={() => setIsCreatingTicket(false)}
          />
        </div>
      )}

      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {(Object.keys(COLUMN_TITLES) as StatusColumn[]).map((status) => (
            <KanbanColumn
              key={status}
              id={`column-${status}`}
              title={COLUMN_TITLES[status]}
              tickets={getColumnFeatures(status)}
              onDeleteTicket={handleDeleteTicket}
              onUpdateTicket={handleUpdateTicket}
            />
          ))}
        </div>

        <DragOverlay>
          {activeTicket && <TicketCard ticket={activeTicket} />}
        </DragOverlay>
      </DndContext>
    </div>
  );
}

#### Kanban Column Component

```typescript
// src/components/tickets/KanbanColumn.tsx

import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Feature } from '@/types/feature';
import SortableTicket from './SortableTicket';

interface KanbanColumnProps {
  id: string;
  title: string;
  tickets: Feature[];
  onDeleteTicket: (id: string) => void;
  onUpdateTicket: (ticket: Feature) => void;
}

export default function KanbanColumn({
  id,
  title,
  tickets,
  onDeleteTicket,
  onUpdateTicket,
}: KanbanColumnProps) {
  const { setNodeRef } = useDroppable({
    id,
  });

  return (
    <div
      ref={setNodeRef}
      className="bg-gray-50 rounded-lg p-3 shadow-sm border border-gray-200 flex flex-col h-[70vh]"
    >
      <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-200">
        <h3 className="font-medium text-sm">{title}</h3>
        <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full">
          {tickets.length}
        </span>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        <SortableContext items={tickets.map((t) => t.id)} strategy={verticalListSortingStrategy}>
          {tickets.length === 0 ? (
            <div className="text-center py-4 text-sm text-gray-500 italic">
              No tickets
            </div>
          ) : (
            <div className="space-y-3">
              {tickets.map((ticket) => (
                <SortableTicket
                  key={ticket.id}
                  ticket={ticket}
                  onDelete={() => onDeleteTicket(ticket.id)}
                  onUpdate={onUpdateTicket}
                />
              ))}
            </div>
          )}
        </SortableContext>
      </div>
    </div>
  );
}

#### Sortable Ticket Component

```typescript
// src/components/tickets/SortableTicket.tsx

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Feature } from '@/types/feature';
import TicketCard from './TicketCard';

interface SortableTicketProps {
  ticket: Feature;
  onDelete: () => void;
  onUpdate: (ticket: Feature) => void;
}

export default function SortableTicket({
  ticket,
  onDelete,
  onUpdate,
}: SortableTicketProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: ticket.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    >
      <TicketCard
        ticket={ticket}
        onDelete={onDelete}
        onUpdate={onUpdate}
      />
    </div>
  );
}

#### Ticket Card Component

```typescript
// src/components/tickets/TicketCard.tsx

import { useState } from 'react';
import { Feature } from '@/types/feature';
import EditTicketForm from './EditTicketForm';

interface TicketCardProps {
  ticket: Feature;
  onDelete?: () => void;
  onUpdate?: (ticket: Feature) => void;
}

export default function TicketCard({
  ticket,
  onDelete,
  onUpdate,
}: TicketCardProps) {
  const [isEditing, setIsEditing] = useState(false);

  // Get priority badge color
  const getPriorityColor = () => {
    switch (ticket.priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle edit submission
  const handleEditSubmit = (updatedTicket: Feature) => {
    if (onUpdate) {
      onUpdate(updatedTicket);
    }
    setIsEditing(false);
  };

  if (isEditing && onUpdate) {
    return (
      <EditTicketForm
        ticket={ticket}
        onSubmit={handleEditSubmit}
        onCancel={() => setIsEditing(false)}
      />
    );
  }

  return (
    <div className="bg-white p-3 rounded-md shadow border border-gray-200 select-none">
      <div className="flex justify-between items-start mb-2">
        <h4 className="font-medium text-sm">{ticket.name}</h4>
        {(onDelete || onUpdate) && (
          <div className="flex space-x-1">
            {onUpdate && (
              <button
                onClick={() => setIsEditing(true)}
                className="text-gray-400 hover:text-blue-500"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
              </button>
            )}
            {onDelete && (
              <button
                onClick={onDelete}
                className="text-gray-400 hover:text-red-500"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            )}
          </div>
        )}
      </div>
      
      {ticket.description && (
        <p className="text-xs text-gray-600 mb-2 line-clamp-2">{ticket.description}</p>
      )}
      
      <div className="flex flex-wrap gap-1 mb-2">
        <span className={`text-xs px-2 py-0.5 rounded-full ${getPriorityColor()}`}>
          {ticket.priority}
        </span>
        
        {ticket.tags && ticket.tags.length > 0 && ticket.tags.map((tag) => (
          <span
            key={tag}
            className="text-xs px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full"
          >
            {tag}
          </span>
        ))}
      </div>
      
      <div className="flex justify-between items-center text-xs text-gray-500">
        {ticket.assignee ? (
          <div className="flex items-center">
            <span className="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 mr-1">
              {ticket.assignee.charAt(0).toUpperCase()}
            </span>
            <span>{ticket.assignee}</span>
          </div>
        ) : (
          <span>Unassigned</span>
        )}
        
        {ticket.dueDate && (
          <span>
            Due: {new Date(ticket.dueDate).toLocaleDateString()}
          </span>
        )}
      </div>
    </div>
  );
}

#### Create Ticket Form Component

```typescript
// src/components/tickets/CreateTicketForm.tsx

import { useState } from 'react';
import { Feature } from '@/types/feature';

interface CreateTicketFormProps {
  onSubmit: (ticket: Omit<Feature, 'id' | 'project_id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
}

export default function CreateTicketForm({ onSubmit, onCancel }: CreateTicketFormProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [status, setStatus] = useState<'backlog' | 'todo' | 'in_progress' | 'review' | 'done'>('backlog');
  const [assignee, setAssignee] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [tags, setTags] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const tagArray = tags
      .split(',')
      .map((tag) => tag.trim())
      .filter((tag) => tag !== '');
    
    onSubmit({
      name,
      description,
      priority,
      status,
      assignee: assignee || null,
      dueDate: dueDate || null,
      tags: tagArray.length > 0 ? tagArray : [],
    });
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
      <h3 className="text-lg font-medium mb-4">Create New Ticket</h3>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              required
            />
          </div>
          
          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              rows={3}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Priority
            </label>
            <select
              value={priority}
              onChange={(e) => setPriority(e.target.value as 'low' | 'medium' | 'high')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={status}
              onChange={(e) => 
                setStatus(e.target.value as 'backlog' | 'todo' | 'in_progress' | 'review' | 'done')
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="backlog">Backlog</option>
              <option value="todo">To Do</option>
              <option value="in_progress">In Progress</option>
              <option value="review">Review</option>
              <option value="done">Done</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Assignee
            </label>
            <input
              type="text"
              value={assignee}
              onChange={(e) => setAssignee(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Optional"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Due Date
            </label>
            <input
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tags
            </label>
            <input
              type="text"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Comma-separated tags"
            />
          </div>
        </div>
        
        <div className="flex justify-end mt-4 space-x-2">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!name.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
          >
            Create Ticket
          </button>
        </div>
      </form>
    </div>
  );
}

#### Edit Ticket Form Component

```typescript
// src/components/tickets/EditTicketForm.tsx

import { useState } from 'react';
import { Feature } from '@/types/feature';

interface EditTicketFormProps {
  ticket: Feature;
  onSubmit: (ticket: Feature) => void;
  onCancel: () => void;
}

export default function EditTicketForm({ ticket, onSubmit, onCancel }: EditTicketFormProps) {
  const [name, setName] = useState(ticket.name);
  const [description, setDescription] = useState(ticket.description || '');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>(ticket.priority);
  const [assignee, setAssignee] = useState(ticket.assignee || '');
  const [dueDate, setDueDate] = useState(
    ticket.dueDate ? new Date(ticket.dueDate).toISOString().split('T')[0] : ''
  );
  const [tags, setTags] = useState((ticket.tags || []).join(', '));

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const tagArray = tags
      .split(',')
      .map((tag) => tag.trim())
      .filter((tag) => tag !== '');
    
    onSubmit({
      ...ticket,
      name,
      description,
      priority,
      assignee: assignee || null,
      dueDate: dueDate || null,
      tags: tagArray.length > 0 ? tagArray : [],
    });
  };

  return (
    <div className="bg-white p-3 rounded-md shadow border border-gray-200">
      <form onSubmit={handleSubmit}>
        <div className="space-y-3">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md"
              required
            />
          </div>
          
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md"
              rows={2}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Priority
              </label>
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value as 'low' | 'medium' | 'high')}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Assignee
              </label>
              <input
                type="text"
                value={assignee}
                onChange={(e) => setAssignee(e.target.value)}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md"
                placeholder="Optional"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Due Date
              </label>
              <input
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Tags
              </label>
              <input
                type="text"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md"
                placeholder="Comma-separated"
              />
            </div>
          </div>
        </div>
        
        <div className="flex justify-end mt-3 space-x-2">
          <button
            type="button"
            onClick={onCancel}
            className="px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!name.trim()}
            className="px-3 py-1 text-xs bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
          >
            Save
          </button>
        </div>
      </form>
    </div>
  );
}

### AI Ticket Generation

The Kanban Ticket Board includes AI-assisted ticket generation based on the project's analysis and user flow diagram. This helps users quickly create a set of development tickets aligned with their project requirements.

#### Ticket Generator Service

```typescript
// src/lib/ai/ticketGenerator.ts

import { AnalysisData, CoreFeature } from '@/types/analysis';
import { FlowData, FlowNode } from '@/types/userFlow';
import { Feature } from '@/types/feature';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function generateTickets(
  projectName: string,
  projectDescription: string,
  analysisData: AnalysisData,
  userFlow?: FlowData
): Promise<Omit<Feature, 'id' | 'project_id' | 'createdAt' | 'updatedAt'>[]> {
  try {
    const prompt = generateTicketsPrompt(projectName, projectDescription, analysisData, userFlow);
    
    const response = await openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [
        {
          role: 'system',
          content: `You are an expert software project manager and developer. 
          Generate development tickets for a SaaS application based on the provided details.
          Each ticket should represent a specific task that can be implemented by a developer.
          Respond with a structured JSON array following the specified format.`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      response_format: { type: 'json_object' }
    });

    const result = JSON.parse(response.choices[0].message.content || '{}');
    
    // Process and validate the tickets
    return processTickets(result.tickets || []);
  } catch (error) {
    console.error('Error generating tickets:', error);
    throw new Error('Failed to generate tickets');
  }
}

function generateTicketsPrompt(
  projectName: string,
  projectDescription: string,
  analysisData: AnalysisData,
  userFlow?: FlowData
): string {
  const coreFeatures = analysisData.coreFeatures
    .map(feature => `- ${feature.name}: ${feature.description}`)
    .join('\n');
  
  const userFlowNodes = userFlow?.nodes
    ? userFlow.nodes.map(node => `- ${node.data.label} (${node.type}): ${node.data.description || 'No description'}`)
    .join('\n')
    : 'No user flow data available';
  
  return `
    Please generate development tickets for the following SaaS application:
    
    Name: ${projectName}
    Description: ${projectDescription}
    
    Core Features:
    ${coreFeatures}
    
    User Flow Pages/Components:
    ${userFlowNodes}
    
    For each ticket, provide:
    - A clear, specific name
    - A detailed description of what needs to be implemented
    - Priority level (low, medium, high)
    - Initial status (should be "backlog" for most tickets)
    - Relevant tags
    
    Generate tickets that cover:
    1. Core functionality implementation
    2. UI/UX components
    3. API endpoints and data handling
    4. Authentication and authorization
    5. Testing and quality assurance
    
    Return the tickets in the following JSON format:
    {
      "tickets": [
        {
          "name": "Ticket name",
          "description": "Detailed description",
          "priority": "low|medium|high",
          "status": "backlog|todo|in_progress|review|done",
          "tags": ["tag1", "tag2"]
        },
        ...
      ]
    }
    
    Generate 10-15 well-defined tickets that would help developers start implementing this project.
  `;
}

function processTickets(tickets: any[]): Omit<Feature, 'id' | 'project_id' | 'createdAt' | 'updatedAt'>[] {
  return tickets.map(ticket => ({
    name: ticket.name || 'Untitled Ticket',
    description: ticket.description || '',
    priority: ['low', 'medium', 'high'].includes(ticket.priority) ? ticket.priority : 'medium',
    status: ['backlog', 'todo', 'in_progress', 'review', 'done'].includes(ticket.status) ? ticket.status : 'backlog',
    assignee: null,
    dueDate: null,
    tags: Array.isArray(ticket.tags) ? ticket.tags : [],
  }));
}

#### Ticket Generator Component

```typescript
// src/components/tickets/TicketGenerator.tsx

'use client';

import { useState } from 'react';
import { generateTickets } from '@/lib/ai/ticketGenerator';
import { AnalysisData } from '@/types/analysis';
import { FlowData } from '@/types/userFlow';
import { Feature } from '@/types/feature';
import { toast } from 'react-hot-toast';

interface TicketGeneratorProps {
  projectId: string;
  projectName: string;
  projectDescription: string;
  analysisData: AnalysisData;
  userFlow?: FlowData;
  onTicketsGenerated: (tickets: Omit<Feature, 'id' | 'project_id' | 'createdAt' | 'updatedAt'>[]) => void;
}

export default function TicketGenerator({
  projectId,
  projectName,
  projectDescription,
  analysisData,
  userFlow,
  onTicketsGenerated,
}: TicketGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateTickets = async () => {
    try {
      setIsGenerating(true);
      
      const tickets = await generateTickets(
        projectName,
        projectDescription,
        analysisData,
        userFlow
      );
      
      onTicketsGenerated(tickets);
      toast.success(`Generated ${tickets.length} tickets successfully`);
    } catch (error) {
      console.error('Error generating tickets:', error);
      toast.error('Failed to generate tickets');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="mb-6">
      <button
        onClick={handleGenerateTickets}
        disabled={isGenerating}
        className={`px-4 py-2 rounded text-sm font-medium flex items-center gap-2 ${
          isGenerating
            ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
            : 'bg-purple-600 text-white hover:bg-purple-700'
        }`}
      >
        {isGenerating ? (
          <>
            <svg
              className="animate-spin h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            Generating Tickets...
          </>
        ) : (
          <>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            Generate AI Tickets
          </>
        )}
      </button>
      <p className="mt-2 text-xs text-gray-500">
        Let AI create development tickets based on your project analysis and user flow.
      </p>
    </div>
  );
}

#### Integration in the Tickets Page

```typescript
// src/app/studio/[projectId]/tickets/page.tsx

'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Feature } from '@/types/feature';
import { AnalysisData } from '@/types/analysis';
import { FlowData } from '@/types/userFlow';
import { supabase } from '@/lib/supabase/client';
import KanbanBoard from '@/components/tickets/KanbanBoard';
import TicketGenerator from '@/components/tickets/TicketGenerator';
import { toast } from 'react-hot-toast';

export default function TicketsPage() {
  const params = useParams();
  const projectId = params.projectId as string;
  
  const [projectData, setProjectData] = useState<{
    name: string;
    description: string;
    analysisData: AnalysisData | null;
  } | null>(null);
  const [userFlow, setUserFlow] = useState<FlowData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true);
        setError(null);
        
        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('name, description, analysis_data')
          .eq('id', projectId)
          .single();
        
        if (projectError) {
          throw new Error('Failed to fetch project data');
        }
        
        // Fetch user flow data
        const { data: flowData, error: flowError } = await supabase
          .from('user_flows')
          .select('flow_data')
          .eq('project_id', projectId)
          .maybeSingle();
        
        setProjectData({
          name: projectData.name,
          description: projectData.description,
          analysisData: projectData.analysis_data,
        });
        
        if (flowData) {
          setUserFlow(flowData.flow_data);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load project data');
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchData();
  }, [projectId]);

  const handleTicketsGenerated = async (tickets: Omit<Feature, 'id' | 'project_id' | 'createdAt' | 'updatedAt'>[]) => {
    try {
      // Insert all tickets at once
      const { data, error } = await supabase
        .from('features')
        .insert(
          tickets.map(ticket => ({
            project_id: projectId,
            ...ticket,
          }))
        );
      
      if (error) {
        throw error;
      }
      
      // Refresh the board to show new tickets
      window.location.reload();
    } catch (err) {
      console.error('Error saving generated tickets:', err);
      toast.error('Failed to save generated tickets');
    }
  };

  if (isLoading) {
    return <div className="p-6">Loading project data...</div>;
  }

  if (error || !projectData) {
    return (
      <div className="p-6">
        <div className="text-red-600">{error || 'Failed to load project data'}</div>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Kanban Ticket Board</h1>
        <p className="text-gray-600">
          Manage and track the development of your application features.
        </p>
      </div>
      
      {projectData.analysisData && (
        <TicketGenerator
          projectId={projectId}
          projectName={projectData.name}
          projectDescription={projectData.description}
          analysisData={projectData.analysisData}
          userFlow={userFlow || undefined}
          onTicketsGenerated={handleTicketsGenerated}
        />
      )}
      
      <KanbanBoard projectId={projectId} />
    </div>
  );
}

## Best Practices for Kanban Board Usage

When using the Kanban Ticket Board for your SaaS application development, consider these best practices:

1. **Start with AI-generated tickets**: Use the AI ticket generator to create an initial set of development tasks based on your project analysis and user flow.

2. **Refine tickets**: Review and refine the AI-generated tickets to ensure they accurately represent the work that needs to be done.

3. **Break down large tasks**: Split complex features into smaller, manageable tickets that can be completed in a reasonable timeframe.

4. **Use consistent naming**: Adopt a consistent naming convention for tickets to make them easy to understand and track.

5. **Add detailed descriptions**: Include clear descriptions that specify what needs to be done and any acceptance criteria.

6. **Set appropriate priorities**: Assign priorities to help the team focus on the most important work first.

7. **Limit work-in-progress**: Keep the number of tickets in the "In Progress" column limited to avoid context switching and improve flow.

8. **Update regularly**: Move tickets across the board as work progresses to maintain an accurate view of project status.

9. **Link to user flow nodes**: Connect tickets to specific nodes in your user flow diagram to maintain traceability.

10. **Track progress metrics**: Use the board to monitor completion rates and identify bottlenecks in your development process.

## Integration with Cursor AI

The Kanban Ticket Board integrates with Cursor AI to enable automated code generation and ticket updates:

1. **Code generation from tickets**: Developers can request Cursor AI to generate code for specific tickets directly from the board.

2. **Automated ticket updates**: When code is committed or merged, Cursor AI can automatically update the status of related tickets.

3. **Context sharing**: The ticket details and requirements are shared with Cursor AI to provide context for code generation.

4. **Progress tracking**: Cursor AI can report on implementation progress and suggest updates to ticket statuses.

This integration creates a seamless workflow between planning and implementation, helping teams move faster and maintain alignment throughout the development process. 