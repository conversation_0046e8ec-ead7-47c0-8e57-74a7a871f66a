# SaaS Ideas - User Flow Diagram Editor

## Overview

The User Flow Diagram Editor is a key feature of the SaaS Ideas application, allowing users to create visual representations of their application's user journeys. This interactive tool helps developers plan and visualize how users will navigate through their application, connecting different pages and features in a logical flow.

## Features

- Interactive node-based diagram editor
- Drag-and-drop interface for creating and connecting pages
- AI-assisted flow suggestions based on project analysis
- Connection to feature tickets for implementation tracking
- Export options for sharing and documentation
- Version history for tracking changes

## Implementation

The User Flow Diagram Editor is implemented using React Flow, a powerful library for building node-based editors and interactive diagrams. This is integrated with the Supabase backend for data persistence and real-time collaboration.

### Core Components

#### Flow Editor Component

```typescript
// src/components/userflow/FlowEditor.tsx

'use client';

import { useCallback, useEffect, useState } from 'react';
import ReactFlow, {
  addEdge,
  Background,
  Connection,
  Controls,
  Edge,
  EdgeChange,
  MiniMap,
  Node,
  NodeChange,
  Panel,
  useEdgesState,
  useNodesState,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { v4 as uuidv4 } from 'uuid';
import { FlowData, FlowNode as FlowNodeType, FlowEdge } from '@/types/userFlow';
import { supabase } from '@/lib/supabase/client';
import FlowNode from './FlowNode';
import AddNodePanel from './AddNodePanel';
import SaveButton from './SaveButton';
import { toast } from 'react-hot-toast';

// Define node types
const nodeTypes = {
  customNode: FlowNode,
};

interface FlowEditorProps {
  projectId: string;
  initialData?: FlowData;
  readOnly?: boolean;
}

export default function FlowEditor({ projectId, initialData, readOnly = false }: FlowEditorProps) {
  // Initialize nodes and edges from initial data or empty arrays
  const [nodes, setNodes, onNodesChange] = useNodesState(
    initialData?.nodes.map(convertToReactFlowNode) || []
  );
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialData?.edges || []);
  const [isDirty, setIsDirty] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Handle edge connections
  const onConnect = useCallback(
    (connection: Connection) => {
      const newEdge = {
        ...connection,
        id: `e-${uuidv4()}`,
        type: 'smoothstep',
        animated: false,
        style: { stroke: '#555', strokeWidth: 2 },
      };
      setEdges((eds) => addEdge(newEdge, eds));
      setIsDirty(true);
    },
    [setEdges]
  );

  // Track changes to mark as dirty
  useEffect(() => {
    if (nodes.length > 0 || edges.length > 0) {
      setIsDirty(true);
    }
  }, [nodes, edges]);

  // Add a new node to the diagram
  const addNode = useCallback(
    (type: 'page' | 'action' | 'decision' | 'api', label: string) => {
      const newNode: Node = {
        id: `node-${uuidv4()}`,
        type: 'customNode',
        position: {
          x: Math.random() * 300,
          y: Math.random() * 300,
        },
        data: {
          label,
          type,
          description: '',
          features: [],
        },
      };
      
      setNodes((nds) => [...nds, newNode]);
      setIsDirty(true);
    },
    [setNodes]
  );

  // Save the flow to the database
  const saveFlow = async () => {
    try {
      setIsLoading(true);
      
      // Convert React Flow nodes to our custom format
      const flowData: FlowData = {
        nodes: nodes.map(convertToFlowNode),
        edges: edges as FlowEdge[],
      };
      
      // Check if a user flow already exists for this project
      const { data: existingFlow, error: fetchError } = await supabase
        .from('user_flows')
        .select('id')
        .eq('project_id', projectId)
        .maybeSingle();
      
      let result;
      
      if (existingFlow?.id) {
        // Update existing flow
        result = await supabase
          .from('user_flows')
          .update({
            flow_data: flowData,
            updated_at: new Date().toISOString(),
          })
          .eq('id', existingFlow.id);
      } else {
        // Create new flow
        result = await supabase
          .from('user_flows')
          .insert({
            project_id: projectId,
            flow_data: flowData,
          });
      }
      
      if (result.error) {
        throw result.error;
      }
      
      setIsDirty(false);
      toast.success('Flow diagram saved successfully');
    } catch (error) {
      console.error('Error saving flow:', error);
      toast.error('Failed to save flow diagram');
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to convert React Flow nodes to our custom format
  function convertToFlowNode(node: Node): FlowNodeType {
    return {
      id: node.id,
      type: node.data.type || 'page',
      position: node.position,
      data: {
        label: node.data.label,
        description: node.data.description || '',
        features: node.data.features || [],
      },
    };
  }

  // Helper function to convert our custom nodes to React Flow format
  function convertToReactFlowNode(node: FlowNodeType): Node {
    return {
      id: node.id,
      type: 'customNode',
      position: node.position,
      data: {
        ...node.data,
        type: node.type,
      },
    };
  }

  return (
    <div className="h-[80vh] w-full border border-gray-200 rounded-lg">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={readOnly ? undefined : onNodesChange}
        onEdgesChange={readOnly ? undefined : onEdgesChange}
        onConnect={readOnly ? undefined : onConnect}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-right"
      >
        <Background />
        <Controls />
        <MiniMap />
        
        {!readOnly && (
          <Panel position="top-right">
            <div className="flex gap-2">
              <SaveButton 
                onClick={saveFlow} 
                disabled={!isDirty || isLoading} 
                loading={isLoading} 
              />
            </div>
          </Panel>
        )}
        
        {!readOnly && (
          <Panel position="top-left">
            <AddNodePanel onAddNode={addNode} />
          </Panel>
        )}
      </ReactFlow>
    </div>
  );
}
```

#### Custom Node Component

```typescript
// src/components/userflow/FlowNode.tsx

import { memo } from 'react';
import { Handle, NodeProps, Position } from 'reactflow';

interface CustomNodeData {
  label: string;
  type: 'page' | 'action' | 'decision' | 'api';
  description?: string;
  features?: string[];
}

function FlowNode({ data, isConnectable }: NodeProps<CustomNodeData>) {
  // Determine node style based on type
  const getNodeStyle = () => {
    switch (data.type) {
      case 'page':
        return {
          background: '#e3f2fd',
          borderColor: '#2196f3',
          icon: '📄',
        };
      case 'action':
        return {
          background: '#e8f5e9',
          borderColor: '#4caf50',
          icon: '🔄',
        };
      case 'decision':
        return {
          background: '#fff3e0',
          borderColor: '#ff9800',
          icon: '❓',
        };
      case 'api':
        return {
          background: '#f3e5f5',
          borderColor: '#9c27b0',
          icon: '🔌',
        };
      default:
        return {
          background: '#f5f5f5',
          borderColor: '#9e9e9e',
          icon: '📄',
        };
    }
  };

  const nodeStyle = getNodeStyle();

  return (
    <div
      className="px-4 py-2 rounded-md shadow-md border-2"
      style={{
        background: nodeStyle.background,
        borderColor: nodeStyle.borderColor,
        minWidth: '150px',
      }}
    >
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className="w-2 h-2"
      />
      
      <div className="flex items-center gap-2">
        <span>{nodeStyle.icon}</span>
        <div className="font-medium text-sm">{data.label}</div>
      </div>
      
      {data.description && (
        <div className="text-xs text-gray-600 mt-1">{data.description}</div>
      )}
      
      {data.features && data.features.length > 0 && (
        <div className="mt-2">
          <div className="text-xs font-medium text-gray-700">Features:</div>
          <div className="flex flex-wrap gap-1 mt-1">
            {data.features.map((feature) => (
              <span
                key={feature}
                className="text-xs px-1 py-0.5 bg-white bg-opacity-50 rounded"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      )}
      
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className="w-2 h-2"
      />
    </div>
  );
}

export default memo(FlowNode);
```

#### Add Node Panel Component

```typescript
// src/components/userflow/AddNodePanel.tsx

import { useState } from 'react';

interface AddNodePanelProps {
  onAddNode: (type: 'page' | 'action' | 'decision' | 'api', label: string) => void;
}

export default function AddNodePanel({ onAddNode }: AddNodePanelProps) {
  const [nodeType, setNodeType] = useState<'page' | 'action' | 'decision' | 'api'>('page');
  const [nodeName, setNodeName] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  const handleAddNode = () => {
    if (nodeName.trim()) {
      onAddNode(nodeType, nodeName.trim());
      setNodeName('');
      setIsExpanded(false);
    }
  };

  return (
    <div className="bg-white rounded-md shadow-md p-2 border border-gray-200">
      {!isExpanded ? (
        <button
          onClick={() => setIsExpanded(true)}
          className="flex items-center gap-2 px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          <span>+</span> Add Node
        </button>
      ) : (
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Node Type</label>
            <select
              value={nodeType}
              onChange={(e) => setNodeType(e.target.value as any)}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded"
            >
              <option value="page">Page</option>
              <option value="action">Action</option>
              <option value="decision">Decision</option>
              <option value="api">API</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Node Name</label>
            <input
              type="text"
              value={nodeName}
              onChange={(e) => setNodeName(e.target.value)}
              placeholder="Enter node name"
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded"
            />
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleAddNode}
              disabled={!nodeName.trim()}
              className="px-3 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 disabled:bg-blue-300"
            >
              Add
            </button>
            <button
              onClick={() => setIsExpanded(false)}
              className="px-3 py-2 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
```

#### Save Button Component

```typescript
// src/components/userflow/SaveButton.tsx

interface SaveButtonProps {
  onClick: () => void;
  disabled?: boolean;
  loading?: boolean;
}

export default function SaveButton({ onClick, disabled = false, loading = false }: SaveButtonProps) {
  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`px-4 py-2 rounded text-sm font-medium flex items-center gap-2 ${
        disabled
          ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
          : 'bg-blue-500 text-white hover:bg-blue-600'
      }`}
    >
      {loading ? (
        <>
          <svg
            className="animate-spin h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Saving...
        </>
      ) : (
        <>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
            />
          </svg>
          Save
        </>
      )}
    </button>
  );
}
```

### API Implementation

#### User Flow API Routes

```typescript
// src/app/api/projects/[id]/userflow/route.ts

import { supabase } from '@/lib/supabase/client';
import { FlowData } from '@/types/userFlow';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }
    
    const { data, error } = await supabase
      .from('user_flows')
      .select('flow_data, version')
      .eq('project_id', projectId)
      .single();
    
    if (error) {
      // If no flow found, return empty data
      if (error.code === 'PGRST116') {
        return NextResponse.json({ 
          data: { 
            flowData: { nodes: [], edges: [] }, 
            version: 1 
          } 
        });
      }
      
      console.error('Error fetching user flow:', error);
      return NextResponse.json(
        { error: 'Failed to fetch user flow' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ 
      data: {
        flowData: data.flow_data,
        version: data.version
      } 
    });
  } catch (error) {
    console.error('Error fetching user flow:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user flow' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    const { flowData, version }: { flowData: FlowData; version?: number } = await request.json();
    
    if (!projectId || !flowData) {
      return NextResponse.json(
        { error: 'Project ID and flow data are required' },
        { status: 400 }
      );
    }
    
    // Check if a user flow already exists for this project
    const { data: existingFlow, error: fetchError } = await supabase
      .from('user_flows')
      .select('id, version')
      .eq('project_id', projectId)
      .maybeSingle();
    
    let result;
    
    if (existingFlow?.id) {
      // Optimistic concurrency control
      if (version && existingFlow.version !== version) {
        return NextResponse.json(
          { error: 'Flow has been modified by another user' },
          { status: 409 }
        );
      }
      
      // Update existing flow
      result = await supabase
        .from('user_flows')
        .update({
          flow_data: flowData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingFlow.id)
        .select('id, version');
    } else {
      // Create new flow
      result = await supabase
        .from('user_flows')
        .insert({
          project_id: projectId,
          flow_data: flowData,
        })
        .select('id, version');
    }
    
    if (result.error) {
      console.error('Error saving user flow:', result.error);
      return NextResponse.json(
        { error: 'Failed to save user flow' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ 
      data: {
        id: result.data[0].id,
        version: result.data[0].version
      },
      message: 'User flow saved successfully'
    });
  } catch (error) {
    console.error('Error saving user flow:', error);
    return NextResponse.json(
      { error: 'Failed to save user flow' },
      { status: 500 }
    );
  }
}
```

### AI Integration for User Flow Generation

The User Flow Diagram Editor includes AI-assisted flow generation based on the project's analysis and features. This helps users quickly create a starting point for their application's user flow.

#### AI Flow Generator

```typescript
// src/lib/ai/flowGenerator.ts

import { AnalysisData, CoreFeature } from '@/types/analysis';
import { FlowData, FlowNode, FlowEdge } from '@/types/userFlow';
import { v4 as uuidv4 } from 'uuid';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function generateUserFlow(
  projectName: string,
  projectDescription: string,
  analysisData: AnalysisData
): Promise<FlowData> {
  try {
    const prompt = generateFlowPrompt(projectName, projectDescription, analysisData);
    
    const response = await openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [
        {
          role: 'system',
          content: `You are an expert UX designer and application architect. 
          Generate a user flow diagram for a SaaS application based on the provided details.
          The flow should include pages, actions, decisions, and API calls.
          Respond with a structured JSON object following the specified format.`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      response_format: { type: 'json_object' }
    });

    const flowData = JSON.parse(response.choices[0].message.content || '{}');
    
    // Process and validate the flow data
    return processFlowData(flowData, analysisData.coreFeatures);
  } catch (error) {
    console.error('Error generating user flow:', error);
    throw new Error('Failed to generate user flow');
  }
}

function generateFlowPrompt(
  projectName: string,
  projectDescription: string,
  analysisData: AnalysisData
): string {
  const coreFeatures = analysisData.coreFeatures
    .map(feature => `- ${feature.name}: ${feature.description}`)
    .join('\n');
  
  return `
    Please generate a user flow diagram for the following SaaS application:
    
    Name: ${projectName}
    Description: ${projectDescription}
    
    Core Features:
    ${coreFeatures}
    
    The user flow should include:
    1. Landing page
    2. Authentication (login/signup)
    3. Main dashboard
    4. Core feature pages
    5. Settings/profile pages
    6. Any necessary API interactions or decision points
    
    For each node in the flow, provide:
    - A descriptive label
    - The type (page, action, decision, or api)
    - A brief description
    - Associated features (if applicable)
    
    Return the flow in the following JSON format:
    {
      "nodes": [
        {
          "id": "unique-id",
          "type": "page|action|decision|api",
          "position": { "x": number, "y": number },
          "data": {
            "label": "Node Label",
            "description": "Brief description",
            "features": ["feature1", "feature2"]
          }
        },
        ...
      ],
      "edges": [
        {
          "id": "unique-id",
          "source": "source-node-id",
          "target": "target-node-id",
          "label": "Optional label for the connection",
          "type": "default|success|error|conditional"
        },
        ...
      ]
    }
    
    Position the nodes in a logical layout with appropriate spacing.
  `;
}

function processFlowData(data: any, coreFeatures: CoreFeature[]): FlowData {
  // Ensure nodes have unique IDs
  const nodes: FlowNode[] = (data.nodes || []).map((node: any, index: number) => ({
    id: node.id || `node-${uuidv4()}`,
    type: node.type || 'page',
    position: node.position || { x: index * 200, y: Math.floor(index / 3) * 200 },
    data: {
      label: node.data?.label || `Node ${index + 1}`,
      description: node.data?.description || '',
      features: node.data?.features || [],
    },
  }));
  
  // Ensure edges have unique IDs and valid source/target
  const nodeIds = nodes.map(node => node.id);
  const edges: FlowEdge[] = (data.edges || [])
    .filter((edge: any) => 
      edge.source && 
      edge.target && 
      nodeIds.includes(edge.source) && 
      nodeIds.includes(edge.target)
    )
    .map((edge: any) => ({
      id: edge.id || `edge-${uuidv4()}`,
      source: edge.source,
      target: edge.target,
      label: edge.label,
      type: edge.type || 'default',
    }));
  
  return { nodes, edges };
}
```

#### Flow Generator Component

```typescript
// src/components/userflow/FlowGenerator.tsx

'use client';

import { useState } from 'react';
import { generateUserFlow } from '@/lib/ai/flowGenerator';
import { AnalysisData } from '@/types/analysis';
import { FlowData } from '@/types/userFlow';
import { toast } from 'react-hot-toast';

interface FlowGeneratorProps {
  projectId: string;
  projectName: string;
  projectDescription: string;
  analysisData: AnalysisData;
  onFlowGenerated: (flowData: FlowData) => void;
}

export default function FlowGenerator({
  projectId,
  projectName,
  projectDescription,
  analysisData,
  onFlowGenerated,
}: FlowGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateFlow = async () => {
    try {
      setIsGenerating(true);
      
      const flowData = await generateUserFlow(
        projectName,
        projectDescription,
        analysisData
      );
      
      onFlowGenerated(flowData);
      toast.success('User flow generated successfully');
    } catch (error) {
      console.error('Error generating flow:', error);
      toast.error('Failed to generate user flow');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="mb-6">
      <button
        onClick={handleGenerateFlow}
        disabled={isGenerating}
        className={`px-4 py-2 rounded text-sm font-medium flex items-center gap-2 ${
          isGenerating
            ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
            : 'bg-purple-600 text-white hover:bg-purple-700'
        }`}
      >
        {isGenerating ? (
          <>
            <svg
              className="animate-spin h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            Generating Flow...
          </>
        ) : (
          <>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
              />
            </svg>
            Generate AI Flow
          </>
        )}
      </button>
      <p className="mt-2 text-xs text-gray-500">
        Let AI create a user flow diagram based on your project description and analysis.
      </p>
    </div>
  );
}
```

### User Flow Page Implementation

```typescript
// src/app/studio/[projectId]/userflow/page.tsx

'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { FlowData } from '@/types/userFlow';
import { AnalysisData } from '@/types/analysis';
import { supabase } from '@/lib/supabase/client';
import FlowEditor from '@/components/userflow/FlowEditor';
import FlowGenerator from '@/components/userflow/FlowGenerator';
import { toast } from 'react-hot-toast';

export default function UserFlowPage() {
  const params = useParams();
  const projectId = params.projectId as string;
  
  const [flowData, setFlowData] = useState<FlowData | null>(null);
  const [projectData, setProjectData] = useState<{
    name: string;
    description: string;
    analysisData: AnalysisData | null;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true);
        setError(null);
        
        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('name, description, analysis_data')
          .eq('id', projectId)
          .single();
        
        if (projectError) {
          throw new Error('Failed to fetch project data');
        }
        
        // Fetch user flow data
        const { data: flowData, error: flowError } = await supabase
          .from('user_flows')
          .select('flow_data')
          .eq('project_id', projectId)
          .maybeSingle();
        
        setProjectData({
          name: projectData.name,
          description: projectData.description,
          analysisData: projectData.analysis_data,
        });
        
        if (flowData) {
          setFlowData(flowData.flow_data);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data');
        toast.error('Failed to load user flow data');
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchData();
  }, [projectId]);

  const handleFlowGenerated = (newFlowData: FlowData) => {
    setFlowData(newFlowData);
  };

  if (isLoading) {
    return <div className="p-6">Loading user flow...</div>;
  }

  if (error || !projectData) {
    return (
      <div className="p-6">
        <div className="text-red-600">{error || 'Failed to load project data'}</div>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">User Flow Diagram</h1>
        <p className="text-gray-600">
          Design the user journey for your application by creating a visual flow diagram.
        </p>
      </div>
      
      {projectData.analysisData && !flowData?.nodes?.length && (
        <FlowGenerator
          projectId={projectId}
          projectName={projectData.name}
          projectDescription={projectData.description}
          analysisData={projectData.analysisData}
          onFlowGenerated={handleFlowGenerated}
        />
      )}
      
      <FlowEditor
        projectId={projectId}
        initialData={flowData || { nodes: [], edges: [] }}
      />
    </div>
  );
}
```

## Best Practices for User Flow Design

When creating user flows for your SaaS application, consider these best practices:

1. **Start with the main user journey**: Begin with the core path that most users will take through your application.

2. **Keep it simple**: Don't overcomplicate the flow with too many decision points or branches.

3. **Group related actions**: Organize your flow by grouping related actions and pages together.

4. **Consider edge cases**: Include error paths and alternative flows for different user scenarios.

5. **Connect to features**: Link nodes in your flow to specific features to maintain traceability.

6. **Use consistent node types**: Maintain a clear distinction between pages, actions, decisions, and API calls.

7. **Add descriptive labels**: Make sure node labels clearly describe the purpose of each step.

8. **Review and iterate**: Regularly review and update your flow as your application evolves.

## Integration with Kanban Tickets

The user flow diagram integrates with the Kanban ticket board, allowing developers to:

1. Link flow nodes to specific features/tickets
2. Track implementation progress directly from the flow diagram
3. Generate new tickets based on flow nodes
4. Ensure all user journey steps are properly implemented

This integration creates a seamless connection between the user experience design and the development process, helping teams maintain alignment throughout the project lifecycle. 