"use client";
import { useState } from "react";

const sections = [
  { id: "getting-started", label: "Getting Started" },
  { id: "ai-insights", label: "AI Insights" },
  { id: "idea-validator", label: "AI Idea Validator" },
  { id: "settings", label: "Settings" },
  { id: "support", label: "Support" },
  { id: "api", label: "API" },
  { id: "faq", label: "FAQ" },
];

export default function DocsPage() {
  const [active, setActive] = useState("getting-started");

  return (
    <div className="flex min-h-screen">
      <aside className="w-64 bg-slate-900/80 border-r border-slate-800 p-6">
        <h2 className="text-xl font-bold text-white mb-6">Documentation</h2>
        <nav className="space-y-2">
          {sections.map(section => (
            <button
              key={section.id}
              onClick={() => setActive(section.id)}
              className={`block w-full text-left px-3 py-2 rounded-lg font-medium transition-colors ${active === section.id ? 'bg-blue-600 text-white' : 'text-slate-300 hover:bg-slate-800'}`}
            >
              {section.label}
            </button>
          ))}
        </nav>
      </aside>
      <main className="flex-1 p-10">
        {active === "getting-started" && (
          <div>
            <h1 className="text-3xl font-bold mb-4 text-white">Getting Started</h1>
            <ol className="text-slate-300 list-decimal ml-6 space-y-2">
              <li>Sign up or sign in to your SaaSifyX account.</li>
              <li>Navigate using the sidebar: Dashboard, AI Insights, AI Idea Validator, Settings, Documentation, and Support.</li>
              <li>Use the <b>AI Idea Validator</b> to validate your SaaS ideas with market, technical, and business analysis.</li>
              <li>Explore <b>AI Insights</b> for project analytics and recommendations.</li>
              <li>Manage your profile and preferences in <b>Settings</b> (theme, notifications, language, etc).</li>
              <li>Access <b>Documentation</b> and <b>Support</b> for help and FAQs.</li>
            </ol>
            <div className="mt-8">
              <h2 className="text-2xl font-bold mb-2 text-white">Main Components</h2>
              <ul className="text-slate-300 list-disc ml-6 space-y-1">
                <li><b>Dashboard</b>: Overview of your projects and quick actions.</li>
                <li><b>AI Insights</b>: Advanced analytics and recommendations for your SaaS projects.</li>
                <li><b>AI Idea Validator</b>: Validate new SaaS ideas with AI-powered analysis.</li>
                <li><b>Settings</b>: Manage your profile, theme, notifications, and account.</li>
                <li><b>Documentation</b>: Guides, API reference, and FAQs.</li>
                <li><b>Support</b>: Contact form and help resources.</li>
              </ul>
            </div>
          </div>
        )}
        {active === "ai-insights" && (
          <div>
            <h1 className="text-3xl font-bold mb-4 text-white">AI Insights</h1>
            <p className="text-slate-300 mb-4">AI Insights provides analytics and actionable recommendations for your SaaS projects. Use it to:</p>
            <ul className="text-slate-300 list-disc ml-6 space-y-1">
              <li>View project metrics and trends.</li>
              <li>Get AI-powered suggestions for growth and optimization.</li>
              <li>Track user engagement and feature usage.</li>
              <li>Identify potential issues and opportunities.</li>
            </ul>
            <div className="mt-4 text-slate-400">Navigate to <b>AI Insights</b> from the sidebar. Select a project to see detailed analytics and recommendations.</div>
          </div>
        )}
        {active === "idea-validator" && (
          <div>
            <h1 className="text-3xl font-bold mb-4 text-white">AI Idea Validator</h1>
            <p className="text-slate-300 mb-4">The AI Idea Validator helps you assess new SaaS ideas before you build. It provides:</p>
            <ul className="text-slate-300 list-disc ml-6 space-y-1">
              <li><b>Market Analysis</b>: Size, competition, and demand.</li>
              <li><b>Technical Feasibility</b>: Complexity and resource needs.</li>
              <li><b>Business Viability</b>: Revenue projections and validation.</li>
              <li><b>Strategic Recommendations</b>: Next steps and actionable insights.</li>
            </ul>
            <div className="mt-4 text-slate-400">To use: Go to <b>AI Idea Validator</b> in the sidebar, click "New Validation", and fill in your idea details.</div>
          </div>
        )}
        {active === "settings" && (
          <div>
            <h1 className="text-3xl font-bold mb-4 text-white">Settings</h1>
            <p className="text-slate-300 mb-4">The Settings page lets you personalize your SaaSifyX experience:</p>
            <ul className="text-slate-300 list-disc ml-6 space-y-1">
              <li>Edit your profile information (name, email, avatar).</li>
              <li>Switch between dark, light, or system theme.</li>
              <li>Enable or disable email notifications.</li>
              <li>Select your preferred language (future feature).</li>
              <li>Change your password (stub in demo).</li>
              <li>Delete your account (stub in demo).</li>
            </ul>
            <div className="mt-4 text-slate-400">All changes are saved instantly. Some features require a real backend connection.</div>
          </div>
        )}
        {active === "support" && (
          <div>
            <h1 className="text-3xl font-bold mb-4 text-white">Support</h1>
            <p className="text-slate-300 mb-4">Need help? The Support page offers:</p>
            <ul className="text-slate-300 list-disc ml-6 space-y-1">
              <li>A contact form to reach our team directly.</li>
              <li>Support email: <a href="mailto:<EMAIL>" className="text-blue-400 underline"><EMAIL></a></li>
              <li>Frequently Asked Questions (FAQ).</li>
              <li>Links to documentation and resources.</li>
            </ul>
            <div className="mt-4 text-slate-400">For urgent issues, email us directly. For common questions, check the FAQ section.</div>
          </div>
        )}
        {active === "api" && (
          <div>
            <h1 className="text-3xl font-bold mb-4 text-white">API Reference</h1>
            <p className="text-slate-300 mb-4">SaaSifyX provides a RESTful API for project and idea management. (API endpoints are for demonstration only.)</p>
            <ul className="text-slate-300 list-disc ml-6 space-y-1">
              <li><b>POST /api/ideas</b>: Submit a new SaaS idea for validation.</li>
              <li><b>GET /api/ideas</b>: List your validated ideas.</li>
              <li><b>GET /api/projects</b>: List your SaaS projects.</li>
              <li><b>PATCH /api/user</b>: Update user profile and preferences.</li>
              <li><b>GET /api/insights</b>: Get analytics and recommendations for your projects.</li>
            </ul>
            <div className="mt-6">
              <b>Note:</b> API requires authentication. Use your account token in the <code>Authorization</code> header.
            </div>
          </div>
        )}
        {active === "faq" && (
          <div>
            <h1 className="text-3xl font-bold mb-4 text-white">FAQ</h1>
            <ul className="text-slate-300 space-y-4">
              <li><b>How do I change the theme?</b><br />Go to Settings &gt; Theme and select Dark, Light, or System. The UI will update instantly.</li>
              <li><b>How do I change my password?</b><br />Go to Settings and click "Change Password". (In this demo, this is a stub. In production, you'll receive a password reset email.)</li>
              <li><b>How do I delete my account?</b><br />Go to Settings and click "Delete Account". (In this demo, this is a stub. In production, your account and data will be removed.)</li>
              <li><b>How do I validate a SaaS idea?</b><br />Use the AI Idea Validator from the sidebar, fill in your idea details, and get instant analysis.</li>
              <li><b>How do I contact support?</b><br />Go to the Support page and use the contact form <NAME_EMAIL>.</li>
              <li><b>How do I navigate the app?</b><br />Use the sidebar for all main sections. The top bar provides quick access to your profile and notifications.</li>
              <li><b>How do I enable or disable notifications?</b><br />Go to Settings and use the notifications toggle. (In this demo, this is a UI-only feature.)</li>
              <li><b>Can I change the language?</b><br />A language selector is available in Settings. More languages will be supported soon.</li>
              <li><b>Does SaaSifyX integrate with other tools?</b><br />Integrations are planned for future releases. Stay tuned for updates!</li>
              <li><b>I'm having trouble logging in or using a feature. What should I do?</b><br />First, try refreshing the page or clearing your browser cache. If the issue persists, contact support.</li>
            </ul>
          </div>
        )}
      </main>
    </div>
  );
} 