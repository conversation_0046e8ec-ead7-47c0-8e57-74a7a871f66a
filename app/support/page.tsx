"use client";
import { useState } from "react";

export default function SupportPage() {
  const [form, setForm] = useState({ name: "", email: "", message: "" });
  const [submitted, setSubmitted] = useState(false);

  function handleChange(e) {
    setForm({ ...form, [e.target.name]: e.target.value });
  }

  function handleSubmit(e) {
    e.preventDefault();
    setSubmitted(true);
  }

  return (
    <div className="max-w-2xl mx-auto py-12 px-4">
      <div className="bg-slate-900/80 rounded-2xl shadow-xl p-8 border border-slate-800">
        <h2 className="text-3xl font-bold mb-6 text-white">Help & Support</h2>
        <p className="mb-4 text-slate-300">Need help? Contact us below or email <a href='mailto:<EMAIL>' className='text-blue-400 underline'><EMAIL></a>.</p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <input name="name" value={form.name} onChange={handleChange} placeholder="Your Name" className="w-full px-4 py-2 rounded-lg bg-slate-800 text-white" required />
          <input name="email" value={form.email} onChange={handleChange} placeholder="Your Email" type="email" className="w-full px-4 py-2 rounded-lg bg-slate-800 text-white" required />
          <textarea name="message" value={form.message} onChange={handleChange} placeholder="Your Message" className="w-full px-4 py-2 rounded-lg bg-slate-800 text-white" rows={4} required />
          <button type="submit" className="btn-premium w-full py-2 rounded-lg font-semibold">Send Message</button>
        </form>
        {submitted && <div className="mt-4 text-green-400 font-medium">Thank you! We'll get back to you soon.</div>}
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-slate-200 mb-2">FAQ</h3>
          <ul className="space-y-2">
            <li className="text-slate-300"><b>How do I reset my password?</b> Use the password reset link on the login page.</li>
            <li className="text-slate-300"><b>How do I contact support?</b> Use the form above or email us directly.</li>
            <li className="text-slate-300"><b>Where can I find documentation?</b> Visit the Documentation page from the sidebar.</li>
          </ul>
        </div>
      </div>
    </div>
  );
} 