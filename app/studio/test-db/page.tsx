"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { getUserValidationsAction } from "@/lib/actions/idea-validation-db-actions"

export default function TestDbPage() {
  const [result, setResult] = useState<string>("")
  const [loading, setLoading] = useState(false)

  const testDatabase = async () => {
    setLoading(true)
    setResult("Testing database connection...")
    
    try {
      const validationsResult = await getUserValidationsAction()
      
      if (validationsResult.success) {
        setResult(`✅ Database connection successful!\nFound ${validationsResult.data?.length || 0} validations`)
      } else {
        setResult(`❌ Database error: ${validationsResult.error}`)
      }
    } catch (error) {
      setResult(`❌ Connection failed: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>Database Connection Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testDatabase} disabled={loading}>
            {loading ? "Testing..." : "Test Database Connection"}
          </Button>
          
          {result && (
            <div className="p-4 bg-slate-100 rounded-lg">
              <pre className="whitespace-pre-wrap">{result}</pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
