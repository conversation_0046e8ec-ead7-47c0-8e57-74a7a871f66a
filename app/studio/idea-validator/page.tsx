"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Lightbulb,
  TrendingUp,
  Code,
  DollarSign,
  Users,
  Target,
  AlertTriangle,
  CheckCircle2,
  Loader2,
  Sparkles,
  BarChart3,
  Shield,
  ArrowLeft,
  Save,
  ExternalLink,
  Trash2,
  History,
  Calendar
} from "lucide-react"
import DashboardShell from "@/components/studio/dashboard/dashboard-shell"
import { validateIdeaWithGemini } from "@/lib/actions/idea-validator-actions"
import {
  IdeaValidationInput,
  IdeaValidationResult,
  getValidationScoreColor,
  getValidationScoreBg,
  getRiskLevelColor
} from "@/lib/services/idea-validator-service"
import {
  getCurrentUserId,
  isUserAuthenticated,
  getUserValidationsClient,
  deleteValidationClient
} from "@/lib/services/idea-validation-client-service"
import { saveValidationResultAction } from "@/lib/actions/idea-validation-db-actions"
import { IdeaValidationRecord } from "@/lib/services/idea-validation-db-service"
import ValidationCard from "@/components/studio/idea-validator/validation-card"
import IdeaValidatorHeader from "@/components/studio/idea-validator/idea-validator-header"
import ValidationsGrid from "@/components/studio/idea-validator/validations-grid"
import EmptyState from "@/components/studio/idea-validator/empty-state"

export default function IdeaValidatorPage() {
  const [formData, setFormData] = useState<IdeaValidationInput>({
    ideaTitle: "",
    ideaDescription: "",
    targetMarket: "",
    businessModel: "",
    technicalComplexity: "medium",
    budget: undefined,
    timeline: ""
  })

  const [validationResult, setValidationResult] = useState<IdeaValidationResult | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoadingAuth, setIsLoadingAuth] = useState(true)
  const [isCachedResult, setIsCachedResult] = useState(false)
  const [savedValidations, setSavedValidations] = useState<IdeaValidationRecord[]>([])
  const [isLoadingValidations, setIsLoadingValidations] = useState(false)
  const [isCurrentValidationSaved, setIsCurrentValidationSaved] = useState(false)
  const [showForm, setShowForm] = useState(false)

  // Check authentication on component mount
  useEffect(() => {
    const checkAuth = async () => {
      setIsLoadingAuth(true)
      try {
        const authenticated = await isUserAuthenticated()
        setIsAuthenticated(authenticated)

        if (authenticated) {
          const userId = await getCurrentUserId()
          setCurrentUserId(userId)
          // Load saved validations
          loadSavedValidations()
        }
      } catch (error) {
        console.error('Error checking authentication:', error)
      } finally {
        setIsLoadingAuth(false)
      }
    }

    checkAuth()
  }, [])

  const loadSavedValidations = async () => {
    if (!isAuthenticated) return

    setIsLoadingValidations(true)
    try {
      const validations = await getUserValidationsClient()
      setSavedValidations(validations)

      // Don't automatically show form - let the user choose
      // Cards should be shown by default if validations exist

      // Check if current validation is already saved
      if (validationResult && formData.ideaTitle) {
        const currentExists = validations.some(v =>
          v.idea_title === formData.ideaTitle &&
          v.idea_description === formData.ideaDescription
        )
        setIsCurrentValidationSaved(currentExists)
      }
    } catch (error) {
      console.error('Error loading saved validations:', error)
    } finally {
      setIsLoadingValidations(false)
    }
  }

  const handleDeleteValidation = async (id: string) => {
    if (!confirm('Are you sure you want to delete this validation? This action cannot be undone.')) {
      return
    }

    try {
      await deleteValidationClient(id)
      // Remove from local state
      setSavedValidations(prev => prev.filter(v => v.id !== id))
      alert('Validation deleted successfully!')
    } catch (error) {
      console.error('Error deleting validation:', error)
      alert('Failed to delete validation. Please try again.')
    }
  }

  const handleLoadValidation = (validation: IdeaValidationRecord) => {
    // Load the validation data into the form
    setFormData({
      ideaTitle: validation.idea_title,
      ideaDescription: validation.idea_description,
      targetMarket: validation.target_market,
      businessModel: validation.business_model,
      technicalComplexity: validation.technical_complexity as "low" | "medium" | "high",
      budget: validation.budget,
      timeline: validation.timeline || ""
    })
    setValidationResult(validation.validation_result)
    setIsCachedResult(true)
    setShowForm(true) // Show form with loaded validation
  }

  const handleNewValidation = () => {
    resetForm()
    setShowForm(true)
  }

  const handleBackToValidations = () => {
    setShowForm(false)
    setValidationResult(null)
  }

  const handleInputChange = (field: keyof IdeaValidationInput, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleValidate = async () => {
    if (!formData.ideaTitle || !formData.ideaDescription || !formData.targetMarket || !formData.businessModel) {
      setError("Please fill in all required fields")
      return
    }

    setIsValidating(true)
    setError(null)

    try {
      // Log validation attempt
      if (isAuthenticated && currentUserId) {
        console.log('Validating idea with caching enabled for user:', currentUserId)
      } else {
        console.log('Validating idea without caching (user not authenticated)')
      }

      // Pass caching flag based on authentication
      const startTime = Date.now()
      const result = await validateIdeaWithGemini(
        formData,
        isAuthenticated // Use caching if user is authenticated
      )
      const endTime = Date.now()

      setValidationResult(result)

      // Check if this was a cached result (very fast response)
      const responseTime = endTime - startTime
      const wasCached = responseTime < 2000 // Less than 2 seconds suggests cached result
      setIsCachedResult(wasCached)

      // Check if this validation is already saved
      if (isAuthenticated && savedValidations.length > 0) {
        const alreadySaved = savedValidations.some(v =>
          v.idea_title === formData.ideaTitle &&
          v.idea_description === formData.ideaDescription
        )
        setIsCurrentValidationSaved(alreadySaved)
      }

      if (wasCached) {
        console.log('Loaded cached validation result in', responseTime, 'ms')
      } else {
        console.log('Generated new validation result in', responseTime, 'ms')
      }
    } catch (err) {
      console.error('Validation error:', err)
      setError("Failed to validate idea. Please try again.")
    } finally {
      setIsValidating(false)
    }
  }

  const resetForm = () => {
    setFormData({
      ideaTitle: "",
      ideaDescription: "",
      targetMarket: "",
      businessModel: "",
      technicalComplexity: "medium",
      budget: undefined,
      timeline: ""
    })
    setValidationResult(null)
    setError(null)
    setIsCachedResult(false)
    setIsCurrentValidationSaved(false)
  }

  const handleSaveValidation = async () => {
    if (!validationResult) return

    if (!isAuthenticated) {
      alert('Please log in to save validation results.')
      return
    }

    setIsSaving(true)
    try {
      // Try to save the validation result
      const saveResult = await saveValidationResultAction(formData, validationResult)

      if (saveResult.success) {
        // Check if it's a duplicate (success but with error message)
        if (saveResult.error?.includes('already exists')) {
          alert('This validation is already saved to your account!')
        } else {
          alert('Validation saved successfully to your account!')
        }
        // Always refresh saved validations list on success
        await loadSavedValidations()
        setIsCurrentValidationSaved(true)
      } else {
        alert(`Failed to save validation: ${saveResult.error}`)
      }
    } catch (error) {
      console.error('Error saving validation:', error)
      alert('Failed to save validation. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const getCompetitorUrl = (competitorName: string) => {
    // Simple URL generation - in a real app, you might have a mapping or API
    const cleanName = competitorName.toLowerCase().replace(/[^a-z0-9]/g, '')
    return `https://www.google.com/search?q=${encodeURIComponent(competitorName + ' website')}`
  }

  if (isLoadingAuth) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center h-full">
          <div className="flex items-center gap-2 text-slate-400">
            <Loader2 className="h-5 w-5 animate-spin" />
            Loading...
          </div>
        </div>
      </DashboardShell>
    )
  }

  // Determine what to show based on authentication and validation state
  const shouldShowCards = isAuthenticated && savedValidations.length > 0 && !showForm && !validationResult
  const shouldShowForm = showForm || (!isAuthenticated && !validationResult)
  const shouldShowEmptyState = isAuthenticated && savedValidations.length === 0 && !showForm && !validationResult



  return (
    <DashboardShell>
      <div className="flex flex-col space-y-6 h-full">
        {/* Header - Show different headers based on current view */}
        {shouldShowCards || shouldShowEmptyState ? (
          <IdeaValidatorHeader onNewValidation={handleNewValidation} />
        ) : (
          <div className="flex flex-col gap-4 flex-shrink-0">
            <div>
              {validationResult ? (
                <button
                  onClick={savedValidations.length > 0 ? handleBackToValidations : () => setShowForm(false)}
                  className="inline-flex items-center text-sm text-slate-400 hover:text-slate-300 mb-2"
                >
                  <ArrowLeft className="mr-1 h-4 w-4" />
                  {savedValidations.length > 0 ? 'Back to Validations' : 'Back to Idea Validator'}
                </button>
              ) : (
                savedValidations.length > 0 ? (
                  <button
                    onClick={handleBackToValidations}
                    className="inline-flex items-center text-sm text-slate-400 hover:text-slate-300 mb-2"
                  >
                    <ArrowLeft className="mr-1 h-4 w-4" />
                    Back to Validations
                  </button>
                ) : (
                  <Link
                    href="/studio/ai-insights"
                    className="inline-flex items-center text-sm text-slate-400 hover:text-slate-300 mb-2"
                  >
                    <ArrowLeft className="mr-1 h-4 w-4" />
                    Back to Insights
                  </Link>
                )
              )}
              <div className="flex justify-between items-center">
                <div>
                  <h1 className="text-2xl font-bold text-white flex items-center gap-2">
                    <Sparkles className="h-6 w-6 text-blue-400" />
                    AI Idea Validator
                  </h1>
                  <p className="text-slate-400 mt-1 text-sm">Get AI-powered insights and validation for your SaaS ideas</p>
                </div>
                <div className="flex items-center gap-3">
                  {validationResult && (
                    <>
                      <Button
                        variant="outline"
                        onClick={handleSaveValidation}
                        disabled={isSaving || isCurrentValidationSaved}
                        size="sm"
                        className={isCurrentValidationSaved ? "bg-green-500/20 border-green-500/50 text-green-400" : ""}
                      >
                        {isSaving ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4 mr-2" />
                        )}
                        {isCurrentValidationSaved ? 'Already Saved' : 'Save Results'}
                      </Button>
                      <Button variant="outline" onClick={handleNewValidation} size="sm">
                        <Lightbulb className="h-4 w-4 mr-2" />
                        Validate New Idea
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}



        {/* Main Content */}
        {isLoadingValidations ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2 text-slate-400">
              <Loader2 className="h-5 w-5 animate-spin" />
              Loading validations...
            </div>
          </div>
        ) : shouldShowEmptyState ? (
          <EmptyState onCreateNew={handleNewValidation} />
        ) : shouldShowCards ? (
          <div className="space-y-6">
            <ValidationsGrid
              validations={savedValidations}
              onValidationView={handleLoadValidation}
              onValidationDelete={handleDeleteValidation}
            />
          </div>
        ) : shouldShowForm && !validationResult ? (
          /* Validation Form */
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Form Section */}
            <div className="space-y-4">
              <Card className="card-premium">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white flex items-center gap-2 text-lg">
                    <Lightbulb className="h-4 w-4 text-blue-400" />
                    Idea Details
                  </CardTitle>
                  <CardDescription className="text-slate-400 text-sm">
                    Tell us about your SaaS idea
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label htmlFor="ideaTitle" className="text-slate-300 text-sm">Idea Title *</Label>
                    <Input
                      id="ideaTitle"
                      placeholder="e.g., AI-Powered Project Management Tool"
                      value={formData.ideaTitle}
                      onChange={(e) => handleInputChange('ideaTitle', e.target.value)}
                      className="mt-1 bg-slate-800 border-slate-700 text-white text-sm"
                    />
                  </div>

                  <div>
                    <Label htmlFor="ideaDescription" className="text-slate-300 text-sm">Idea Description *</Label>
                    <Textarea
                      id="ideaDescription"
                      placeholder="Describe your SaaS idea, what problem it solves, and how it works..."
                      value={formData.ideaDescription}
                      onChange={(e) => handleInputChange('ideaDescription', e.target.value)}
                      className="mt-1 bg-slate-800 border-slate-700 text-white min-h-[80px] text-sm"
                    />
                  </div>

                  <div>
                    <Label htmlFor="targetMarket" className="text-slate-300 text-sm">Target Market *</Label>
                    <Input
                      id="targetMarket"
                      placeholder="e.g., Small to medium businesses, Freelancers, Enterprise teams"
                      value={formData.targetMarket}
                      onChange={(e) => handleInputChange('targetMarket', e.target.value)}
                      className="mt-1 bg-slate-800 border-slate-700 text-white text-sm"
                    />
                  </div>

                  <div>
                    <Label htmlFor="businessModel" className="text-slate-300 text-sm">Business Model *</Label>
                    <Input
                      id="businessModel"
                      placeholder="e.g., Subscription-based, Freemium, One-time purchase"
                      value={formData.businessModel}
                      onChange={(e) => handleInputChange('businessModel', e.target.value)}
                      className="mt-1 bg-slate-800 border-slate-700 text-white text-sm"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="card-premium">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white flex items-center gap-2 text-lg">
                    <Code className="h-4 w-4 text-green-400" />
                    Technical & Business Details
                  </CardTitle>
                  <CardDescription className="text-slate-400 text-sm">
                    Optional details for better analysis
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label htmlFor="technicalComplexity" className="text-slate-300 text-sm">Technical Complexity</Label>
                    <Select
                      value={formData.technicalComplexity}
                      onValueChange={(value) => handleInputChange('technicalComplexity', value)}
                    >
                      <SelectTrigger className="mt-1 bg-slate-800 border-slate-700 text-white text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low - Simple features, basic tech stack</SelectItem>
                        <SelectItem value="medium">Medium - Moderate complexity, standard integrations</SelectItem>
                        <SelectItem value="high">High - Complex features, advanced tech requirements</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="budget" className="text-slate-300 text-sm">Available Budget (USD)</Label>
                    <Input
                      id="budget"
                      type="number"
                      placeholder="e.g., 50000"
                      value={formData.budget || ""}
                      onChange={(e) => handleInputChange('budget', parseInt(e.target.value) || undefined)}
                      className="mt-1 bg-slate-800 border-slate-700 text-white text-sm"
                    />
                  </div>

                  <div>
                    <Label htmlFor="timeline" className="text-slate-300 text-sm">Desired Timeline</Label>
                    <Input
                      id="timeline"
                      placeholder="e.g., 6 months to MVP, 1 year to market"
                      value={formData.timeline}
                      onChange={(e) => handleInputChange('timeline', e.target.value)}
                      className="mt-1 bg-slate-800 border-slate-700 text-white text-sm"
                    />
                  </div>
                </CardContent>
              </Card>

              {error && (
                <Card className="border-red-500/30 bg-red-500/10">
                  <CardContent className="p-3">
                    <div className="flex items-center gap-2 text-red-400">
                      <AlertTriangle className="h-4 w-4" />
                      <span className="text-sm">{error}</span>
                    </div>
                  </CardContent>
                </Card>
              )}

              <Button
                onClick={handleValidate}
                disabled={isValidating}
                className="w-full btn-premium"
                size="sm"
              >
                {isValidating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Analyzing Your Idea...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Validate My Idea
                  </>
                )}
              </Button>
            </div>

            {/* Info Section */}
            <div className="space-y-4">
              <Card className="card-premium">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white flex items-center gap-2 text-lg">
                    <BarChart3 className="h-4 w-4 text-purple-400" />
                    What You'll Get
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start gap-2">
                    <TrendingUp className="h-4 w-4 text-blue-400 mt-0.5" />
                    <div>
                      <h4 className="text-white font-medium text-sm">Market Analysis</h4>
                      <p className="text-slate-400 text-xs">Market size, competition, and demand assessment</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <Code className="h-4 w-4 text-green-400 mt-0.5" />
                    <div>
                      <h4 className="text-white font-medium text-sm">Technical Feasibility</h4>
                      <p className="text-slate-400 text-xs">Development complexity and resource requirements</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <DollarSign className="h-4 w-4 text-yellow-400 mt-0.5" />
                    <div>
                      <h4 className="text-white font-medium text-sm">Business Viability</h4>
                      <p className="text-slate-400 text-xs">Revenue projections and business model validation</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <Users className="h-4 w-4 text-purple-400 mt-0.5" />
                    <div>
                      <h4 className="text-white font-medium text-sm">Competitive Analysis</h4>
                      <p className="text-slate-400 text-xs">Competitor landscape and differentiation opportunities</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <Target className="h-4 w-4 text-orange-400 mt-0.5" />
                    <div>
                      <h4 className="text-white font-medium text-sm">Actionable Recommendations</h4>
                      <p className="text-slate-400 text-xs">Next steps and strategic guidance</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="card-premium border-blue-500/30">
                <CardContent className="p-3">
                  <div className="flex items-center gap-2">
                    <Shield className="h-6 w-6 text-blue-400" />
                    <div>
                      <h4 className="text-blue-300 font-medium text-sm">AI-Powered Analysis</h4>
                      <p className="text-blue-200/70 text-xs">
                        Our advanced AI analyzes thousands of data points to provide you with comprehensive,
                        data-driven insights about your SaaS idea's potential for success.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {!isAuthenticated && (
                <Card className="card-premium border-yellow-500/30">
                  <CardContent className="p-3">
                    <div className="flex items-center gap-2">
                      <Save className="h-5 w-5 text-yellow-400" />
                      <div>
                        <h4 className="text-yellow-300 font-medium text-sm">Save & Cache Results</h4>
                        <p className="text-yellow-200/70 text-xs">
                          Log in to save your validation results and get instant access to previously analyzed ideas.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        ) : (
          /* Validation Results */
          <div className="space-y-4 flex-1 overflow-auto">
            {/* Overall Score */}
            <Card className="card-premium">
              <CardHeader className="pb-3">
                <CardTitle className="text-white flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-blue-400" />
                    <span className="text-lg">Validation Results for "{formData.ideaTitle}"</span>
                  </div>
                  <div className={`text-xl font-bold ${getValidationScoreColor(validationResult.overallScore)}`}>
                    {validationResult.overallScore}/100
                  </div>
                </CardTitle>
                <CardDescription className="text-slate-400 text-sm flex items-center gap-2">
                  Comprehensive AI analysis completed on {new Date(validationResult.validationDate).toLocaleDateString()}
                  {isCachedResult && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">
                      <CheckCircle2 className="h-3 w-3" />
                      Cached Result
                    </span>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className={`p-3 rounded-lg border ${getValidationScoreBg(validationResult.successProbability)}`}>
                    <div className="flex items-center gap-2 mb-1">
                      <Target className="h-4 w-4" />
                      <span className="font-medium text-sm">Success Probability</span>
                    </div>
                    <div className={`text-xl font-bold ${getValidationScoreColor(validationResult.successProbability)}`}>
                      {validationResult.successProbability}%
                    </div>
                  </div>
                  <div className={`p-3 rounded-lg border bg-slate-500/20 border-slate-500/30`}>
                    <div className="flex items-center gap-2 mb-1">
                      <AlertTriangle className="h-4 w-4" />
                      <span className="font-medium text-sm">Risk Level</span>
                    </div>
                    <div className={`text-lg font-bold capitalize ${getRiskLevelColor(validationResult.riskLevel)}`}>
                      {validationResult.riskLevel}
                    </div>
                  </div>
                  <div className="p-3 rounded-lg border bg-blue-500/20 border-blue-500/30">
                    <div className="flex items-center gap-2 mb-1">
                      <CheckCircle2 className="h-4 w-4" />
                      <span className="font-medium text-sm">Validation Status</span>
                    </div>
                    <div className="text-lg font-bold text-blue-400">
                      {validationResult.overallScore >= 70 ? 'Promising' :
                       validationResult.overallScore >= 50 ? 'Moderate' : 'Challenging'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Detailed Analysis */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Market Potential */}
              <Card className="card-premium">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-blue-400" />
                      <span className="text-lg">Market Potential</span>
                    </div>
                    <span className={`text-lg font-bold ${getValidationScoreColor(validationResult.marketPotential.score)}`}>
                      {validationResult.marketPotential.score}/100
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Market Size</h4>
                    <p className="text-slate-400 text-xs">{validationResult.marketPotential.marketSize}</p>
                  </div>
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Competition</h4>
                    <p className="text-slate-400 text-xs">{validationResult.marketPotential.competitorCount}</p>
                  </div>
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Key Insights</h4>
                    <ul className="text-slate-400 text-xs space-y-1">
                      {validationResult.marketPotential.insights.map((insight, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-blue-400 mt-1">•</span>
                          {insight}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Technical Feasibility */}
              <Card className="card-premium">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-green-400" />
                      <span className="text-lg">Technical Feasibility</span>
                    </div>
                    <span className={`text-lg font-bold ${getValidationScoreColor(validationResult.technicalFeasibility.score)}`}>
                      {validationResult.technicalFeasibility.score}/100
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Development Time</h4>
                    <p className="text-slate-400 text-xs">{validationResult.technicalFeasibility.estimatedDevelopmentTime}</p>
                  </div>
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Required Skills</h4>
                    <div className="flex flex-wrap gap-1">
                      {validationResult.technicalFeasibility.requiredSkills.map((skill, index) => (
                        <span key={index} className="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Technical Risks</h4>
                    <ul className="text-slate-400 text-xs space-y-1">
                      {validationResult.technicalFeasibility.technicalRisks.map((risk, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-orange-400 mt-1">⚠</span>
                          {risk}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Business Viability & Competitive Analysis */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Business Viability */}
              <Card className="card-premium">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-yellow-400" />
                      <span className="text-lg">Business Viability</span>
                    </div>
                    <span className={`text-lg font-bold ${getValidationScoreColor(validationResult.businessViability.score)}`}>
                      {validationResult.businessViability.score}/100
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Revenue Model</h4>
                    <div className="flex flex-wrap gap-1">
                      {validationResult.businessViability.revenueModel.map((model, index) => (
                        <span key={index} className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded text-xs">
                          {model}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Revenue Projection</h4>
                    <p className="text-slate-400 text-xs">{validationResult.businessViability.estimatedRevenue}</p>
                  </div>
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Break-even Time</h4>
                    <p className="text-slate-400 text-xs">{validationResult.businessViability.breakEvenTime}</p>
                  </div>
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Funding Required</h4>
                    <p className="text-slate-400 text-xs">{validationResult.businessViability.fundingRequired}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Competitive Analysis */}
              <Card className="card-premium">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-purple-400" />
                      <span className="text-lg">Competitive Analysis</span>
                    </div>
                    <span className={`text-lg font-bold ${getValidationScoreColor(validationResult.competitiveAnalysis.score)}`}>
                      {validationResult.competitiveAnalysis.score}/100
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Main Competitors</h4>
                    <div className="flex flex-wrap gap-1">
                      {validationResult.competitiveAnalysis.mainCompetitors.map((competitor, index) => (
                        <a
                          key={index}
                          href={getCompetitorUrl(competitor)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="px-2 py-1 bg-purple-500/20 text-purple-400 rounded text-xs hover:bg-purple-500/30 transition-colors cursor-pointer inline-flex items-center gap-1"
                        >
                          {competitor}
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Market Gap</h4>
                    <p className="text-slate-400 text-xs">{validationResult.competitiveAnalysis.marketGap}</p>
                  </div>
                  <div>
                    <h4 className="text-slate-300 font-medium mb-1 text-sm">Competitive Advantages</h4>
                    <ul className="text-slate-400 text-xs space-y-1">
                      {validationResult.competitiveAnalysis.competitiveAdvantage.map((advantage, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-green-400 mt-1">✓</span>
                          {advantage}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* SWOT Analysis & Recommendations */}
            <Card className="card-premium">
              <CardHeader className="pb-3">
                <CardTitle className="text-white flex items-center gap-2 text-lg">
                  <Target className="h-4 w-4 text-orange-400" />
                  Strategic Analysis & Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* SWOT Analysis */}
                  <div className="space-y-3">
                    <h3 className="text-base font-semibold text-white">SWOT Analysis</h3>

                    <div>
                      <h4 className="text-green-400 font-medium mb-1 flex items-center gap-2 text-sm">
                        <CheckCircle2 className="h-3 w-3" />
                        Strengths
                      </h4>
                      <ul className="text-slate-400 text-xs space-y-1">
                        {validationResult.recommendations.strengths.map((strength, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-green-400 mt-1">•</span>
                            {strength}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-red-400 font-medium mb-1 flex items-center gap-2 text-sm">
                        <AlertTriangle className="h-3 w-3" />
                        Weaknesses
                      </h4>
                      <ul className="text-slate-400 text-xs space-y-1">
                        {validationResult.recommendations.weaknesses.map((weakness, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-red-400 mt-1">•</span>
                            {weakness}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-blue-400 font-medium mb-1 flex items-center gap-2 text-sm">
                        <TrendingUp className="h-3 w-3" />
                        Opportunities
                      </h4>
                      <ul className="text-slate-400 text-xs space-y-1">
                        {validationResult.recommendations.opportunities.map((opportunity, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-blue-400 mt-1">•</span>
                            {opportunity}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-orange-400 font-medium mb-1 flex items-center gap-2 text-sm">
                        <Shield className="h-3 w-3" />
                        Threats
                      </h4>
                      <ul className="text-slate-400 text-xs space-y-1">
                        {validationResult.recommendations.threats.map((threat, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-orange-400 mt-1">•</span>
                            {threat}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Next Steps */}
                  <div className="space-y-3">
                    <h3 className="text-base font-semibold text-white">Recommended Next Steps</h3>

                    <div className="space-y-2">
                      {validationResult.recommendations.nextSteps.map((step, index) => (
                        <div key={index} className="flex items-start gap-2 p-2 bg-slate-800/50 rounded-lg">
                          <div className="w-5 h-5 bg-blue-500/20 rounded-full flex items-center justify-center text-blue-400 text-xs font-bold mt-0.5">
                            {index + 1}
                          </div>
                          <p className="text-slate-300 text-xs">{step}</p>
                        </div>
                      ))}
                    </div>

                    {validationResult.recommendations.pivotSuggestions && validationResult.recommendations.pivotSuggestions.length > 0 && (
                      <div>
                        <h4 className="text-yellow-400 font-medium mb-1 flex items-center gap-2 text-sm">
                          <Lightbulb className="h-3 w-3" />
                          Pivot Suggestions
                        </h4>
                        <ul className="text-slate-400 text-xs space-y-1">
                          {validationResult.recommendations.pivotSuggestions.map((suggestion, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-yellow-400 mt-1">💡</span>
                              {suggestion}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}


      </div>
    </DashboardShell>
  )
}
