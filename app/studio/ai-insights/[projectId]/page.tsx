"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Spark<PERSON>,
  TrendingUp,
  Users,
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  Plus,
  Edit,
  Save,
  X,
  Loader2
} from "lucide-react"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"

export default function ProjectOverviewPage() {
  const params = useParams()
  const projectId = params.projectId as string
  const [project, setProject] = useState<AIAnalysisResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [editForm, setEditForm] = useState({
    projectName: '',
    projectDescription: ''
  })

  useEffect(() => {
    const fetchProject = async () => {
      try {
        const projects = await getUserProjectsClient()
        const foundProject = projects.find(p => p.id === projectId)
        setProject(foundProject || null)

        // Initialize edit form with current project data
        if (foundProject) {
          setEditForm({
            projectName: foundProject.projectName || '',
            projectDescription: foundProject.projectDescription || ''
          })
        }
      } catch (err) {
        console.error('Error fetching project:', err)
      } finally {
        setLoading(false)
      }
    }

    if (projectId) {
      fetchProject()
    }
  }, [projectId])

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    // Reset form to original values
    if (project) {
      setEditForm({
        projectName: project.projectName || '',
        projectDescription: project.projectDescription || ''
      })
    }
  }

  const handleSave = async () => {
    if (!project || !editForm.projectName.trim() || !editForm.projectDescription.trim()) {
      alert('Project name and description are required')
      return
    }

    try {
      setIsSaving(true)

      // Update project via API
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editForm.projectName,
          description: editForm.projectDescription
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update project')
      }

      // Update local state
      setProject(prev => prev ? {
        ...prev,
        projectName: editForm.projectName,
        projectDescription: editForm.projectDescription
      } : null)

      setIsEditing(false)
      alert('Project updated successfully!')
    } catch (error) {
      console.error('Error updating project:', error)
      alert('Failed to update project. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-400">Project not found</p>
      </div>
    )
  }

  // Mock data for demonstration - in real app this would come from the database
  const mockMetrics = {
    developmentProgress: 65,
    completedTasks: 13,
    totalTasks: 20,
    inProgressTasks: 3,
    backlogTasks: 4,
    upcomingDeadlines: [
      { name: "Auth System", date: "Tomorrow", priority: "high" },
      { name: "User Flow Editor", date: "Next Week", priority: "medium" },
      { name: "Pricing Page", date: "2 Weeks", priority: "low" }
    ],
    recentActivity: [
      { action: 'Ticket "Auth System" moved to In Progress', time: '10 minutes ago' },
      { action: 'User Flow updated', time: '2 hours ago' },
      { action: 'New ticket created "API Endpoints"', time: 'Yesterday' }
    ],
    teamMembers: [
      { name: "John Doe", role: "Owner", initials: "JD" },
      { name: "Alice Smith", role: "Editor", initials: "AS" }
    ]
  }

  return (
    <div className="space-y-6">
      {/* Project Header with Edit Functionality */}
      <Card className="bg-slate-900 border-slate-800">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              {isEditing ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Project Name
                    </label>
                    <Input
                      value={editForm.projectName}
                      onChange={(e) => setEditForm(prev => ({ ...prev, projectName: e.target.value }))}
                      className="bg-slate-800 border-slate-700 text-white"
                      placeholder="Enter project name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Project Description
                    </label>
                    <Textarea
                      value={editForm.projectDescription}
                      onChange={(e) => setEditForm(prev => ({ ...prev, projectDescription: e.target.value }))}
                      className="bg-slate-800 border-slate-700 text-white min-h-[100px]"
                      placeholder="Enter project description"
                    />
                  </div>
                </div>
              ) : (
                <div>
                  <CardTitle className="text-white text-2xl mb-2">
                    {project?.projectName || 'Untitled Project'}
                  </CardTitle>
                  <CardDescription className="text-slate-300 text-base leading-relaxed">
                    {project?.projectDescription || 'No description available'}
                  </CardDescription>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2 ml-4">
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelEdit}
                    disabled={isSaving}
                    className="border-slate-600 text-slate-300 hover:bg-slate-800"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={isSaving}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isSaving ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    {isSaving ? 'Saving...' : 'Save'}
                  </Button>
                </>
              ) : (
                <Button
                  size="sm"
                  onClick={handleEdit}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Project
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Market Feasibility Card */}
        <Card className="bg-slate-900 border-slate-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-white flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-400" />
              Market Feasibility
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center mb-4">
              <div className="text-3xl font-bold text-white mb-2">
                {project.overallScore}/10
              </div>
              <Progress value={project.overallScore * 10} className="mb-4" />
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between text-slate-300">
                <span>Uniqueness:</span>
                <span className="text-white">{project.analysisData?.uniqueness || 8}/10</span>
              </div>
              <div className="flex justify-between text-slate-300">
                <span>Stickiness:</span>
                <span className="text-white">{project.analysisData?.stickiness || 7}/10</span>
              </div>
              <div className="flex justify-between text-slate-300">
                <span>Growth Trend:</span>
                <span className="text-white">{project.analysisData?.growthPotential || 8}/10</span>
              </div>
              <div className="flex justify-between text-slate-300">
                <span>Pricing:</span>
                <span className="text-white">{project.analysisData?.pricingModel || 9}/10</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Development Progress Card */}
        <Card className="bg-slate-900 border-slate-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-white flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-400" />
              Development Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center mb-4">
              <div className="text-3xl font-bold text-white mb-2">
                {mockMetrics.developmentProgress}%
              </div>
              <Progress value={mockMetrics.developmentProgress} className="mb-4" />
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between text-slate-300">
                <span>Completed:</span>
                <span className="text-green-400">{mockMetrics.completedTasks}/{mockMetrics.totalTasks}</span>
              </div>
              <div className="flex justify-between text-slate-300">
                <span>In Progress:</span>
                <span className="text-blue-400">{mockMetrics.inProgressTasks}/{mockMetrics.totalTasks}</span>
              </div>
              <div className="flex justify-between text-slate-300">
                <span>Backlog:</span>
                <span className="text-slate-400">{mockMetrics.backlogTasks}/{mockMetrics.totalTasks}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Deadlines Card */}
        <Card className="bg-slate-900 border-slate-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-white flex items-center gap-2">
              <Calendar className="h-5 w-5 text-orange-400" />
              Upcoming Deadlines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockMetrics.upcomingDeadlines.map((deadline, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-white text-sm font-medium">{deadline.name}</p>
                    <p className="text-slate-400 text-xs">{deadline.date}</p>
                  </div>
                  <Badge 
                    variant={deadline.priority === 'high' ? 'destructive' : 
                            deadline.priority === 'medium' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {deadline.priority}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card className="bg-slate-900 border-slate-800">
          <CardHeader>
            <CardTitle className="text-white">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockMetrics.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <p className="text-slate-300 text-sm">{activity.action}</p>
                    <p className="text-slate-500 text-xs">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Team Members */}
        <Card className="bg-slate-900 border-slate-800">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Users className="h-5 w-5" />
              Team Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockMetrics.teamMembers.map((member, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-slate-700 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {member.initials}
                  </div>
                  <div>
                    <p className="text-white text-sm font-medium">{member.name}</p>
                    <p className="text-slate-400 text-xs">{member.role}</p>
                  </div>
                </div>
              ))}
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full border-slate-600 text-slate-300 hover:bg-slate-800"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Member
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
