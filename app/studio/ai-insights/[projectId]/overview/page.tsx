"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  TrendingUp,
  TrendingDown,
  Minus,
  Loader2,
  RefreshCw,
  BarChart3,
  Target,
  Lightbulb,
  CheckCircle,
  Edit
} from "lucide-react"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"
import { generateOverviewWithGemini } from "@/lib/services/gemini-service"
import { saveOverviewData, getOverviewData } from "@/lib/services/supabase-service"
import { useRouter } from "next/navigation"

interface OverviewData {
  summary: string;
  keyMetrics: Array<{ label: string; value: string; trend: 'up' | 'down' | 'stable' }>;
  recommendations: string[];
  nextSteps: string[];
}

export default function OverviewPage() {
  const params = useParams()
  const router = useRouter()
  const projectId = params.projectId as string
  const [project, setProject] = useState<AIAnalysisResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [overviewData, setOverviewData] = useState<OverviewData | null>(null)
  const [generatingOverview, setGeneratingOverview] = useState(false)

  const generateOverview = async (projectData: AIAnalysisResult, forceRegenerate = false) => {
    try {
      setGeneratingOverview(true)

      // Check if overview is already in database (unless forcing regeneration)
      if (!forceRegenerate) {
        try {
          const { data: existingOverview } = await getOverviewData(projectId)
          if (existingOverview) {
            setOverviewData(existingOverview)
            return
          }
        } catch (error) {
          console.log('No existing overview found, generating new one')
        }
      }

      const generatedOverview = await generateOverviewWithGemini({
        name: projectData.projectName,
        description: projectData.projectDescription,
        features: projectData.coreFeatures || [],
        marketFeasibility: projectData.marketFeasibility,
        technicalRequirements: projectData.technicalRequirements
      })

      setOverviewData(generatedOverview)

      // Save to database
      try {
        await saveOverviewData(projectId, generatedOverview)
      } catch (error) {
        console.error('Error saving overview to database:', error)
      }
    } catch (error) {
      console.error('Error generating overview:', error)
      // Fallback overview
      const fallbackOverview: OverviewData = {
        summary: `${projectData.projectName} represents a promising SaaS opportunity with strong market potential. The project combines innovative features with proven technical approaches to deliver value to users. Based on the AI analysis, this project shows excellent feasibility across multiple dimensions including market demand, technical implementation, and business viability.`,
        keyMetrics: [
          { label: "Market Feasibility", value: `${projectData.marketFeasibility.overallScore}/100`, trend: "up" },
          { label: "Core Features", value: `${projectData.coreFeatures?.length || 0}`, trend: "stable" },
          { label: "Development Complexity", value: "Medium", trend: "stable" },
          { label: "Time to Market", value: "3-6 months", trend: "stable" }
        ],
        recommendations: [
          "Focus on MVP development with core features first",
          "Validate market demand through user research",
          "Build a strong technical foundation for scalability",
          "Develop a comprehensive go-to-market strategy"
        ],
        nextSteps: [
          "Define detailed technical specifications",
          "Create user personas and journey maps",
          "Set up development environment and CI/CD",
          "Begin MVP development with priority features"
        ]
      }
      setOverviewData(fallbackOverview)
    } finally {
      setGeneratingOverview(false)
    }
  }

  useEffect(() => {
    const fetchProject = async () => {
      try {
        const projects = await getUserProjectsClient()
        const foundProject = projects.find(p => p.id === projectId)
        setProject(foundProject || null)

        if (foundProject) {
          generateOverview(foundProject)
        }
      } catch (err) {
        console.error('Error fetching project:', err)
      } finally {
        setLoading(false)
      }
    }

    if (projectId) {
      fetchProject()
    }
  }, [projectId])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-400">Project not found</p>
      </div>
    )
  }

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down': return <TrendingDown className="h-4 w-4 text-red-500" />
      case 'stable': return <Minus className="h-4 w-4 text-slate-400" />
    }
  }

  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return 'text-green-500'
      case 'down': return 'text-red-500'
      case 'stable': return 'text-slate-400'
    }
  }

  return (
    <div className="space-y-6 h-full overflow-auto">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-white">{project.projectName} Overview</h1>
          <p className="text-slate-400 mt-1 text-sm">AI-generated project analysis and insights</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/studio/ai-insights/${projectId}`)}
            className="border-slate-600 text-slate-300 hover:bg-slate-800"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Project
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              if (project) {
                // Clear existing data and regenerate
                setOverviewData(null)
                generateOverview(project, true)
              }
            }}
            disabled={generatingOverview}
          >
            {generatingOverview ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Regenerate
          </Button>
        </div>
      </div>

      {generatingOverview ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
            <p className="text-slate-400">Generating AI-powered overview...</p>
          </div>
        </div>
      ) : overviewData ? (
        <>
          {/* Summary Section */}
          <Card className="bg-slate-900/50 border-slate-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2 text-lg">
                <BarChart3 className="h-4 w-4 text-blue-500" />
                Project Summary
              </CardTitle>
              <CardDescription className="text-slate-400 text-sm">
                AI-generated project analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-slate-300 leading-relaxed whitespace-pre-line text-sm">
                {overviewData.summary}
              </p>
            </CardContent>
          </Card>

          {/* Key Metrics */}
          <Card className="bg-slate-900/50 border-slate-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2 text-lg">
                <Target className="h-4 w-4 text-green-500" />
                Key Metrics
              </CardTitle>
              <CardDescription className="text-slate-400 text-sm">
                Project indicators and measurements
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {overviewData.keyMetrics.map((metric, index) => (
                  <div key={index} className="bg-slate-800/50 rounded-lg p-3 border border-slate-700/50">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-slate-400 text-xs">{metric.label}</span>
                      {getTrendIcon(metric.trend)}
                    </div>
                    <div className={`text-lg font-bold ${getTrendColor(metric.trend)}`}>
                      {metric.value}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommendations and Next Steps */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recommendations */}
            <Card className="bg-slate-900/50 border-slate-800">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  Recommendations
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Strategic suggestions for project success
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {overviewData.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-yellow-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-yellow-500 text-xs font-bold">{index + 1}</span>
                      </div>
                      <p className="text-slate-300 text-sm leading-relaxed">{recommendation}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Next Steps */}
            <Card className="bg-slate-900/50 border-slate-800">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-blue-500" />
                  Next Steps
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Immediate actionable items to move forward
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {overviewData.nextSteps.map((step, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <CheckCircle className="h-3 w-3 text-blue-500" />
                      </div>
                      <p className="text-slate-300 text-sm leading-relaxed">{step}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <p className="text-slate-400">Failed to generate overview. Please try again.</p>
        </div>
      )}
    </div>
  )
}
