"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Brain,
  Search,
  Filter,
  Lightbulb,
  TrendingUp,
  Users,
  Code,
  Loader2,
  RefreshCw,
  Calendar,
  Tag,
  Star
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"
import { generateMemoryBankWithGemini } from "@/lib/services/gemini-service"
import { saveMemoryBankData, getMemoryBankData } from "@/lib/services/supabase-service"

interface Insight {
  id: string;
  title: string;
  content: string;
  category: 'technical' | 'business' | 'user-experience' | 'market';
  importance: 'high' | 'medium' | 'low';
  tags: string[];
  createdAt: string;
}

interface MemoryBankData {
  insights: Insight[];
  summary: string;
}

export default function MemoryBankPage() {
  const params = useParams()
  const projectId = params.projectId as string
  const [project, setProject] = useState<AIAnalysisResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [memoryData, setMemoryData] = useState<MemoryBankData | null>(null)
  const [generatingMemory, setGeneratingMemory] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedImportance, setSelectedImportance] = useState<string>("all")

  const generateMemoryBank = async (projectData: AIAnalysisResult, forceRegenerate = false) => {
    try {
      setGeneratingMemory(true)

      // Check if memory bank is already in database (unless forcing regeneration)
      if (!forceRegenerate) {
        try {
          const { data: existingMemory } = await getMemoryBankData(projectId)
          if (existingMemory) {
            setMemoryData(existingMemory)
            return
          }
        } catch (error) {
          console.log('No existing memory bank found, generating new one')
        }
      }

      const generatedMemory = await generateMemoryBankWithGemini({
        name: projectData.projectName,
        description: projectData.projectDescription,
        features: projectData.coreFeatures || [],
        technicalRequirements: projectData.technicalRequirements
      })

      setMemoryData(generatedMemory)

      // Save to database
      try {
        await saveMemoryBankData(projectId, generatedMemory)
      } catch (error) {
        console.error('Error saving memory bank to database:', error)
      }
    } catch (error) {
      console.error('Error generating memory bank:', error)
      // Fallback memory bank
      const fallbackMemory: MemoryBankData = {
        insights: [
          {
            id: "tech-1",
            title: "Scalable Architecture Design",
            content: "Consider implementing a microservices architecture to ensure scalability as your user base grows. This will allow for independent scaling of different components and easier maintenance.",
            category: "technical",
            importance: "high",
            tags: ["architecture", "scalability", "microservices"],
            createdAt: new Date().toISOString()
          },
          {
            id: "business-1",
            title: "Freemium Model Strategy",
            content: "A freemium model with core features free and premium features paid could maximize user acquisition while ensuring revenue generation from power users.",
            category: "business",
            importance: "high",
            tags: ["monetization", "freemium", "strategy"],
            createdAt: new Date().toISOString()
          },
          {
            id: "ux-1",
            title: "Mobile-First Design",
            content: "Prioritize mobile user experience as most users will likely access the platform via mobile devices. Ensure responsive design and touch-friendly interfaces.",
            category: "user-experience",
            importance: "medium",
            tags: ["mobile", "responsive", "UX"],
            createdAt: new Date().toISOString()
          }
        ],
        summary: `This memory bank contains key insights and learnings for ${projectData.projectName}. These AI-generated insights cover technical architecture decisions, business strategy considerations, user experience best practices, and market positioning advice. Use these insights to guide your development decisions and strategic planning.`
      }
      setMemoryData(fallbackMemory)
    } finally {
      setGeneratingMemory(false)
    }
  }

  useEffect(() => {
    const fetchProject = async () => {
      try {
        const projects = await getUserProjectsClient()
        const foundProject = projects.find(p => p.id === projectId)
        setProject(foundProject || null)

        if (foundProject) {
          generateMemoryBank(foundProject)
        }
      } catch (err) {
        console.error('Error fetching project:', err)
      } finally {
        setLoading(false)
      }
    }

    if (projectId) {
      fetchProject()
    }
  }, [projectId])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-400">Project not found</p>
      </div>
    )
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'technical': return <Code className="h-4 w-4 text-blue-500" />
      case 'business': return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'user-experience': return <Users className="h-4 w-4 text-purple-500" />
      case 'market': return <Lightbulb className="h-4 w-4 text-yellow-500" />
      default: return <Brain className="h-4 w-4 text-slate-400" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'technical': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'business': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'user-experience': return 'bg-purple-500/20 text-purple-400 border-purple-500/30'
      case 'market': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default: return 'bg-slate-500/20 text-slate-400 border-slate-500/30'
    }
  }

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high': return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'medium': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'low': return 'bg-green-500/20 text-green-400 border-green-500/30'
      default: return 'bg-slate-500/20 text-slate-400 border-slate-500/30'
    }
  }

  // Filter insights
  const filteredInsights = memoryData?.insights.filter(insight => {
    const matchesSearch = insight.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         insight.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         insight.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "all" || insight.category === selectedCategory
    const matchesImportance = selectedImportance === "all" || insight.importance === selectedImportance
    return matchesSearch && matchesCategory && matchesImportance
  }) || []

  // Group insights by category
  const insightsByCategory = filteredInsights.reduce((acc, insight) => {
    if (!acc[insight.category]) {
      acc[insight.category] = []
    }
    acc[insight.category].push(insight)
    return acc
  }, {} as Record<string, Insight[]>)

  const categories = ['technical', 'business', 'user-experience', 'market']



  return (
    <div className="space-y-6 h-full overflow-auto">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">{project.projectName} Memory Bank</h1>
          <p className="text-slate-400 mt-1">AI-generated insights and knowledge repository for your project</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              if (project) {
                // Clear existing data and regenerate
                setMemoryData(null)
                generateMemoryBank(project, true)
              }
            }}
            disabled={generatingMemory}
          >
            {generatingMemory ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Regenerate
          </Button>
        </div>
      </div>

      {/* Summary */}
      {memoryData && (
        <Card className="bg-slate-900/50 border-slate-800">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-500" />
              Memory Bank Overview
            </CardTitle>
            <CardDescription className="text-slate-400">
              AI-curated knowledge and insights for your project
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-slate-300 leading-relaxed">{memoryData.summary}</p>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card className="bg-slate-900/50 border-slate-800">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search insights..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-800 border-slate-700 text-white"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-white text-sm"
              >
                <option value="all">All Categories</option>
                <option value="technical">Technical</option>
                <option value="business">Business</option>
                <option value="user-experience">User Experience</option>
                <option value="market">Market</option>
              </select>
              <select
                value={selectedImportance}
                onChange={(e) => setSelectedImportance(e.target.value)}
                className="px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-white text-sm"
              >
                <option value="all">All Importance</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {generatingMemory ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-500" />
            <p className="text-slate-400">Generating AI-powered insights...</p>
          </div>
        </div>
      ) : memoryData ? (
        <>
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="bg-slate-900/50 border-slate-800">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <Brain className="h-5 w-5 text-purple-500" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Total Insights</p>
                    <p className="text-2xl font-bold text-purple-500">{memoryData.insights.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-900/50 border-slate-800">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center">
                    <Star className="h-5 w-5 text-red-500" />
                  </div>
                  <div>
                    <p className="text-white font-medium">High Priority</p>
                    <p className="text-2xl font-bold text-red-500">
                      {memoryData.insights.filter(i => i.importance === 'high').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-900/50 border-slate-800">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Code className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Technical</p>
                    <p className="text-2xl font-bold text-blue-500">
                      {memoryData.insights.filter(i => i.category === 'technical').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-900/50 border-slate-800">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Business</p>
                    <p className="text-2xl font-bold text-green-500">
                      {memoryData.insights.filter(i => i.category === 'business').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Insights Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredInsights.map(insight => (
              <Card key={insight.id} className="bg-slate-900/50 border-slate-800 hover:border-slate-700 transition-colors">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(insight.category)}
                      <CardTitle className="text-white text-lg">{insight.title}</CardTitle>
                    </div>
                    <Badge className={`text-xs ${getImportanceColor(insight.importance)}`}>
                      {insight.importance}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-300 leading-relaxed mb-4">{insight.content}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex gap-2">
                      <Badge className={`text-xs ${getCategoryColor(insight.category)}`}>
                        {insight.category.replace('-', ' ')}
                      </Badge>
                      {insight.tags.slice(0, 2).map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex items-center gap-1 text-slate-400 text-xs">
                      <Calendar className="h-3 w-3" />
                      {new Date(insight.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <p className="text-slate-400">Failed to generate memory bank. Please try again.</p>
        </div>
      )}

    </div>
  )
}
