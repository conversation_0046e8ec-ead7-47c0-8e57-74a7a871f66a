"use client"

import { useEffect, useState } from "react"
import { usePara<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Search,
  Clock,
  CheckCircle2,
  Circle,
  Loader2,
  RefreshCw,
  Tag,
  ArrowRight
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"
import { generateTasksWithGemini, generateTasksExplanationWithGemini } from "@/lib/services/gemini-service"
import {
  getProjectTasksFromDB,
  saveTasksToDB,
  updateTaskStatus as updateTaskStatusDB,
  Task as DBTask,
  saveProjectTasks,
  getProjectTasks
} from "@/lib/services/supabase-service"

interface Task {
  id: string;
  title: string;
  description: string;
  status: "todo" | "in_progress" | "done";
  priority: "high" | "medium" | "low";
  category: string;
  estimatedHours?: number;
  dependencies?: string[];
}

export default function TicketsPage() {
  const params = useParams()
  const projectId = params.projectId as string
  const [project, setProject] = useState<AIAnalysisResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [tasks, setTasks] = useState<Task[]>([])
  const [generatingTasks, setGeneratingTasks] = useState(false)
  const [aboutTasksExplanation, setAboutTasksExplanation] = useState<string | null>(null)
  const [generatingExplanation, setGeneratingExplanation] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")

  // Function to update task status
  const updateTaskStatus = async (taskId: string, newStatus: "todo" | "in_progress" | "done") => {
    try {
      // Update in database
      await updateTaskStatusDB(taskId, newStatus)

      // Update local state
      const updatedTasks = tasks.map(task =>
        task.id === taskId ? { ...task, status: newStatus } : task
      )
      setTasks(updatedTasks)
    } catch (error) {
      console.error('Error updating task status:', error)
    }
  }

  // Function to handle task click for status progression
  const handleTaskClick = (task: Task) => {
    let newStatus: "todo" | "in_progress" | "done"

    switch (task.status) {
      case "todo":
        newStatus = "in_progress"
        break
      case "in_progress":
        newStatus = "done"
        break
      case "done":
        newStatus = "todo" // Reset to todo if clicked again
        break
      default:
        newStatus = "todo"
    }

    updateTaskStatus(task.id, newStatus)
  }

  const generateTasks = async (projectData: AIAnalysisResult, forceRegenerate = false) => {
    try {
      setGeneratingTasks(true)

      // Check if tasks are already in database (unless forcing regeneration)
      if (!forceRegenerate) {
        try {
          const existingTasks = await getProjectTasksFromDB(projectId)
          if (existingTasks.length > 0) {
            setTasks(existingTasks)
            return existingTasks
          }
        } catch (error) {
          console.log('No existing tasks found, generating new ones')
        }
      }

      // If forcing regeneration, we'll just generate new tasks
      // The database will handle the new tasks appropriately

      const generatedTasks = await generateTasksWithGemini({
        name: projectData.projectName,
        description: projectData.projectDescription,
        features: projectData.coreFeatures || [],
        technicalRequirements: projectData.technicalRequirements
      })

      // Save to database
      const savedTasks = await saveTasksToDB(projectId, generatedTasks.map(task => ({
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        category: task.category,
        estimated_hours: task.estimatedHours,
        dependencies: task.dependencies
      })))

      setTasks(savedTasks)
      return savedTasks
    } catch (error) {
      console.error('Error generating tasks:', error)
      // Fallback tasks
      const fallbackTasks: Task[] = [
        {
          id: "setup-1",
          title: "Project Setup and Planning",
          description: "Initialize project structure, set up development environment, and define project requirements",
          status: "done",
          priority: "high",
          category: "Setup",
          estimatedHours: 8
        },
        {
          id: "design-1",
          title: "UI/UX Design System",
          description: "Create comprehensive design system including components, colors, typography, and layouts",
          status: "in_progress",
          priority: "high",
          category: "Design",
          estimatedHours: 16
        },
        {
          id: "frontend-1",
          title: "Frontend Development",
          description: "Implement core frontend features and user interface components",
          status: "todo",
          priority: "high",
          category: "Frontend",
          estimatedHours: 40,
          dependencies: ["design-1"]
        }
      ]
      setTasks(fallbackTasks)
      return fallbackTasks
    } finally {
      setGeneratingTasks(false)
    }
  }

  const generateAboutTasksExplanation = async (projectData: AIAnalysisResult, tasksData: Task[], forceRegenerate = false) => {
    try {
      setGeneratingExplanation(true)

      // Check if explanation is already in database (unless forcing regeneration)
      if (!forceRegenerate) {
        try {
          const { explanation } = await getProjectTasks(projectData.projectName)
          if (explanation) {
            setAboutTasksExplanation(explanation)
            return
          }
        } catch (error) {
          console.log('No existing explanation found, generating new one')
        }
      }

      const explanation = await generateTasksExplanationWithGemini({
        name: projectData.projectName,
        description: projectData.projectDescription,
        features: projectData.coreFeatures || [],
        technicalRequirements: projectData.technicalRequirements,
        generatedTasks: tasksData
      })

      setAboutTasksExplanation(explanation)

      // Save explanation to database
      try {
        await saveProjectTasks(projectData.projectName, tasksData, explanation)
      } catch (error) {
        console.error('Error saving explanation to database:', error)
      }
    } catch (error) {
      console.error('Error generating about tasks explanation:', error)
      setAboutTasksExplanation(`These ${tasksData.length} tasks have been intelligently generated by AI based on a comprehensive analysis of ${projectData.projectName}. The AI considered your project's core features, technical requirements, and development best practices to create a realistic roadmap. Tasks are organized across multiple categories with priority-based scheduling to optimize your development workflow.`)
    } finally {
      setGeneratingExplanation(false)
    }
  }

  useEffect(() => {
    const fetchProject = async () => {
      try {
        const projects = await getUserProjectsClient()
        const foundProject = projects.find(p => p.id === projectId)
        setProject(foundProject || null)

        if (foundProject) {
          const generatedTasks = await generateTasks(foundProject)
          generateAboutTasksExplanation(foundProject, generatedTasks)
        }
      } catch (err) {
        console.error('Error fetching project:', err)
      } finally {
        setLoading(false)
      }
    }

    if (projectId) {
      fetchProject()
    }
  }, [projectId])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-400">Project not found</p>
      </div>
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'done': return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case 'in_progress': return <Clock className="h-4 w-4 text-blue-500" />
      case 'todo': return <Circle className="h-4 w-4 text-slate-400" />
      default: return <Circle className="h-4 w-4 text-slate-400" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'medium': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'low': return 'bg-green-500/20 text-green-400 border-green-500/30'
      default: return 'bg-slate-500/20 text-slate-400 border-slate-500/30'
    }
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      'Setup': 'bg-purple-500/20 text-purple-400',
      'Design': 'bg-pink-500/20 text-pink-400',
      'Frontend': 'bg-blue-500/20 text-blue-400',
      'Backend': 'bg-green-500/20 text-green-400',
      'Testing': 'bg-orange-500/20 text-orange-400',
      'DevOps': 'bg-cyan-500/20 text-cyan-400',
      'Documentation': 'bg-indigo-500/20 text-indigo-400'
    }
    return colors[category as keyof typeof colors] || 'bg-slate-500/20 text-slate-400'
  }

  // Filter tasks
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || task.category === selectedCategory
    const matchesStatus = selectedStatus === "all" || task.status === selectedStatus
    return matchesSearch && matchesCategory && matchesStatus
  })

  // Group tasks by status
  const tasksByStatus = {
    todo: filteredTasks.filter(task => task.status === 'todo'),
    in_progress: filteredTasks.filter(task => task.status === 'in_progress'),
    done: filteredTasks.filter(task => task.status === 'done')
  }

  const categories = Array.from(new Set(tasks.map(task => task.category)))

  return (
    <div className="space-y-6 h-full overflow-auto">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">{project.projectName} Tickets Board</h1>
          <p className="text-slate-400 mt-1">AI-generated development tasks and project management</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              if (project) {
                // Clear existing tasks and regenerate
                setTasks([])
                setAboutTasksExplanation(null)
                generateTasks(project, true).then(tasks => {
                  generateAboutTasksExplanation(project, tasks, true)
                })
              }
            }}
            disabled={generatingTasks}
          >
            {generatingTasks ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Regenerate
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Task
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="card-premium">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search tasks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-800 border-slate-700 text-white"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-white text-sm"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-white text-sm"
              >
                <option value="all">All Status</option>
                <option value="todo">To Do</option>
                <option value="in_progress">In Progress</option>
                <option value="done">Done</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {generatingTasks ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
            <p className="text-slate-400">Generating AI-powered tasks...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Task Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="card-premium">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Tag className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Total Tasks</p>
                    <p className="text-2xl font-bold text-blue-500">{tasks.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-premium">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-slate-500/20 rounded-lg flex items-center justify-center">
                    <Circle className="h-5 w-5 text-slate-400" />
                  </div>
                  <div>
                    <p className="text-white font-medium">To Do</p>
                    <p className="text-2xl font-bold text-slate-400">{tasksByStatus.todo.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-premium">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Clock className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-white font-medium">In Progress</p>
                    <p className="text-2xl font-bold text-blue-500">{tasksByStatus.in_progress.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-premium">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Completed</p>
                    <p className="text-2xl font-bold text-green-500">{tasksByStatus.done.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <Card className="card-premium border-blue-500/30">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <Circle className="h-4 w-4 text-blue-400" />
                </div>
                <div className="flex-1">
                  <p className="text-blue-300 text-sm font-medium">Interactive Task Management</p>
                  <p className="text-blue-200/70 text-xs">Click on any task to progress it: To Do → In Progress → Done → To Do</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Kanban Board */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* To Do Column */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <div className="h-5 w-5 rounded-full bg-blue-400/20 border-2 border-blue-400 pulse-glow"></div>
                  To Do ({tasksByStatus.todo.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 min-h-[400px]">
                {tasksByStatus.todo.map(task => (
                  <div
                    key={task.id}
                    className="glass-card rounded-lg p-4 hover:border-blue-500/50 task-transition cursor-pointer group hover:glow-blue"
                    onClick={() => handleTaskClick(task)}
                    title="Click to move to In Progress"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="text-white font-medium text-sm leading-tight group-hover:text-blue-300 transition-colors">{task.title}</h4>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(task.status)}
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                          <ArrowRight className="h-3 w-3 text-blue-400" />
                        </div>
                      </div>
                    </div>
                    <p className="text-slate-400 text-xs mb-3 leading-relaxed">{task.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex gap-2">
                        <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </Badge>
                        <Badge className={`text-xs ${getCategoryColor(task.category)}`}>
                          {task.category}
                        </Badge>
                      </div>
                      {task.estimatedHours && (
                        <div className="flex items-center gap-1 text-slate-400">
                          <Clock className="h-3 w-3" />
                          <span className="text-xs">{task.estimatedHours}h</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                {tasksByStatus.todo.length === 0 && (
                  <div className="flex items-center justify-center h-32 border-2 border-dashed border-slate-600 rounded-lg">
                    <p className="text-slate-400 text-sm">No tasks in To Do</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* In Progress Column */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <div className="h-5 w-5 rounded-full bg-amber-400/20 border-2 border-amber-400 pulse-glow flex items-center justify-center">
                    <div className="h-2 w-2 rounded-full bg-amber-400 animate-pulse"></div>
                  </div>
                  In Progress ({tasksByStatus.in_progress.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 min-h-[400px]">
                {tasksByStatus.in_progress.map(task => (
                  <div
                    key={task.id}
                    className="glass-card rounded-lg p-4 hover:border-green-500/50 task-transition cursor-pointer group hover:glow-emerald"
                    onClick={() => handleTaskClick(task)}
                    title="Click to mark as Done"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="text-white font-medium text-sm leading-tight group-hover:text-green-300 transition-colors">{task.title}</h4>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(task.status)}
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                          <CheckCircle2 className="h-3 w-3 text-green-400" />
                        </div>
                      </div>
                    </div>
                    <p className="text-slate-400 text-xs mb-3 leading-relaxed">{task.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex gap-2">
                        <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </Badge>
                        <Badge className={`text-xs ${getCategoryColor(task.category)}`}>
                          {task.category}
                        </Badge>
                      </div>
                      {task.estimatedHours && (
                        <div className="flex items-center gap-1 text-slate-400">
                          <Clock className="h-3 w-3" />
                          <span className="text-xs">{task.estimatedHours}h</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                {tasksByStatus.in_progress.length === 0 && (
                  <div className="flex items-center justify-center h-32 border-2 border-dashed border-slate-600 rounded-lg">
                    <p className="text-slate-400 text-sm">No tasks in progress</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Done Column */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <div className="h-5 w-5 rounded-full bg-emerald-400/20 border-2 border-emerald-400 pulse-glow flex items-center justify-center">
                    <CheckCircle2 className="h-3 w-3 text-emerald-400" />
                  </div>
                  Done ({tasksByStatus.done.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 min-h-[400px]">
                {tasksByStatus.done.map(task => (
                  <div
                    key={task.id}
                    className="glass-card rounded-lg p-4 hover:border-slate-500/50 task-transition cursor-pointer group opacity-90 hover:opacity-100"
                    onClick={() => handleTaskClick(task)}
                    title="Click to reset to To Do"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="text-white font-medium text-sm leading-tight group-hover:text-slate-300 transition-colors line-through decoration-green-500">{task.title}</h4>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(task.status)}
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                          <Circle className="h-3 w-3 text-slate-400" />
                        </div>
                      </div>
                    </div>
                    <p className="text-slate-400 text-xs mb-3 leading-relaxed">{task.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex gap-2">
                        <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </Badge>
                        <Badge className={`text-xs ${getCategoryColor(task.category)}`}>
                          {task.category}
                        </Badge>
                      </div>
                      {task.estimatedHours && (
                        <div className="flex items-center gap-1 text-slate-400">
                          <Clock className="h-3 w-3" />
                          <span className="text-xs">{task.estimatedHours}h</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                {tasksByStatus.done.length === 0 && (
                  <div className="flex items-center justify-center h-32 border-2 border-dashed border-slate-600 rounded-lg">
                    <p className="text-slate-400 text-sm">No completed tasks</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </>
      )}

      {/* About Tasks Section */}
      <Card className="card-premium mt-8">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <span className="text-2xl">🤖</span>
            About Tasks
          </CardTitle>
          <CardDescription className="text-slate-400">
            AI-generated insights about your development task breakdown
          </CardDescription>
        </CardHeader>
        <CardContent>
          {generatingExplanation ? (
            <div className="flex items-center gap-3 text-slate-400">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Generating AI-powered explanation...</span>
            </div>
          ) : aboutTasksExplanation ? (
            <div className="prose prose-slate prose-invert max-w-none">
              <p className="text-slate-300 leading-relaxed whitespace-pre-line">{aboutTasksExplanation}</p>
            </div>
          ) : (
            <p className="text-slate-400">
              These tasks have been intelligently generated by AI based on your project requirements and best practices.
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
