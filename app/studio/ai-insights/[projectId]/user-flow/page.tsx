"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Download,
  Plus,
  Edit,
  Trash2,
  ArrowRight,
  Circle,
  Square,
  Diamond,
  ExternalLink,
  Loader2
} from "lucide-react"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"
import { generateUserFlowWithGemini, generateFlowExplanationWithGemini } from "@/lib/services/gemini-service"

interface FlowStep {
  id: string;
  title: string;
  icon: string;
  points: string[];
  position: { x: number; y: number };
  connections: string[];
}

export default function UserFlowPage() {
  const params = useParams()
  const projectId = params.projectId as string
  const [project, setProject] = useState<AIAnalysisResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [flowSteps, setFlowSteps] = useState<FlowStep[]>([])
  const [generatingFlow, setGeneratingFlow] = useState(false)
  const [aboutFlowExplanation, setAboutFlowExplanation] = useState<string | null>(null)
  const [generatingExplanation, setGeneratingExplanation] = useState(false)

  const generateUserFlow = async (projectData: AIAnalysisResult) => {
    try {
      setGeneratingFlow(true)

      // Check if flow is already cached
      const cachedFlowKey = `user-flow-${projectId}`
      const cachedFlow = localStorage.getItem(cachedFlowKey)
      if (cachedFlow) {
        const parsedFlow = JSON.parse(cachedFlow)
        setFlowSteps(adjustFlowPositions(parsedFlow))
        return parsedFlow
      }

      const generatedFlow = await generateUserFlowWithGemini({
        name: projectData.projectName,
        description: projectData.projectDescription,
        features: projectData.coreFeatures || []
      })

      const adjustedFlow = adjustFlowPositions(generatedFlow)
      setFlowSteps(adjustedFlow)
      // Cache the flow
      localStorage.setItem(cachedFlowKey, JSON.stringify(adjustedFlow))
      return adjustedFlow
    } catch (error) {
      console.error('Error generating user flow:', error)
      // Fallback to default flow with better spacing
      const fallbackFlow = [
        { id: "landing", title: "LANDING PAGE", icon: "🏠", points: ["Welcome message", "Key features overview", "Call-to-action buttons", "Social proof"], position: { x: 50, y: 50 }, connections: ["auth"] },
        { id: "auth", title: "USER AUTHENTICATION", icon: "🔐", points: ["Secure login form", "Registration process", "Password recovery", "Social login options"], position: { x: 450, y: 50 }, connections: ["dashboard"] },
        { id: "dashboard", title: "MAIN DASHBOARD", icon: "📊", points: ["Overview metrics", "Quick actions", "Recent activity", "Navigation menu"], position: { x: 850, y: 50 }, connections: ["features"] }
      ]
      setFlowSteps(fallbackFlow)
      return fallbackFlow
    } finally {
      setGeneratingFlow(false)
    }
  }

  // Function to adjust flow positions for intelligent layout
  const adjustFlowPositions = (flow: FlowStep[]) => {
    const cardWidth = 320
    const cardHeight = 240 // Estimated card height for this component
    const horizontalSpacing = 180 // Increased spacing to prevent connection overlap
    const verticalSpacing = 160 // Increased vertical spacing for better connection routing
    const topMargin = 100 // Increased top margin for connection space above cards
    const leftMargin = 60

    // Determine optimal layout based on number of steps
    let cardsPerRow: number
    if (flow.length <= 4) {
      cardsPerRow = flow.length // Single row for 4 or fewer cards
    } else if (flow.length <= 8) {
      cardsPerRow = Math.ceil(flow.length / 2) // 2 rows for 5-8 cards
    } else {
      cardsPerRow = Math.ceil(flow.length / 3) // 3 rows for more cards
    }

    return flow.map((step, index) => {
      const row = Math.floor(index / cardsPerRow)
      const col = index % cardsPerRow

      return {
        ...step,
        position: {
          x: leftMargin + col * (cardWidth + horizontalSpacing),
          y: topMargin + row * (cardHeight + verticalSpacing)
        }
      }
    })
  }

  const generateAboutFlowExplanation = async (projectData: AIAnalysisResult, flowData: FlowStep[]) => {
    try {
      setGeneratingExplanation(true)

      // Check if explanation is already cached
      const cachedKey = `about-flow-explanation-${projectId}`
      const cached = localStorage.getItem(cachedKey)
      if (cached) {
        setAboutFlowExplanation(cached)
        return
      }

      const explanation = await generateFlowExplanationWithGemini({
        name: projectData.projectName,
        description: projectData.projectDescription,
        features: projectData.coreFeatures || [],
        flowSteps: flowData
      })

      setAboutFlowExplanation(explanation)
      // Cache the explanation
      localStorage.setItem(cachedKey, explanation)
    } catch (error) {
      console.error('Error generating about flow explanation:', error)
      setAboutFlowExplanation('This user flow diagram represents the core user journey through your application. Each node shows a key step in the user experience, helping you visualize how users will navigate and interact with your product features.')
    } finally {
      setGeneratingExplanation(false)
    }
  }

  useEffect(() => {
    const fetchProject = async () => {
      try {
        const projects = await getUserProjectsClient()
        const foundProject = projects.find(p => p.id === projectId)
        setProject(foundProject || null)

        // Generate user flow and explanation if project found
        if (foundProject) {
          const generatedFlow = await generateUserFlow(foundProject)
          generateAboutFlowExplanation(foundProject, generatedFlow)
        }
      } catch (err) {
        console.error('Error fetching project:', err)
      } finally {
        setLoading(false)
      }
    }

    if (projectId) {
      fetchProject()
    }
  }, [projectId])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-400">Project not found</p>
      </div>
    )
  }

  const getStatusColor = (stepId: string) => {
    // Simple logic: first step is active, others are pending
    const stepIndex = flowSteps.findIndex(step => step.id === stepId)
    if (stepIndex === 0) return 'bg-green-500'
    if (stepIndex === 1) return 'bg-blue-500'
    return 'bg-slate-500'
  }

  const getStatusText = (stepId: string) => {
    const stepIndex = flowSteps.findIndex(step => step.id === stepId)
    if (stepIndex === 0) return 'Active'
    if (stepIndex === 1) return 'Active'
    return 'Pending'
  }

  return (
    <div className="flex flex-col space-y-8">
      {/* Premium Header */}
      <div className="bg-slate-900/40 backdrop-blur-xl border border-slate-700/30 rounded-2xl p-8 glow-blue">
        <div className="flex justify-between items-center">
          <div className="space-y-3">
            <h1 className="text-4xl font-bold text-white tracking-tight">
              {project.projectName} User Flow
            </h1>
            <p className="text-slate-300 text-lg leading-relaxed">
              Visual representation of the user journey through your application
            </p>
          </div>
          <div className="flex gap-4">
            <Button
              variant="outline"
              size="lg"
              onClick={() => {
                if (project) {
                  generateUserFlow(project).then(flow => {
                    generateAboutFlowExplanation(project, flow)
                  })
                }
              }}
              disabled={generatingFlow}
              className="bg-slate-800/50 hover:bg-slate-700/70 text-slate-300 border-slate-600/50 hover:border-slate-500/50 px-6 py-3 rounded-xl transition-all duration-200 hover:scale-105"
            >
              {generatingFlow ? (
                <Loader2 className="h-5 w-5 mr-3 animate-spin" />
              ) : (
                <Edit className="h-5 w-5 mr-3" />
              )}
              Regenerate
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="bg-slate-800/50 hover:bg-slate-700/70 text-slate-300 border-slate-600/50 hover:border-slate-500/50 px-6 py-3 rounded-xl transition-all duration-200 hover:scale-105"
            >
              <Download className="h-5 w-5 mr-3" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Premium Flow Canvas */}
      <div className="bg-slate-900/40 backdrop-blur-xl border border-slate-700/30 rounded-2xl overflow-hidden glow-blue">
        <div className="p-8">
          {generatingFlow ? (
            <div className="flex items-center justify-center h-96 text-slate-300">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto glow-blue">
                  <Loader2 className="h-8 w-8 animate-spin text-white" />
                </div>
                <p className="text-lg font-medium">Generating AI-powered user flow...</p>
                <p className="text-sm text-slate-400">This may take a few moments</p>
              </div>
            </div>
          ) : (
            <div className="relative bg-slate-950/50 rounded-lg border border-slate-700/50 overflow-auto" style={{ minHeight: '600px' }}>
              {/* Grid Background */}
              <div
                className="absolute inset-0 opacity-10 pointer-events-none"
                style={{
                  backgroundImage: `
                    linear-gradient(rgba(148, 163, 184, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(148, 163, 184, 0.1) 1px, transparent 1px)
                  `,
                  backgroundSize: '40px 40px'
                }}
              />

              {/* Flow Container with intelligent sizing */}
              <div
                className="relative p-6"
                style={{
                  width: `${(() => {
                    const cardWidth = 320
                    const horizontalSpacing = 180
                    const leftMargin = 60

                    // Calculate based on intelligent layout
                    let cardsPerRow: number
                    if (flowSteps.length <= 4) {
                      cardsPerRow = flowSteps.length
                    } else if (flowSteps.length <= 8) {
                      cardsPerRow = Math.ceil(flowSteps.length / 2)
                    } else {
                      cardsPerRow = Math.ceil(flowSteps.length / 3)
                    }

                    return Math.max(800, leftMargin * 2 + cardsPerRow * (cardWidth + horizontalSpacing))
                  })()}px`,
                  height: `${(() => {
                    const cardHeight = 240
                    const verticalSpacing = 160
                    const topMargin = 100
                    const bottomMargin = 60

                    // Calculate rows needed
                    let cardsPerRow: number
                    if (flowSteps.length <= 4) {
                      cardsPerRow = flowSteps.length
                    } else if (flowSteps.length <= 8) {
                      cardsPerRow = Math.ceil(flowSteps.length / 2)
                    } else {
                      cardsPerRow = Math.ceil(flowSteps.length / 3)
                    }

                    const rows = Math.ceil(flowSteps.length / cardsPerRow)
                    return topMargin + bottomMargin + rows * cardHeight + (rows - 1) * verticalSpacing
                  })()}px`
                }}
              >
                {/* Flow Steps */}
                {flowSteps.map((step, index) => (
                  <div
                    key={step.id}
                    className="absolute group cursor-pointer"
                    style={{
                      left: step.position.x,
                      top: step.position.y,
                      transform: 'translate(0, 0)'
                    }}
                  >
                    {/* Premium Step Card */}
                    <div className="group relative">
                      {/* Premium glow effect on hover */}
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-0 group-hover:opacity-30 transition duration-500"></div>

                      <div className="relative w-80 bg-slate-900/60 backdrop-blur-xl border border-slate-700/30 hover:border-blue-500/50 rounded-2xl p-6 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105">
                        {/* Header */}
                        <div className="flex items-center justify-between mb-6">
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center text-2xl border border-blue-500/30">
                              {step.icon}
                            </div>
                            <Badge variant="secondary" className="bg-slate-800/50 text-slate-300 border-slate-600/50 px-3 py-1 rounded-lg">
                              <Circle className={`h-3 w-3 mr-2 ${getStatusColor(step.id)}`} />
                              {getStatusText(step.id)}
                            </Badge>
                          </div>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-all hover:bg-slate-700/50 rounded-lg">
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Title */}
                        <h3 className="text-white font-bold text-lg mb-6 leading-tight group-hover:text-blue-300 transition-colors">
                          {step.title}
                        </h3>

                        {/* Points */}
                        <div className="space-y-4">
                          {step.points.map((point, pointIndex) => (
                            <div key={pointIndex} className="flex items-start gap-4">
                              <span className="text-blue-400 text-sm mt-1 font-bold bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-xl w-6 h-6 flex items-center justify-center text-xs border border-blue-500/30 glow-blue">
                                {pointIndex + 1}
                              </span>
                              <span className="text-slate-300 text-sm leading-relaxed flex-1">
                                {point}
                              </span>
                            </div>
                          ))}
                        </div>

                        {/* Connection indicator */}
                        {step.connections.length > 0 && (
                          <div className="mt-6 pt-4 border-t border-slate-700/30">
                            <div className="flex items-center gap-3 text-slate-400">
                              <span className="text-sm font-medium">{step.connections.length} connection{step.connections.length > 1 ? 's' : ''}</span>
                              <ArrowRight className="h-4 w-4 text-blue-400" />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {/* Connection Lines */}
                <svg className="absolute inset-0 pointer-events-none w-full h-full">
                  {flowSteps.map((step, index) =>
                    step.connections.map(connectionId => {
                      const targetStep = flowSteps.find(s => s.id === connectionId)
                      if (!targetStep) return null

                      const cardWidth = 320
                      const cardHeight = 240

                      // Calculate connection points - using top/bottom edges to avoid card overlap
                      const startX = step.position.x + cardWidth // Right edge of source card
                      const endX = targetStep.position.x // Left edge of target card

                      // Determine if we should route above or below cards
                      const sourceRow = Math.floor(flowSteps.findIndex(s => s.id === step.id) / (() => {
                        if (flowSteps.length <= 4) return flowSteps.length
                        if (flowSteps.length <= 8) return Math.ceil(flowSteps.length / 2)
                        return Math.ceil(flowSteps.length / 3)
                      })())

                      const targetRow = Math.floor(flowSteps.findIndex(s => s.id === connectionId) / (() => {
                        if (flowSteps.length <= 4) return flowSteps.length
                        if (flowSteps.length <= 8) return Math.ceil(flowSteps.length / 2)
                        return Math.ceil(flowSteps.length / 3)
                      })())

                      let pathData: string

                      if (sourceRow === targetRow) {
                        // Same row - route above cards to avoid overlap
                        const routeY = step.position.y - 30 // Route above cards
                        const startY = step.position.y + cardHeight / 2 // Middle of source card
                        const endY = targetStep.position.y + cardHeight / 2 // Middle of target card

                        pathData = `M ${startX} ${startY} L ${startX + 20} ${startY} L ${startX + 20} ${routeY} L ${endX - 20} ${routeY} L ${endX - 20} ${endY} L ${endX} ${endY}`
                      } else {
                        // Different rows - create right-angled path that avoids cards
                        const startY = step.position.y + cardHeight / 2 // Middle of source card
                        const endY = targetStep.position.y + cardHeight / 2 // Middle of target card

                        // Route around cards with sufficient clearance
                        const midX = startX + (endX - startX) / 2
                        const clearanceOffset = 40 // Extra space to avoid card overlap

                        if (startY < endY) {
                          // Going down - route below source card and above target card
                          const routeY1 = step.position.y + cardHeight + clearanceOffset
                          const routeY2 = targetStep.position.y - clearanceOffset
                          pathData = `M ${startX} ${startY} L ${startX + 20} ${startY} L ${startX + 20} ${routeY1} L ${midX} ${routeY1} L ${midX} ${routeY2} L ${endX - 20} ${routeY2} L ${endX - 20} ${endY} L ${endX} ${endY}`
                        } else {
                          // Going up - route above source card and below target card
                          const routeY1 = step.position.y - clearanceOffset
                          const routeY2 = targetStep.position.y + cardHeight + clearanceOffset
                          pathData = `M ${startX} ${startY} L ${startX + 20} ${startY} L ${startX + 20} ${routeY1} L ${midX} ${routeY1} L ${midX} ${routeY2} L ${endX - 20} ${routeY2} L ${endX - 20} ${endY} L ${endX} ${endY}`
                        }
                      }

                      return (
                        <g key={`${step.id}-${connectionId}`}>
                          {/* Main connection line */}
                          <path
                            d={pathData}
                            stroke="#3b82f6"
                            strokeWidth="3"
                            fill="none"
                            opacity="0.9"
                            markerEnd="url(#arrowhead)"
                            strokeDasharray="8,4"
                          />
                          {/* Glow effect */}
                          <path
                            d={pathData}
                            stroke="#60a5fa"
                            strokeWidth="1"
                            fill="none"
                            opacity="0.7"
                            strokeDasharray="8,4"
                          />
                        </g>
                      )
                    })
                  )}

                  {/* Arrow marker definition */}
                  <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="10" refX="9" refY="5" orient="auto">
                      <polygon points="0 2, 8 5, 0 8" fill="#3b82f6" />
                    </marker>
                  </defs>
                </svg>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Premium Flow Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 flex-shrink-0">
        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-blue-400 rounded-2xl blur opacity-0 group-hover:opacity-30 transition duration-500"></div>
          <div className="relative bg-slate-900/40 backdrop-blur-xl border border-slate-700/30 hover:border-blue-500/50 rounded-2xl p-6 transition-all duration-300 hover:scale-105 glow-blue">
            <div className="flex items-center gap-4">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500/30 to-blue-600/30 rounded-xl flex items-center justify-center border border-blue-500/30 glow-blue">
                <Circle className="h-7 w-7 text-blue-400" />
              </div>
              <div>
                <p className="text-white font-semibold text-lg">Total Steps</p>
                <p className="text-3xl font-bold text-blue-400">{flowSteps.length}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-600 to-emerald-400 rounded-2xl blur opacity-0 group-hover:opacity-30 transition duration-500"></div>
          <div className="relative bg-slate-900/40 backdrop-blur-xl border border-slate-700/30 hover:border-emerald-500/50 rounded-2xl p-6 transition-all duration-300 hover:scale-105 glow-emerald">
            <div className="flex items-center gap-4">
              <div className="w-14 h-14 bg-gradient-to-br from-emerald-500/30 to-emerald-600/30 rounded-xl flex items-center justify-center border border-emerald-500/30 glow-emerald">
                <ArrowRight className="h-7 w-7 text-emerald-400" />
              </div>
              <div>
                <p className="text-white font-semibold text-lg">Connections</p>
                <p className="text-3xl font-bold text-emerald-400">
                  {flowSteps.reduce((total, step) => total + step.connections.length, 0)}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-purple-400 rounded-2xl blur opacity-0 group-hover:opacity-30 transition duration-500"></div>
          <div className="relative bg-slate-900/40 backdrop-blur-xl border border-slate-700/30 hover:border-purple-500/50 rounded-2xl p-6 transition-all duration-300 hover:scale-105 glow-purple">
            <div className="flex items-center gap-4">
              <div className="w-14 h-14 bg-gradient-to-br from-purple-500/30 to-purple-600/30 rounded-xl flex items-center justify-center border border-purple-500/30 glow-purple">
                <Diamond className="h-7 w-7 text-purple-400" />
              </div>
              <div>
                <p className="text-white font-semibold text-lg">AI Generated</p>
                <p className="text-3xl font-bold text-purple-400">100%</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Premium About User Flow Section */}
      <div className="bg-slate-900/40 backdrop-blur-xl border border-slate-700/30 rounded-2xl overflow-hidden glow-blue">
        <div className="p-8">
          <div className="flex items-center gap-4 mb-6">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/30 to-purple-600/30 flex items-center justify-center text-2xl border border-blue-500/30 glow-blue">
              🤖
            </div>
            <div>
              <h3 className="text-2xl font-bold text-white">About User Flow</h3>
              <p className="text-slate-300 mt-1">
                AI-generated insights about your application's user journey design
              </p>
            </div>
          </div>

          <div className="bg-slate-800/30 rounded-xl p-6 backdrop-blur-sm">
            {generatingExplanation ? (
              <div className="flex items-center gap-4 text-slate-300">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center glow-blue">
                  <Loader2 className="h-5 w-5 animate-spin text-white" />
                </div>
                <span className="text-lg font-medium">Generating AI-powered explanation...</span>
              </div>
            ) : aboutFlowExplanation ? (
              <div className="prose prose-slate prose-invert max-w-none">
                <p className="text-slate-300 leading-relaxed whitespace-pre-line text-lg">
                  {aboutFlowExplanation}
                </p>
              </div>
            ) : (
              <p className="text-slate-300 text-lg leading-relaxed">
                This user flow diagram represents the core user journey through your application, intelligently generated by AI based on your project requirements.
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
