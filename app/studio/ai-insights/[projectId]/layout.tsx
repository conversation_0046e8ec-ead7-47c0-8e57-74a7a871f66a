"use client"

import { useEffect, useState } from "react"
import { use<PERSON>ara<PERSON>, usePathname } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import DashboardShell from "@/components/studio/dashboard/dashboard-shell"
import { ArrowLeft, FileLineChart, Kanban, LayoutDashboard, Brain } from "lucide-react"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"

export default function ProjectLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const params = useParams()
  const pathname = usePathname()
  const projectId = params.projectId as string
  const [project, setProject] = useState<AIAnalysisResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Determine active tab from pathname
  const getActiveTab = () => {
    if (pathname.includes('/user-flow')) return 'user-flow'
    if (pathname.includes('/tickets')) return 'tickets'
    if (pathname.includes('/memory-bank')) return 'memory-bank'
    return 'overview'
  }

  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true)
        const projects = await getUserProjectsClient()
        const foundProject = projects.find(p => p.id === projectId)
        
        if (!foundProject) {
          setError('Project not found')
          return
        }
        
        setProject(foundProject)
      } catch (err) {
        console.error('Error fetching project:', err)
        setError('Failed to load project')
      } finally {
        setLoading(false)
      }
    }

    if (projectId) {
      fetchProject()
    }
  }, [projectId])

  if (loading) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardShell>
    )
  }

  if (error || !project) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-red-400 mb-4">{error || 'Project not found'}</p>
            <Button asChild>
              <Link href="/studio/ai-insights">Return to AI Insights</Link>
            </Button>
          </div>
        </div>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <div className="flex flex-col space-y-6 h-full">
        {/* Header */}
        <div className="flex flex-col gap-4 flex-shrink-0">
          <div>
            <Link
              href="/studio/ai-insights"
              className="inline-flex items-center text-sm text-slate-400 hover:text-slate-300 mb-2"
            >
              <ArrowLeft className="mr-1 h-4 w-4" />
              Back to AI Insights
            </Link>
            <h1 className="text-2xl font-bold text-white">{project.projectName}</h1>
            <p className="text-slate-400 mt-1 max-w-3xl">
              {project.projectDescription}
            </p>
          </div>

          {/* Tab Navigation */}
          <Tabs value={getActiveTab()} className="w-full">
            <TabsList className="grid grid-cols-4 glass-card border-slate-600/50">
              <TabsTrigger value="user-flow" asChild>
                <Link
                  href={`/studio/ai-insights/${projectId}/user-flow`}
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:glow-blue transition-all duration-300"
                >
                  <FileLineChart className="h-4 w-4" />
                  User Flow
                </Link>
              </TabsTrigger>
              <TabsTrigger value="tickets" asChild>
                <Link
                  href={`/studio/ai-insights/${projectId}/tickets`}
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-500 data-[state=active]:to-orange-600 data-[state=active]:text-white data-[state=active]:glow-amber transition-all duration-300"
                >
                  <Kanban className="h-4 w-4" />
                  Tickets Board
                </Link>
              </TabsTrigger>
              <TabsTrigger value="overview" asChild>
                <Link
                  href={`/studio/ai-insights/${projectId}`}
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-teal-600 data-[state=active]:text-white data-[state=active]:glow-emerald transition-all duration-300"
                >
                  <LayoutDashboard className="h-4 w-4" />
                  Overview
                </Link>
              </TabsTrigger>
              <TabsTrigger value="memory-bank" asChild>
                <Link
                  href={`/studio/ai-insights/${projectId}/memory-bank`}
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:glow-purple transition-all duration-300"
                >
                  <Brain className="h-4 w-4" />
                  Memory Bank
                </Link>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Tab Content */}
        <div className="flex-1 min-h-0 overflow-hidden">
          {children}
        </div>
      </div>
    </DashboardShell>
  )
}
