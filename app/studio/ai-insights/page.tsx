"use client"

import { useEffect, useState, useCallback } from "react"
import DashboardShell from "@/components/studio/dashboard/dashboard-shell"
import AIInsightsHeader from "@/components/studio/ai-insights/ai-insights-header"
import ProjectFilters from "@/components/studio/ai-insights/project-filters"
import ProjectsGrid from "@/components/studio/ai-insights/projects-grid"
import { AIAnalysisProvider, useAIAnalysisModal } from "@/components/studio/ai-analysis/ai-analysis-provider"
import EmptyState from "@/components/studio/ai-insights/empty-state"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { useAuth } from "@/components/providers/auth-provider"

// Client Component wrapper
function AIInsightsContent() {
  const { openModal } = useAIAnalysisModal()
  const { user, isLoading: authLoading } = useAuth()
  const [activeFilter, setActiveFilter] = useState("all")
  const [projects, setProjects] = useState<AIAnalysisResult[]>([])
  const [loading, setLoading] = useState(true)
  
  // Fetch user projects - wrapped in useCallback to prevent unnecessary re-renders
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true)
      // Only fetch projects if user is authenticated
      if (user) {
        console.log("Fetching projects for authenticated user:", user.id)
        const userProjects = await getUserProjectsClient()
        console.log(`Fetched ${userProjects.length} projects`)
        setProjects(userProjects)
      } else {
        console.log("No authenticated user, setting empty projects array")
        setProjects([])
      }
    } catch (error) {
      console.error("Failed to fetch projects:", error)
      setProjects([])
    } finally {
      setLoading(false)
    }
  }, [user])
  
  // Fetch projects when component mounts or user changes
  useEffect(() => {
    if (!authLoading) {
      fetchProjects()
    }
  }, [authLoading, fetchProjects])
  
  // Refresh projects when the page gains focus (user returns from another tab/page)
  useEffect(() => {
    const handleFocus = () => {
      console.log("Window focused, refreshing projects")
      fetchProjects()
    }
    
    window.addEventListener('focus', handleFocus)
    
    return () => {
      window.removeEventListener('focus', handleFocus)
    }
  }, [fetchProjects])
  
  // If user has no projects, show the analysis modal automatically
  useEffect(() => {
    if (!loading && !authLoading && projects.length === 0) {
      openModal()
    }
  }, [loading, authLoading, projects.length, openModal])

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter)
  }

  const handleProjectDelete = useCallback((projectId: string) => {
    setProjects(prevProjects => prevProjects.filter(p => p.id !== projectId))
  }, [])

  // Show loading state while auth is being determined
  if (authLoading || loading) {
    return (
      <DashboardShell>
        <div className="flex flex-col">
          <AIInsightsHeader />
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </div>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <div className="flex flex-col space-y-8">
        <AIInsightsHeader />
        {projects.length > 0 ? (
          <div className="space-y-6">
            <ProjectFilters onFilterChange={handleFilterChange} activeFilter={activeFilter} />
            <ProjectsGrid filter={activeFilter} projects={projects} onProjectDelete={handleProjectDelete} />
          </div>
        ) : (
          <EmptyState onCreateNew={openModal} />
        )}
      </div>
    </DashboardShell>
  )
}

// Page component
export default function AIInsightsPage() {
  return (
    <AIAnalysisProvider>
      <AIInsightsContent />
    </AIAnalysisProvider>
  )
}
