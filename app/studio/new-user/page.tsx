"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import DashboardShell from "@/components/studio/dashboard/dashboard-shell"
import WelcomeHeader from "@/components/studio/dashboard/welcome-header"
import MetricsCards from "@/components/studio/dashboard/metrics-cards"
import ProjectsGrid from "@/components/studio/dashboard/projects-grid"
import ActivityFeed from "@/components/studio/dashboard/activity-feed"
import QuickActions from "@/components/studio/dashboard/quick-actions"
import { AIAnalysisProvider } from "@/components/studio/ai-analysis/ai-analysis-provider"
import NewUserModal from "@/components/studio/onboarding/new-user-modal"

export default function NewUserDashboardPage() {
  const [showWelcomeModal, setShowWelcomeModal] = useState(true)
  const router = useRouter()

  // In a real app, you would get the user's name from authentication
  const userName = "John"

  return (
    <AIAnalysisProvider isNewUser={true}>
      <DashboardShell>
        <div className="flex flex-col gap-8">
          <WelcomeHeader name={userName} isNewUser={true} />
          <MetricsCards />
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <ProjectsGrid />
            </div>
            <div className="space-y-8">
              <QuickActions />
              <ActivityFeed />
            </div>
          </div>
        </div>
      </DashboardShell>

      <NewUserModal userName={userName} open={showWelcomeModal} onOpenChange={setShowWelcomeModal} />
    </AIAnalysisProvider>
  )
}
