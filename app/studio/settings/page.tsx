"use client";
import { useState } from "react";
import { useTheme } from "next-themes";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import ProfileSettings from "@/components/studio/settings/ProfileSettings"
import AccountSettings from "@/components/studio/settings/AccountSettings"
import NotificationSettings from "@/components/studio/settings/NotificationSettings"
import BillingSettings from "@/components/studio/settings/BillingSettings"
import APIKeysSettings from "@/components/studio/settings/APIKeysSettings"
import DataExport from "@/components/studio/settings/DataExport"

const languages = [
  { code: "en", label: "English" },
  { code: "es", label: "Spanish" },
  { code: "fr", label: "French" },
];

export default function SettingsPage() {
  const { theme, setTheme } = useTheme();
  const [notifications, setNotifications] = useState(true);
  const [name, setName] = useState("User Name");
  const [email, setEmail] = useState("<EMAIL>");
  const [language, setLanguage] = useState("en");
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [passwordChanged, setPasswordChanged] = useState(false);
  const [accountDeleted, setAccountDeleted] = useState(false);

  function handleSaveAll() {
    setSaving(true);
    setTimeout(() => {
      setSaving(false);
      setSaved(true);
      setTimeout(() => setSaved(false), 2000);
    }, 1000);
  }

  function handleDeleteAccount() {
    setShowDelete(false);
    setAccountDeleted(true);
    setTimeout(() => setAccountDeleted(false), 2000);
  }

  function handleChangePassword() {
    setShowPasswordModal(false);
    setPasswordChanged(true);
    setTimeout(() => setPasswordChanged(false), 2000);
  }

  return (
    <div className="max-w-3xl mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8 text-white">Settings</h1>
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="data-export">Data Export</TabsTrigger>
        </TabsList>
        <TabsContent value="profile">
          <ProfileSettings />
        </TabsContent>
        <TabsContent value="account">
          <AccountSettings />
        </TabsContent>
        <TabsContent value="notifications">
          <NotificationSettings />
        </TabsContent>
        <TabsContent value="billing">
          <BillingSettings />
        </TabsContent>
        <TabsContent value="api-keys">
          <APIKeysSettings />
        </TabsContent>
        <TabsContent value="data-export">
          <DataExport />
        </TabsContent>
      </Tabs>
    </div>
  )
} 