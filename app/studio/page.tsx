"use client"

import { useState, useEffect } from "react"
import DashboardShell from "@/components/studio/dashboard/dashboard-shell"
import WelcomeHeader from "@/components/studio/dashboard/welcome-header"
import MetricsCards from "@/components/studio/dashboard/metrics-cards"
import ProjectsGrid from "@/components/studio/dashboard/projects-grid"
import ActivityFeed from "@/components/studio/dashboard/activity-feed"
import QuickActions from "@/components/studio/dashboard/quick-actions"
import { AIAnalysisProvider } from "@/components/studio/ai-analysis/ai-analysis-provider"
import { useAuth } from "@/components/providers/auth-provider"
import { Loader2 } from "lucide-react"
import {
  getDashboardMetrics,
  getDashboardProjects,
  getDashboardActivity,
  type DashboardMetrics,
  type DashboardProject,
  type ActivityItem
} from "@/lib/services/dashboard-service"

export default function DashboardPage() {
  const { user } = useAuth()
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [projects, setProjects] = useState<DashboardProject[]>([])
  const [activity, setActivity] = useState<ActivityItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const loadDashboardData = async () => {
    if (!user?.id) {
      console.log('No user ID found, skipping dashboard data load')
      setIsLoading(false)
      return
    }

    console.log('Loading dashboard data for user:', user.id)
    setIsLoading(true)
    try {
      const [metricsData, projectsData, activityData] = await Promise.all([
        getDashboardMetrics(user.id),
        getDashboardProjects(user.id),
        getDashboardActivity(user.id)
      ])

      console.log('Dashboard data loaded:', {
        metrics: metricsData,
        projectsCount: projectsData.length,
        activitiesCount: activityData.length
      })

      setMetrics(metricsData)
      setProjects(projectsData)
      setActivity(activityData)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadDashboardData()
  }, [user?.id])

  // Add a function to refresh dashboard data
  const refreshDashboard = () => {
    console.log('Refreshing dashboard data...')
    loadDashboardData()
  }

  // Add window focus listener to refresh data when user returns to dashboard
  useEffect(() => {
    const handleFocus = () => {
      console.log('Window focused, refreshing dashboard...')
      refreshDashboard()
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [user?.id])

  if (isLoading) {
    return (
      <AIAnalysisProvider>
        <DashboardShell>
          <div className="flex items-center justify-center h-full">
            <div className="flex items-center gap-2 text-slate-400">
              <Loader2 className="h-5 w-5 animate-spin" />
              Loading dashboard...
            </div>
          </div>
        </DashboardShell>
      </AIAnalysisProvider>
    )
  }

  if (!user) {
    return (
      <AIAnalysisProvider>
        <DashboardShell>
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-slate-400">
              <p className="text-lg font-medium mb-2">Please sign in to access your dashboard</p>
              <p className="text-sm">You need to be authenticated to view your projects and metrics.</p>
            </div>
          </div>
        </DashboardShell>
      </AIAnalysisProvider>
    )
  }

  return (
    <AIAnalysisProvider>
      <DashboardShell>
        <div className="flex flex-col space-y-8 min-h-full">
          <WelcomeHeader />
          <MetricsCards metrics={metrics} />

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 flex-1">
            <div className="xl:col-span-2 space-y-8">
              <ProjectsGrid projects={projects} onRefresh={refreshDashboard} />
            </div>
            <div className="space-y-6">
              <QuickActions projects={projects.slice(0, 2)} />
              <ActivityFeed activities={activity} />
            </div>
          </div>

          {/* Bottom spacing for better visual balance */}
          <div className="h-8"></div>
        </div>
      </DashboardShell>
    </AIAnalysisProvider>
  )
}
