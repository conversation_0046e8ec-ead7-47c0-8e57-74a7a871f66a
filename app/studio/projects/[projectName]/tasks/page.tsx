"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"
import { generateTasksWithGemini, generateTasksExplanationWithGemini } from "@/lib/services/gemini-service"
import { saveProjectTasks, getProjectTasks } from "@/lib/services/supabase-service"
import DashboardShell from "@/components/studio/dashboard/dashboard-shell"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, CheckCircle2, CircleDashed, FileLineChart, RefreshCw } from "lucide-react"
import Link from "next/link"

interface Task {
  id: string
  title: string
  description: string
  status: "todo" | "in_progress" | "done"
  priority: "high" | "medium" | "low"
  category: string
  estimatedHours?: number
  dependencies?: string[]
}

export default function ProjectTasksPage() {
  const params = useParams()
  const projectName = decodeURIComponent(params.projectName as string)
  const [project, setProject] = useState<AIAnalysisResult | null>(null)
  const [tasks, setTasks] = useState<Task[]>([])
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([])
  const [activeFilter, setActiveFilter] = useState<string>("all")
  const [loading, setLoading] = useState(true)
  const [tasksLoading, setTasksLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [tasksExplanation, setTasksExplanation] = useState<string>("")

  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true)
        const projects = await getUserProjectsClient()
        const foundProject = projects.find(p => p.projectName === projectName)

        if (foundProject) {
          setProject(foundProject)
          // Check for existing tasks first, then generate if needed
          await loadOrGenerateTasksWithAI(foundProject)
        } else {
          setError(`Project "${projectName}" not found`)
        }
      } catch (err) {
        console.error("Error fetching project:", err)
        setError("Failed to load project data")
      } finally {
        setLoading(false)
      }
    }

    fetchProject()
  }, [projectName])

  // Filter tasks when tasks or activeFilter changes
  useEffect(() => {
    if (activeFilter === "all") {
      setFilteredTasks(tasks)
    } else {
      const statusMap: { [key: string]: string } = {
        "todo": "todo",
        "inprogress": "in_progress",
        "done": "done"
      }
      setFilteredTasks(tasks.filter(task => task.status === statusMap[activeFilter]))
    }
  }, [tasks, activeFilter])

  // Load existing tasks or generate new ones if none exist
  const loadOrGenerateTasksWithAI = async (project: AIAnalysisResult) => {
    try {
      setTasksLoading(true)

      // First, try to load existing tasks from storage
      console.log("Checking for existing tasks...")
      const existingData = await getProjectTasks(project.projectName)

      if (existingData.tasks && existingData.explanation) {
        console.log("Found existing tasks, loading from storage...")
        setTasks(existingData.tasks)
        setTasksExplanation(existingData.explanation)
        console.log(`Loaded ${existingData.tasks.length} existing tasks from ${existingData.generatedAt}`)
        return
      }

      // If no existing tasks, generate new ones
      console.log("No existing tasks found, generating new ones with AI...")
      await generateTasksWithAI(project, true) // true = save to storage

    } catch (err) {
      console.error("Error loading or generating tasks:", err)
      // Fallback to generating tasks without storage
      await generateTasksWithAI(project, false)
    } finally {
      setTasksLoading(false)
    }
  }

  const generateTasksWithAI = async (project: AIAnalysisResult, saveToStorage: boolean = false) => {
    try {
      setTasksLoading(true)
      const aiTasks = await generateTasksWithGemini({
        name: project.projectName,
        description: project.projectDescription,
        features: project.coreFeatures,
        technicalRequirements: project.technicalRequirements
      })
      setTasks(aiTasks)

      // Generate AI explanation for the tasks
      try {
        const explanation = await generateTasksExplanationWithGemini({
          name: project.projectName,
          description: project.projectDescription,
          features: project.coreFeatures,
          technicalRequirements: project.technicalRequirements,
          generatedTasks: aiTasks
        })
        setTasksExplanation(explanation)

        // Save to storage if requested
        if (saveToStorage) {
          try {
            console.log("Saving tasks and explanation to storage...")
            await saveProjectTasks(project.projectName, aiTasks, explanation)
            console.log("Tasks and explanation saved successfully!")
          } catch (saveErr) {
            console.error("Error saving tasks to storage:", saveErr)
            // Continue without saving - not critical
          }
        }
      } catch (explanationErr) {
        console.error("Error generating tasks explanation:", explanationErr)
        // Use fallback explanation if AI explanation fails
        const fallbackExplanation = `These ${aiTasks.length} tasks have been intelligently generated by AI based on a comprehensive analysis of ${project.projectName}. The AI considered your project's core features, technical requirements, and development best practices to create a realistic roadmap. Tasks are organized across multiple categories with priority-based scheduling to optimize your development workflow. You can filter, regenerate, or customize these tasks to match your specific development approach and timeline.`
        setTasksExplanation(fallbackExplanation)

        // Save to storage if requested (with fallback explanation)
        if (saveToStorage) {
          try {
            console.log("Saving tasks and fallback explanation to storage...")
            await saveProjectTasks(project.projectName, aiTasks, fallbackExplanation)
            console.log("Tasks and fallback explanation saved successfully!")
          } catch (saveErr) {
            console.error("Error saving tasks to storage:", saveErr)
            // Continue without saving - not critical
          }
        }
      }
    } catch (err) {
      console.error("Error generating tasks with AI:", err)
      // Fallback to a simple task structure if AI fails
      const fallbackTasks: Task[] = [
        {
          id: "fallback-1",
          title: "Project Setup",
          description: "Initialize project repository and development environment",
          status: "todo",
          priority: "high",
          category: "Setup"
        },
        {
          id: "fallback-2",
          title: "Core Development",
          description: "Implement main features and functionality",
          status: "todo",
          priority: "high",
          category: "Development"
        },
        {
          id: "fallback-3",
          title: "Testing & Deployment",
          description: "Test application and deploy to production",
          status: "todo",
          priority: "medium",
          category: "Testing"
        }
      ]
      setTasks(fallbackTasks)
      setTasksExplanation("These tasks provide a basic development roadmap for your project. You can regenerate them with AI for more detailed, project-specific tasks, or add custom tasks as needed to customize your project's development plan.")
    } finally {
      setTasksLoading(false)
    }
  }

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter)
  }

  const handleRegenerateTasks = async () => {
    if (project) {
      console.log("Regenerating tasks with AI...")
      await generateTasksWithAI(project, true) // true = save to storage
    }
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "todo":
        return <Badge variant="outline" className="bg-slate-800 text-slate-300 border-slate-700">To Do</Badge>
      case "in_progress":
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">In Progress</Badge>
      case "done":
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">Done</Badge>
      default:
        return null
    }
  }
  
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">High</Badge>
      case "medium":
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">Medium</Badge>
      case "low":
        return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">Low</Badge>
      default:
        return null
    }
  }

  if (loading) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-slate-400">Loading project and generating tasks with AI...</p>
          </div>
        </div>
      </DashboardShell>
    )
  }

  if (error || !project) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-red-400 mb-4">{error || "Project not found"}</p>
            <Button asChild>
              <Link href="/studio/ai-insights">Return to AI Insights</Link>
            </Button>
          </div>
        </div>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div>
            <Link href={`/studio/projects/${encodeURIComponent(projectName)}/analysis`} className="inline-flex items-center text-sm text-slate-400 hover:text-slate-300 mb-2">
              <ArrowLeft className="mr-1 h-4 w-4" />
              Back to Analysis
            </Link>
            <h1 className="text-2xl font-bold text-white">{project.projectName} Tasks</h1>
            <p className="text-slate-400 mt-1">
              Manage and track tasks for your project
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" asChild>
              <Link href={`/studio/projects/${encodeURIComponent(projectName)}/flow`}>
                <FileLineChart className="mr-2 h-4 w-4" />
                User Flow
              </Link>
            </Button>
            <Button
              variant="outline"
              onClick={handleRegenerateTasks}
              disabled={tasksLoading}
              className="text-slate-400 hover:text-white hover:bg-slate-700"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${tasksLoading ? 'animate-spin' : ''}`} />
              Regenerate Tasks
            </Button>
            <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
              Add Task
            </Button>
          </div>
        </div>

        {/* Task filters */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={() => handleFilterChange("all")}
            className={activeFilter === "all" ? "bg-slate-800 text-white hover:bg-slate-700" : "text-slate-400 hover:text-white hover:bg-slate-700"}
          >
            All Tasks ({tasks.length})
          </Button>
          <Button
            variant="outline"
            onClick={() => handleFilterChange("todo")}
            className={activeFilter === "todo" ? "bg-slate-800 text-white hover:bg-slate-700" : "text-slate-400 hover:text-white hover:bg-slate-700"}
          >
            To Do ({tasks.filter(t => t.status === "todo").length})
          </Button>
          <Button
            variant="outline"
            onClick={() => handleFilterChange("inprogress")}
            className={activeFilter === "inprogress" ? "bg-slate-800 text-white hover:bg-slate-700" : "text-slate-400 hover:text-white hover:bg-slate-700"}
          >
            In Progress ({tasks.filter(t => t.status === "in_progress").length})
          </Button>
          <Button
            variant="outline"
            onClick={() => handleFilterChange("done")}
            className={activeFilter === "done" ? "bg-slate-800 text-white hover:bg-slate-700" : "text-slate-400 hover:text-white hover:bg-slate-700"}
          >
            Done ({tasks.filter(t => t.status === "done").length})
          </Button>
        </div>

        {/* Tasks list */}
        {tasksLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-slate-400">Generating tasks with AI...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredTasks.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-slate-400">No tasks found for the selected filter.</p>
              </div>
            ) : (
              filteredTasks.map((task) => (
                <div key={task.id} className="bg-slate-900 rounded-lg border border-slate-800 p-4 hover:bg-slate-800/50 transition-colors">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-3 mt-1">
                      {task.status === "done" ? (
                        <CheckCircle2 className="h-5 w-5 text-green-500" />
                      ) : (
                        <CircleDashed className="h-5 w-5 text-slate-500" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-wrap items-center gap-2 mb-1">
                        <h3 className="text-lg font-medium text-white">{task.title}</h3>
                        {getStatusBadge(task.status)}
                        {getPriorityBadge(task.priority)}
                        <Badge variant="outline" className="bg-slate-800 text-slate-300 border-slate-700">
                          {task.category}
                        </Badge>
                        {task.estimatedHours && (
                          <Badge variant="outline" className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                            {task.estimatedHours}h
                          </Badge>
                        )}
                      </div>
                      <p className="text-slate-400 mb-2">{task.description}</p>
                      {task.dependencies && task.dependencies.length > 0 && (
                        <div className="text-xs text-slate-500">
                          Dependencies: {task.dependencies.join(", ")}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

      </div>

      {/* About Tasks - Moved to bottom */}
      <div className="mt-8 bg-slate-900 rounded-lg border border-slate-800 p-6">
        <h2 className="text-xl font-semibold text-white mb-4">About Tasks</h2>
        {tasksExplanation ? (
          <div className="text-slate-400 whitespace-pre-line">
            {tasksExplanation}
          </div>
        ) : (
          <div className="text-slate-400">
            <p className="mb-4">
              These tasks are automatically generated using AI based on your project's core features and technical requirements.
              The AI analyzes your project description, features, and technical stack to create a comprehensive development roadmap.
            </p>
            <p className="mb-4">
              Tasks are organized by category (Setup, Design, Frontend, Backend, Testing, DevOps, Documentation) and priority to help you focus on the most important aspects of your project.
              Each task includes estimated hours and dependencies to help with project planning.
            </p>
            <p>
              You can filter tasks by status, regenerate them with AI for fresh perspectives, or add custom tasks as needed to customize your project's roadmap.
              Mark tasks as complete as you progress through your project development.
            </p>
          </div>
        )}
      </div>
    </DashboardShell>
  )
}