"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"
import DashboardShell from "@/components/studio/dashboard/dashboard-shell"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, FileLineChart, Kanban, Sparkles } from "lucide-react"
import Link from "next/link"

export default function ProjectAnalysisPage() {
  const params = useParams()
  const projectName = decodeURIComponent(params.projectName as string)
  const [project, setProject] = useState<AIAnalysisResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true)
        const projects = await getUserProjectsClient()
        const foundProject = projects.find(p => p.projectName === projectName)
        
        if (foundProject) {
          setProject(foundProject)
        } else {
          setError(`Project "${projectName}" not found`)
        }
      } catch (err) {
        console.error("Error fetching project:", err)
        setError("Failed to load project data")
      } finally {
        setLoading(false)
      }
    }
    
    fetchProject()
  }, [projectName])

  if (loading) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardShell>
    )
  }

  if (error || !project) {
    return (
      <DashboardShell>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-red-400 mb-4">{error || "Project not found"}</p>
            <Button asChild>
              <Link href="/studio/ai-insights">Return to AI Insights</Link>
            </Button>
          </div>
        </div>
      </DashboardShell>
    )
  }

  // Calculate overall score percentage
  const overallScore = project.marketFeasibility.overallScore
  const scorePercentage = Math.round(overallScore * 10)

  return (
    <DashboardShell>
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div>
            <Link href="/studio/ai-insights" className="inline-flex items-center text-sm text-slate-400 hover:text-slate-300 mb-2">
              <ArrowLeft className="mr-1 h-4 w-4" />
              Back to AI Insights
            </Link>
            <h1 className="text-2xl font-bold text-white">{project.projectName}</h1>
            <p className="text-slate-400 mt-1 max-w-3xl">
              {project.projectDescription}
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" asChild>
              <Link href={`/studio/projects/${encodeURIComponent(projectName)}/flow`}>
                <FileLineChart className="mr-2 h-4 w-4" />
                User Flow
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/studio/projects/${encodeURIComponent(projectName)}/tasks`}>
                <Kanban className="mr-2 h-4 w-4" />
                Tasks
              </Link>
            </Button>
          </div>
        </div>

        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Market Feasibility */}
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-6">
              <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                <Sparkles className="mr-2 h-5 w-5 text-blue-400" />
                Market Feasibility Analysis
              </h2>

              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-slate-300">Overall Score</span>
                  <div className="flex items-center">
                    <span className="text-lg font-semibold text-white">{overallScore.toFixed(1)}</span>
                    <span className="text-sm text-slate-400 ml-1">/10</span>
                  </div>
                </div>
                <Progress 
                  value={scorePercentage} 
                  className="h-2.5 bg-slate-700" 
                  indicatorClassName={`bg-gradient-to-r ${
                    overallScore >= 7.5 ? "from-green-500 to-emerald-500" : 
                    overallScore >= 6 ? "from-yellow-500 to-amber-500" : 
                    "from-red-500 to-rose-500"
                  }`} 
                />
              </div>

              <div className="space-y-4">
                {project.marketFeasibility.pillars.map((pillar, index) => (
                  <div key={index} className="bg-slate-800/50 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-white">{pillar.name}</span>
                      <Badge className={`${
                        pillar.score >= 8 ? "bg-green-500/20 text-green-400 border-green-500/30" : 
                        pillar.score >= 6 ? "bg-yellow-500/20 text-yellow-400 border-yellow-500/30" : 
                        "bg-red-500/20 text-red-400 border-red-500/30"
                      }`}>
                        {pillar.score}/10
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-400">{pillar.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Suggested Improvements */}
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Suggested Improvements</h2>
              <ul className="space-y-3">
                {project.suggestedImprovements.map((improvement, index) => (
                  <li key={index} className="flex items-start">
                    <div className="flex-shrink-0 h-5 w-5 rounded-full bg-blue-500/20 border border-blue-500/30 flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-xs text-blue-400 font-medium">{index + 1}</span>
                    </div>
                    <span className="text-slate-300">{improvement}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Core Features */}
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Core Features</h2>
              <div className="space-y-4">
                {project.coreFeatures.map((feature, index) => (
                  <div key={index} className="bg-slate-800/50 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-1">
                      <span className="font-medium text-white">{feature.name}</span>
                      <Badge className={`${
                        feature.priority === "high" ? "bg-green-500/20 text-green-400 border-green-500/30" : 
                        feature.priority === "medium" ? "bg-yellow-500/20 text-yellow-400 border-yellow-500/30" : 
                        "bg-blue-500/20 text-blue-400 border-blue-500/30"
                      }`}>
                        {feature.priority}
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-400">{feature.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right column */}
          <div className="space-y-6">
            {/* Technical Requirements */}
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Technical Requirements</h2>
              
              <div className="mb-5">
                <h3 className="text-sm uppercase tracking-wider text-slate-500 mb-2">Frontend</h3>
                <div className="flex flex-wrap gap-2">
                  {project.technicalRequirements.frontend.map((tech, index) => (
                    <Badge key={index} variant="outline" className="bg-slate-800 text-slate-300 border-slate-700">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div className="mb-5">
                <h3 className="text-sm uppercase tracking-wider text-slate-500 mb-2">Backend</h3>
                <div className="flex flex-wrap gap-2">
                  {project.technicalRequirements.backend.map((tech, index) => (
                    <Badge key={index} variant="outline" className="bg-slate-800 text-slate-300 border-slate-700">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div className="mb-5">
                <h3 className="text-sm uppercase tracking-wider text-slate-500 mb-2">Database</h3>
                <div className="flex flex-wrap gap-2">
                  {project.technicalRequirements.database.map((tech, index) => (
                    <Badge key={index} variant="outline" className="bg-slate-800 text-slate-300 border-slate-700">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-sm uppercase tracking-wider text-slate-500 mb-2">Infrastructure</h3>
                <div className="flex flex-wrap gap-2">
                  {project.technicalRequirements.infrastructure.map((tech, index) => (
                    <Badge key={index} variant="outline" className="bg-slate-800 text-slate-300 border-slate-700">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Pricing Model */}
            <div className="bg-slate-900 rounded-lg border border-slate-800 p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Pricing Model</h2>
              <div className="space-y-4">
                {project.pricingModel.map((tier, index) => (
                  <div 
                    key={index} 
                    className={`bg-slate-800/50 rounded-lg p-4 ${tier.recommended ? 'ring-2 ring-blue-500/50' : ''}`}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-white">{tier.name}</span>
                      <div className="text-lg font-semibold text-white">
                        ${tier.price}
                        <span className="text-xs text-slate-400 ml-1">/mo</span>
                      </div>
                    </div>
                    {tier.recommended && (
                      <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30 mb-2">
                        Recommended
                      </Badge>
                    )}
                    <ul className="space-y-1">
                      {tier.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="text-sm text-slate-400 flex items-center">
                          <div className="w-1.5 h-1.5 rounded-full bg-slate-500 mr-2"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardShell>
  )
} 