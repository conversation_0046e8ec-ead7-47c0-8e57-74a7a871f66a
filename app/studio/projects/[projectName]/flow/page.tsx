"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { getUserProjectsClient } from "@/components/studio/ai-analysis/ai-analysis-client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"
import DashboardShell from "@/components/studio/dashboard/dashboard-shell"
import FlowDiagram from "@/components/studio/flow/flow-diagram"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Download } from "lucide-react"
import Link from "next/link"

export default function ProjectFlowPage() {
  const params = useParams()
  const projectName = decodeURIComponent(params.projectName as string)
  const [project, setProject] = useState<AIAnalysisResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true)
        const projects = await getUserProjectsClient()
        const foundProject = projects.find(p => p.projectName === projectName)
        
        if (foundProject) {
          setProject(foundProject)
        } else {
          setError(`Project "${projectName}" not found`)
        }
      } catch (err) {
        console.error("Error fetching project:", err)
        setError("Failed to load project data")
      } finally {
        setLoading(false)
      }
    }
    
    fetchProject()
  }, [projectName])
  
  const handleDownload = () => {
    // Find the SVG element
    const svgElement = document.querySelector('svg')
    if (!svgElement) return
    
    try {
      // Get SVG content
      const svgData = new XMLSerializer().serializeToString(svgElement)
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' })
      const svgUrl = URL.createObjectURL(svgBlob)
      
      // Create download link
      const downloadLink = document.createElement('a')
      downloadLink.href = svgUrl
      downloadLink.download = `${projectName.replace(/\s+/g, '-').toLowerCase()}-flow.svg`
      document.body.appendChild(downloadLink)
      downloadLink.click()
      document.body.removeChild(downloadLink)
      URL.revokeObjectURL(svgUrl)
    } catch (err) {
      console.error("Error downloading SVG:", err)
      alert("Failed to download the flow diagram")
    }
  }

  return (
    <DashboardShell>
      <div className="flex flex-col h-full space-y-6">
        <div className="flex flex-col md:flex-row justify-between gap-4 flex-shrink-0">
          <div>
            <Link href={`/studio/projects/${encodeURIComponent(projectName)}/analysis`} className="inline-flex items-center text-sm text-slate-400 hover:text-slate-300 mb-2">
              <ArrowLeft className="mr-1 h-4 w-4" />
              Back to Analysis
            </Link>
            <h1 className="text-2xl font-bold text-white">{projectName} User Flow</h1>
            <p className="text-slate-400 mt-1">
              Visual representation of the user journey through your application
            </p>
          </div>
          <div className="flex items-start gap-3">
            <Button variant="outline" asChild>
              <Link href={`/studio/projects/${encodeURIComponent(projectName)}/tasks`}>
                <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect width="8" height="8" x="3" y="3" rx="1" />
                  <rect width="8" height="8" x="13" y="3" rx="1" />
                  <rect width="8" height="8" x="3" y="13" rx="1" />
                  <rect width="8" height="8" x="13" y="13" rx="1" />
                </svg>
                Tasks
              </Link>
            </Button>
            <Button variant="outline" onClick={handleDownload} disabled={loading || !project}>
              <Download className="mr-2 h-4 w-4" />
              Download SVG
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-96 bg-slate-900 rounded-lg border border-slate-800">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-96 bg-slate-900 rounded-lg border border-slate-800">
            <div className="text-center">
              <p className="text-red-400 mb-4">{error}</p>
              <Button asChild>
                <Link href="/studio/ai-insights">Return to AI Insights</Link>
              </Button>
            </div>
          </div>
        ) : project ? (
          <div className="flex-1 min-h-0">
            <FlowDiagram project={project} />
          </div>
        ) : null}
      </div>
    </DashboardShell>
  )
} 