'use client';

import dynamic from 'next/dynamic';

const AnimatedLogo = dynamic(() => import('@/components/AnimatedLogo'), {
  ssr: false,
  loading: () => (
    <div className="h-[70vh] w-full flex items-center justify-center bg-gradient-to-b from-black to-gray-900">
      <div className="text-7xl font-bold text-[#FF5733] opacity-50">
        Loading...
      </div>
    </div>
  ),
});

export default function ClientWrapper() {
  return <AnimatedLogo />;
} 