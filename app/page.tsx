'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform, useSpring, useMotionValue } from 'framer-motion';
import Hero from "@/components/hero"
import Features from "@/components/features"
import CallToAction from "@/components/call-to-action"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import Statistics from "@/components/statistics"
import EnhancedPricing from "@/components/pricing-enhanced"
import ScrollReveal from '@/components/animations/ScrollReveal';
import ParallaxSection from '@/components/animations/ParallaxSection';
import FloatingIcons from '@/components/animations/FloatingIcons';
import BackgroundGrid from '@/components/animations/BackgroundGrid';
import TechyNetworkBG from "@/components/animations/TechyNetworkBG"
import MagneticHover from '@/components/animations/MagneticHover';
import { AnimatedGradientBG } from '@/components/animations/MorphingGradient';
import SmoothTransition, { SectionDivider, FloatingActionButton } from '@/components/animations/SmoothTransition';
import { ArrowUp } from 'lucide-react';
import dynamic from 'next/dynamic';

const SplashScreen = dynamic(() => import('@/components/SplashScreen'), {
  ssr: false
});

const AnimatedLogo = dynamic(() => import('@/components/AnimatedLogo'), { ssr: false });

// Prefers-reduced-motion hook
const useReducedMotion = () => {
  const [prefersReduced, setPrefersReduced] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReduced(mediaQuery.matches);
    
    const onChange = () => setPrefersReduced(mediaQuery.matches);
    mediaQuery.addEventListener('change', onChange);
    return () => mediaQuery.removeEventListener('change', onChange);
  }, []);
  
  return prefersReduced;
};

export default function LandingPage() {
  const [showSplash, setShowSplash] = useState(true);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const prefersReducedMotion = useReducedMotion();
  const { scrollYProgress } = useScroll();
  
  // Mouse movement effect
  useEffect(() => {
    if (prefersReducedMotion) return;
    
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight,
      });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [prefersReducedMotion]);

  // Smooth animations
  const springConfig = { stiffness: 100, damping: 30, restDelta: 0.001 };
  const smoothProgress = useSpring(scrollYProgress, springConfig);

  return (
    <>
      <AnimatePresence mode="wait">
        {showSplash ? (
          <SplashScreen onComplete={() => setShowSplash(false)} />
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
            className="relative min-h-screen"
          >
            {/* Background Effects */}
            <BackgroundGrid />
            {!prefersReducedMotion && <FloatingIcons />}

            {/* Main content */}
            <main className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
              {/* Navigation with hover effects */}
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ 
                  type: "spring",
                  stiffness: 200,
                  damping: 20
                }}
                className="py-4 sm:py-6 lg:py-8"
              >
                <Navbar />
              </motion.div>

              <div className="space-y-16 sm:space-y-24 lg:space-y-32">
                {/* Hero Section */}
                <ScrollReveal>
                  <motion.div
                    className="relative"
                    whileInView={{
                      scale: [0.95, 1],
                      opacity: [0, 1]
                    }}
                    transition={{
                      duration: 0.8,
                      ease: "easeOut"
                    }}
                  >
                    <TechyNetworkBG className="absolute inset-0 z-0" />
                    <div className="relative z-10">
                      <Hero />
                    </div>
                  </motion.div>
                </ScrollReveal>

                {/* Dashboard Demo Video Section */}
                <ParallaxSection speed={0.4}>
                  <ScrollReveal>
                    <section className="relative py-16 sm:py-20 lg:py-24">
                      {/* Background gradient */}
                      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-500/5 to-transparent" />

                      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                        <div className="text-center space-y-8">
                          {/* Section header */}
                          <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, ease: "easeOut" }}
                            className="space-y-4"
                          >
                            <div className="inline-flex items-center rounded-full bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-xl border border-purple-500/30 px-4 py-2 text-sm text-purple-300">
                              <span className="mr-2">🎬</span>
                              See Our Dashboard in Action
                            </div>
                            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold tracking-tight text-white">
                              Experience the <span className="bg-gradient-to-r from-blue-400 to-purple-400 text-transparent bg-clip-text">Power</span> of SaaSifyx
                            </h2>
                            <p className="text-lg sm:text-xl text-slate-300 max-w-3xl mx-auto">
                              Watch how our AI-powered platform transforms your SaaS ideas into actionable insights and development roadmaps.
                            </p>
                          </motion.div>

                          {/* Video container */}
                          <motion.div
                            initial={{ opacity: 0, scale: 0.9 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
                            className="relative group"
                          >
                            <div className="relative rounded-2xl overflow-hidden bg-gradient-to-br from-slate-900/80 to-slate-800/80 backdrop-blur-xl border border-slate-700/50 shadow-2xl">
                              {/* Video placeholder with play button */}
                              <div className="relative aspect-video bg-gradient-to-br from-slate-800 to-slate-900 flex items-center justify-center">
                                {/* Animated background pattern */}
                                <div className="absolute inset-0 opacity-20">
                                  <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(59,130,246,0.3)_0%,transparent_70%)]" />
                                </div>

                                {/* Play button */}
                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.95 }}
                                  className="relative z-10 flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300"
                                >
                                  <svg className="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z"/>
                                  </svg>
                                </motion.button>

                                {/* Dashboard preview overlay */}
                                <div className="absolute inset-4 rounded-lg bg-slate-900/60 backdrop-blur-sm border border-slate-600/30 opacity-60 group-hover:opacity-80 transition-opacity duration-300">
                                  <div className="p-4 space-y-3">
                                    <div className="flex items-center space-x-2">
                                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                                    </div>
                                    <div className="space-y-2">
                                      <div className="h-2 bg-gradient-to-r from-blue-500/40 to-purple-500/40 rounded w-3/4"></div>
                                      <div className="h-2 bg-slate-600/40 rounded w-1/2"></div>
                                      <div className="h-2 bg-slate-600/40 rounded w-2/3"></div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Glow effect */}
                              <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                            </div>

                            {/* Floating elements around video */}
                            <motion.div
                              animate={{
                                y: [0, -10, 0],
                                rotate: [0, 2, 0]
                              }}
                              transition={{
                                duration: 4,
                                repeat: Infinity,
                                ease: "easeInOut"
                              }}
                              className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full opacity-60"
                            />
                            <motion.div
                              animate={{
                                y: [0, 10, 0],
                                rotate: [0, -2, 0]
                              }}
                              transition={{
                                duration: 3,
                                repeat: Infinity,
                                ease: "easeInOut",
                                delay: 1
                              }}
                              className="absolute -bottom-4 -left-4 w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full opacity-60"
                            />
                          </motion.div>
                        </div>
                      </div>
                    </section>
                  </ScrollReveal>
                </ParallaxSection>

                {/* Section divider */}
                <SectionDivider className="my-16" />

                {/* Statistics with glow effect */}
                <ParallaxSection speed={0.3}>
                  <ScrollReveal>
                    <motion.div
                      whileHover={{ 
                        scale: 1.02,
                        transition: { duration: 0.3 }
                      }}
                    >
                      <Statistics />
                    </motion.div>
                  </ScrollReveal>
                </ParallaxSection>

                {/* Features Grid with enhanced animations */}
                <ParallaxSection speed={0.2}>
                  <section className="relative py-16 sm:py-20 lg:py-24" id="features">
                    {/* Animated background */}
                    <AnimatedGradientBG intensity="light" />
                    <FloatingIcons className="top-0 left-0 w-full h-full z-0" />

                    <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                      {/* Section header */}
                      <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, ease: "easeOut" }}
                        className="text-center space-y-4 mb-16"
                      >
                        <div className="inline-flex items-center rounded-full bg-gradient-to-r from-emerald-500/20 to-blue-500/20 backdrop-blur-xl border border-emerald-500/30 px-4 py-2 text-sm text-emerald-300">
                          <span className="mr-2">⚡</span>
                          Powerful Features
                        </div>
                        <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold tracking-tight text-white">
                          Everything you need to <span className="bg-gradient-to-r from-emerald-400 to-blue-400 text-transparent bg-clip-text">succeed</span>
                        </h2>
                      </motion.div>

                      {/* Features grid */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
                        {['AI Analysis', 'User Flow Designer', 'Smart Kanban Board', 'Cursor AI Integration'].map((feature, i) => (
                          <ScrollReveal
                            key={feature}
                            delay={0.1 + i * 0.1}
                          >
                            <MagneticHover strength={0.1}>
                              <motion.div
                                whileHover={{
                                  scale: 1.04,
                                  rotateY: 5,
                                  rotateX: 5,
                                  boxShadow: '0 8px 40px 0 rgba(80,80,255,0.15)',
                                  transition: {
                                    type: "spring",
                                    stiffness: 300,
                                    damping: 22
                                  }
                                }}
                                style={{
                                  perspective: 1000,
                                  transformStyle: "preserve-3d"
                                }}
                              >
                                <Features
                                  feature={feature}
                                  index={i}
                                  mousePosition={mousePosition}
                                />
                              </motion.div>
                            </MagneticHover>
                          </ScrollReveal>
                        ))}
                      </div>
                    </div>
                  </section>
                </ParallaxSection>

                {/* Section divider */}
                <SectionDivider className="my-16" gradient="from-purple-500/20 via-blue-500/20 to-emerald-500/20" />

                {/* Enhanced Pricing Section */}
                <ParallaxSection speed={0.3}>
                  <ScrollReveal>
                    <EnhancedPricing />
                  </ScrollReveal>
                </ParallaxSection>

                {/* About Section */}
                <section id="about" className="max-w-3xl mx-auto px-4 sm:px-8 lg:px-16 py-24 text-center font-sans">
                  <h2 className="text-3xl font-display font-bold mb-6 gradient-text">About SassifyX</h2>
                  <p className="text-lg text-slate-300 mb-4 font-sans">
                    SassifyX is an AI-powered platform designed to help entrepreneurs and developers validate, build, and launch successful SaaS products. Our mission is to make SaaS innovation accessible to everyone, with powerful tools, actionable insights, and a supportive community.
                  </p>
                  <p className="text-slate-400 font-sans">
                    Built by passionate engineers and designers, SassifyX leverages the latest in AI and cloud technology to empower your SaaS journey from idea to launch and beyond.
                  </p>
                </section>

                {/* CTA Section with ripple effect */}
                <ParallaxSection speed={0.2}>
                  <ScrollReveal>
                    <motion.div
                      whileInView={{
                        scale: [0.95, 1],
                        opacity: [0, 1],
                      }}
                      transition={{ duration: 0.5 }}
                      className="relative overflow-hidden"
                    >
                      <CallToAction />
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"
                        initial={{ scale: 0, opacity: 0 }}
                        whileInView={{
                          scale: [1, 1.5],
                          opacity: [0.5, 0],
                        }}
                        transition={{
                          duration: 1,
                          ease: "easeOut",
                        }}
                      />
                    </motion.div>
                  </ScrollReveal>
                </ParallaxSection>

                <ScrollReveal>
                  <Footer />
                </ScrollReveal>
              </div>
            </main>

            {/* Floating Action Button */}
            <FloatingActionButton
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              icon={<ArrowUp className="w-6 h-6 text-white" />}
              label="Back to top"
              position="bottom-right"
            />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
