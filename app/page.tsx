'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform, useSpring, useMotionValue } from 'framer-motion';
import Hero from "@/components/hero"
import Features from "@/components/features"
import CallToAction from "@/components/call-to-action"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import Statistics from "@/components/statistics"
import ScrollReveal from '@/components/animations/ScrollReveal';
import ParallaxSection from '@/components/animations/ParallaxSection';
import FloatingIcons from '@/components/animations/FloatingIcons';
import BackgroundGrid from '@/components/animations/BackgroundGrid';
import TechyNetworkBG from "@/components/animations/TechyNetworkBG"
import dynamic from 'next/dynamic';

const SplashScreen = dynamic(() => import('@/components/SplashScreen'), {
  ssr: false
});

const AnimatedLogo = dynamic(() => import('@/components/AnimatedLogo'), { ssr: false });

// Prefers-reduced-motion hook
const useReducedMotion = () => {
  const [prefersReduced, setPrefersReduced] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReduced(mediaQuery.matches);
    
    const onChange = () => setPrefersReduced(mediaQuery.matches);
    mediaQuery.addEventListener('change', onChange);
    return () => mediaQuery.removeEventListener('change', onChange);
  }, []);
  
  return prefersReduced;
};

export default function LandingPage() {
  const [showSplash, setShowSplash] = useState(true);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const prefersReducedMotion = useReducedMotion();
  const { scrollYProgress } = useScroll();
  
  // Mouse movement effect
  useEffect(() => {
    if (prefersReducedMotion) return;
    
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight,
      });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [prefersReducedMotion]);

  // Smooth animations
  const springConfig = { stiffness: 100, damping: 30, restDelta: 0.001 };
  const smoothProgress = useSpring(scrollYProgress, springConfig);

  return (
    <>
      <AnimatePresence mode="wait">
        {showSplash ? (
          <SplashScreen onComplete={() => setShowSplash(false)} />
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
            className="relative min-h-screen"
          >
            {/* Background Effects */}
            <BackgroundGrid />
            {!prefersReducedMotion && <FloatingIcons />}

            {/* Main content */}
            <main className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
              {/* Navigation with hover effects */}
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ 
                  type: "spring",
                  stiffness: 200,
                  damping: 20
                }}
                className="py-4 sm:py-6 lg:py-8"
              >
                <Navbar />
              </motion.div>

              <div className="space-y-16 sm:space-y-24 lg:space-y-32">
                {/* Hero Section */}
                <ScrollReveal>
                  <motion.div
                    className="relative"
                    whileInView={{ 
                      scale: [0.95, 1],
                      opacity: [0, 1]
                    }}
                    transition={{ 
                      duration: 0.8,
                      ease: "easeOut"
                    }}
                  >
                    <TechyNetworkBG className="absolute inset-0 z-0" />
                    <div className="relative z-10">
                      <Hero />
                    </div>
                  </motion.div>
                </ScrollReveal>

                {/* Statistics with glow effect */}
                <ParallaxSection speed={0.3}>
                  <ScrollReveal>
                    <motion.div
                      whileHover={{ 
                        scale: 1.02,
                        transition: { duration: 0.3 }
                      }}
                    >
                      <Statistics />
                    </motion.div>
                  </ScrollReveal>
                </ParallaxSection>

                {/* Features Grid */}
                <ParallaxSection speed={0.2}>
                  <div className="relative" id="features">
                    {/* Floating icons behind cards */}
                    <FloatingIcons className="top-0 left-0 w-full h-full z-0" />
                    <div className="relative z-10 max-w-5xl mx-auto px-4 sm:px-8 lg:px-16 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 justify-center">
                      {['AI Analysis', 'User Flow Designer', 'Smart Kanban Board', 'Cursor AI Integration'].map((feature, i) => (
                        <ScrollReveal 
                          key={feature}
                          delay={0.1 + i * 0.1}
                        >
                          <motion.div
                            whileHover={{ 
                              scale: 1.04,
                              boxShadow: '0 4px 32px 0 rgba(80,80,255,0.10)',
                              transition: { 
                                type: "spring",
                                stiffness: 300,
                                damping: 22
                              }
                            }}
                            style={{
                              perspective: 1000,
                            }}
                          >
                            <Features 
                              feature={feature} 
                              index={i} 
                              mousePosition={mousePosition}
                            />
                          </motion.div>
                        </ScrollReveal>
                      ))}
                    </div>
                  </div>
                </ParallaxSection>

                {/* Pricing Section */}
                <section id="pricing" className="max-w-4xl mx-auto px-4 sm:px-8 lg:px-16 py-24">
                  <h2 className="text-3xl font-bold text-center mb-8 gradient-text">Pricing</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div className="rounded-2xl bg-slate-900/70 p-8 shadow-lg border border-slate-800 text-center">
                      <h3 className="text-xl font-semibold mb-2">Starter</h3>
                      <div className="text-4xl font-bold mb-4">$0</div>
                      <ul className="text-slate-300 space-y-2 mb-6">
                        <li>Basic AI Analysis</li>
                        <li>Community Support</li>
                        <li>Limited Projects</li>
                      </ul>
                      <button className="w-full py-2 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-500 transition">Get Started</button>
                    </div>
                    <div className="rounded-2xl bg-gradient-to-br from-blue-700/80 to-purple-700/80 p-8 shadow-xl border-2 border-blue-500 text-center scale-105">
                      <h3 className="text-xl font-semibold mb-2 text-white">Pro</h3>
                      <div className="text-4xl font-bold mb-4 text-white">$29<span className="text-lg font-normal">/mo</span></div>
                      <ul className="text-white space-y-2 mb-6">
                        <li>All Starter Features</li>
                        <li>Unlimited Projects</li>
                        <li>Premium AI Tools</li>
                        <li>Email Support</li>
                      </ul>
                      <button className="w-full py-2 rounded-lg bg-white text-blue-700 font-semibold hover:bg-blue-100 transition">Start Pro</button>
                    </div>
                    <div className="rounded-2xl bg-slate-900/70 p-8 shadow-lg border border-slate-800 text-center">
                      <h3 className="text-xl font-semibold mb-2">Enterprise</h3>
                      <div className="text-4xl font-bold mb-4">Custom</div>
                      <ul className="text-slate-300 space-y-2 mb-6">
                        <li>All Pro Features</li>
                        <li>Custom Integrations</li>
                        <li>Dedicated Support</li>
                      </ul>
                      <button className="w-full py-2 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-500 transition">Contact Sales</button>
                    </div>
                  </div>
                </section>

                {/* About Section */}
                <section id="about" className="max-w-3xl mx-auto px-4 sm:px-8 lg:px-16 py-24 text-center font-sans">
                  <h2 className="text-3xl font-display font-bold mb-6 gradient-text">About SassifyX</h2>
                  <p className="text-lg text-slate-300 mb-4 font-sans">
                    SassifyX is an AI-powered platform designed to help entrepreneurs and developers validate, build, and launch successful SaaS products. Our mission is to make SaaS innovation accessible to everyone, with powerful tools, actionable insights, and a supportive community.
                  </p>
                  <p className="text-slate-400 font-sans">
                    Built by passionate engineers and designers, SassifyX leverages the latest in AI and cloud technology to empower your SaaS journey from idea to launch and beyond.
                  </p>
                </section>

                {/* CTA Section with ripple effect */}
                <ParallaxSection speed={0.2}>
                  <ScrollReveal>
                    <motion.div
                      whileInView={{
                        scale: [0.95, 1],
                        opacity: [0, 1],
                      }}
                      transition={{ duration: 0.5 }}
                      className="relative overflow-hidden"
                    >
                      <CallToAction />
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"
                        initial={{ scale: 0, opacity: 0 }}
                        whileInView={{
                          scale: [1, 1.5],
                          opacity: [0.5, 0],
                        }}
                        transition={{
                          duration: 1,
                          ease: "easeOut",
                        }}
                      />
                    </motion.div>
                  </ScrollReveal>
                </ParallaxSection>

                <ScrollReveal>
                  <Footer />
                </ScrollReveal>
              </div>
            </main>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
