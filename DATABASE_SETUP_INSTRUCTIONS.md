# Database Setup Instructions for User Management

Since the automated script couldn't execute SQL via the REST API, please follow these manual steps to set up the database:

## Step 1: Access Supabase Dashboard

1. Go to [https://supabase.com](https://supabase.com)
2. Sign in to your account
3. Navigate to your project: `svnoufgtafdxrlynhztr`
4. Go to the **SQL Editor** in the left sidebar

## Step 2: Execute the Database Setup SQL

Copy and paste the entire content of `scripts/complete-database-setup.sql` into the SQL Editor and click **Run**.

Alternatively, you can copy this SQL directly:

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Step 1: Create users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  name TEXT,
  avatar_url TEXT,
  -- User management fields
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'pending', 'inactive')),
  role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
  last_login TIMESTAMP WITH TIME ZONE,
  email_verified BOOLEAN DEFAULT FALSE,
  phone TEXT,
  company TEXT,
  job_title TEXT,
  bio TEXT,
  location TEXT,
  website TEXT,
  social_links JSONB DEFAULT '{}',
  preferences JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}'
);

-- Step 2: Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  project_name TEXT NOT NULL,
  project_description TEXT NOT NULL,
  market_feasibility JSONB NOT NULL,
  suggested_improvements JSONB NOT NULL,
  core_features JSONB NOT NULL,
  technical_requirements JSONB NOT NULL,
  pricing_model JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  progress INTEGER DEFAULT 0,
  -- AI-generated content storage
  generated_tasks JSONB,
  tasks_explanation TEXT,
  tasks_generated_at TIMESTAMPTZ,
  -- User flow data
  user_flow_data JSONB,
  user_flow_generated_at TIMESTAMPTZ,
  -- Memory bank data
  memory_bank_data JSONB,
  memory_bank_generated_at TIMESTAMPTZ,
  -- Overview data
  overview_data JSONB,
  overview_generated_at TIMESTAMPTZ
);

-- Step 3: Create tasks table for kanban board
CREATE TABLE IF NOT EXISTS public.tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'done')),
  priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  category TEXT,
  estimated_hours INTEGER,
  dependencies JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Step 4: Create user activity logs table
CREATE TABLE IF NOT EXISTS public.user_activity_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  resource_type TEXT,
  resource_id UUID,
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 5: Create user sessions table
CREATE TABLE IF NOT EXISTS public.user_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  session_token TEXT UNIQUE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Step 3: Create Indexes (Run this separately)

```sql
-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON public.users(status);
CREATE INDEX IF NOT EXISTS idx_users_role ON public.users(role);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON public.users(last_login);

CREATE INDEX IF NOT EXISTS idx_projects_user_id ON public.projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON public.projects(created_at);

CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON public.tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON public.tasks(created_at);

CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON public.user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_action ON public.user_activity_logs(action);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_created_at ON public.user_activity_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON public.user_sessions(expires_at);
```

## Step 4: Enable Row Level Security (Run this separately)

```sql
-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
```

## Step 5: Create RLS Policies (Run this separately)

```sql
-- RLS policies for users table
CREATE POLICY users_select ON public.users
  FOR SELECT USING (
    auth.uid() = id OR 
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'moderator')
    )
  );

CREATE POLICY users_update ON public.users
  FOR UPDATE USING (
    auth.uid() = id OR 
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

CREATE POLICY users_insert ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS policies for projects table
CREATE POLICY projects_select ON public.projects
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY projects_insert ON public.projects
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY projects_update ON public.projects
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY projects_delete ON public.projects
  FOR DELETE USING (user_id = auth.uid());
```

## Step 6: Create Helper Functions (Run this separately)

```sql
-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_projects_updated_at
BEFORE UPDATE ON public.projects
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_tasks_updated_at
BEFORE UPDATE ON public.tasks
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
```

## Step 7: Create User Management Functions (Run this separately)

```sql
-- Function to log user activity
CREATE OR REPLACE FUNCTION public.log_user_activity(
  p_user_id UUID,
  p_action TEXT,
  p_resource_type TEXT DEFAULT NULL,
  p_resource_id UUID DEFAULT NULL,
  p_details JSONB DEFAULT '{}',
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO public.user_activity_logs (
    user_id, action, resource_type, resource_id, details, ip_address, user_agent
  ) VALUES (
    p_user_id, p_action, p_resource_type, p_resource_id, p_details, p_ip_address, p_user_agent
  ) RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.log_user_activity TO authenticated;
```

## Step 8: Create User Statistics View (Run this separately)

```sql
-- Create user statistics view
CREATE OR REPLACE VIEW public.user_management_stats AS
SELECT 
  COUNT(*) AS total_users,
  COUNT(CASE WHEN status = 'active' THEN 1 END) AS active_users,
  COUNT(CASE WHEN status = 'suspended' THEN 1 END) AS suspended_users,
  COUNT(CASE WHEN status = 'pending' THEN 1 END) AS pending_users,
  COUNT(CASE WHEN status = 'inactive' THEN 1 END) AS inactive_users,
  COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) AS new_users_today,
  COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) AS new_users_this_week,
  COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) AS new_users_this_month,
  COUNT(CASE WHEN role = 'admin' THEN 1 END) AS admin_users,
  COUNT(CASE WHEN role = 'moderator' THEN 1 END) AS moderator_users,
  COUNT(CASE WHEN role = 'user' THEN 1 END) AS regular_users
FROM public.users;

-- Grant permissions on view
GRANT SELECT ON public.user_management_stats TO authenticated;
```

## Step 9: Test the Setup

After running all the SQL commands, you should be able to:

1. **Access the application**: http://localhost:3000
2. **Register a new account** or **sign in**
3. **Access the user management page**: http://localhost:3000/studio/users

## Step 10: Create Test Data (Optional)

To test the user management functionality, you can insert some test users:

```sql
-- Insert test users (replace with actual auth.uid() values from registered users)
INSERT INTO public.users (id, email, name, status, role, email_verified, company, location) VALUES
  ('********-0000-0000-0000-********0001', '<EMAIL>', 'Admin User', 'active', 'admin', true, 'SaaS Ideas Inc.', 'San Francisco, CA'),
  ('********-0000-0000-0000-********0002', '<EMAIL>', 'John Doe', 'active', 'user', true, 'Tech Corp', 'New York, NY'),
  ('********-0000-0000-0000-************', '<EMAIL>', 'Jane Smith', 'pending', 'user', false, 'Startup LLC', 'Austin, TX');
```

## Troubleshooting

If you encounter any issues:

1. **Check the Supabase logs** in the dashboard
2. **Verify all tables were created** in the Table Editor
3. **Ensure RLS policies are active** in the Authentication > Policies section
4. **Check that the environment variables** in `.env.local` are correct

## Next Steps

Once the database is set up:

1. **Register a new account** in the application
2. **Navigate to User Management** at `/studio/users`
3. **Test the user management features**:
   - View user list
   - Search and filter users
   - View user details
   - Update user information
   - Change user status and roles

The user management system is now fully functional with both frontend and backend implementation!
